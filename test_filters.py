#!/usr/bin/env python3
"""
Тестовый скрипт для проверки автоматического обновления фильтров
"""
import streamlit as st
import pandas as pd
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.components.active_filters_panel import ActiveFiltersPanel

st.set_page_config(
    page_title="Тест фильтров",
    page_icon="🧪",
    layout="wide"
)

st.title("🧪 Тест автоматического обновления фильтров")

# Создаем тестовые данные
@st.cache_data
def get_test_data():
    return pd.DataFrame({
        'status': ['active', 'completed', 'active', 'suspended', 'active'],
        'contract_type': ['construction', 'services', 'construction', 'supply', 'services'],
        'total_amount': [1000000, 2000000, 500000, 3000000, 1500000],
        'contract_name': ['Договор 1', 'Договор 2', 'Договор 3', 'Договор 4', 'Договор 5']
    })

# Получаем данные
df = get_test_data()

def _convert_amount_filter_to_range(amount_filter: str) -> tuple:
    """Преобразует текстовый фильтр суммы в числовые значения"""
    if amount_filter == 'До 1 млн':
        return (0, 1_000_000)
    elif amount_filter == '1-2 млн':
        return (1_000_000, 2_000_000)
    elif amount_filter == 'Свыше 2 млн':
        return (2_000_000, float('inf'))
    else:  # 'Все суммы'
        return (0, float('inf'))

st.markdown("## 📊 Тестовые фильтры")

# Создаем фильтры
col1, col2, col3 = st.columns(3)

with col1:
    selected_statuses = st.multiselect(
        "Статус",
        options=['active', 'completed', 'suspended'],
        default=['active', 'completed', 'suspended'],
        key="test_status"
    )

with col2:
    selected_types = st.multiselect(
        "Тип договора",
        options=['construction', 'services', 'supply'],
        default=['construction', 'services', 'supply'],
        key="test_type"
    )

with col3:
    amount_filter = st.selectbox(
        "Диапазон сумм",
        options=['Все суммы', 'До 1 млн', '1-2 млн', 'Свыше 2 млн'],
        key="test_amount"
    )

# Создаем фильтры в формате ActiveFiltersPanel
filters = {
    'status': selected_statuses,
    'contract_type': selected_types,
    'amount_filter': amount_filter,
    'amount_range': _convert_amount_filter_to_range(amount_filter),
    'start_date': None,
    'end_date': None,
    'show_charts': True
}

# Отображаем панель активных фильтров
st.markdown("## 🔍 Панель активных фильтров")
st.markdown("*Эта панель должна появляться автоматически при изменении фильтров*")

available_statuses = ['active', 'completed', 'suspended']
available_types = ['construction', 'services', 'supply']

# Создаем и отрисовываем панель
active_filters_panel = ActiveFiltersPanel()
reset_clicked = active_filters_panel.render(filters, available_statuses, available_types)

if reset_clicked:
    st.success("🔄 Фильтры сброшены!")
    st.rerun()

# Применяем отступ для основного контента
active_filters_panel.apply_main_content_padding()

# Показываем отфильтрованные данные
st.markdown("## 📋 Отфильтрованные данные")

filtered_df = df.copy()

# Применяем фильтры
if selected_statuses:
    filtered_df = filtered_df[filtered_df['status'].isin(selected_statuses)]

if selected_types:
    filtered_df = filtered_df[filtered_df['contract_type'].isin(selected_types)]

# Применяем фильтр по сумме
if amount_filter == 'До 1 млн':
    filtered_df = filtered_df[filtered_df['total_amount'] < 1000000]
elif amount_filter == '1-2 млн':
    filtered_df = filtered_df[(filtered_df['total_amount'] >= 1000000) & (filtered_df['total_amount'] < 2000000)]
elif amount_filter == 'Свыше 2 млн':
    filtered_df = filtered_df[filtered_df['total_amount'] >= 2000000]

st.dataframe(filtered_df, use_container_width=True)

# Тестируем разные форматы фильтров
st.markdown("## 🧪 Тест разных форматов фильтров")

col_test1, col_test2 = st.columns(2)

with col_test1:
    st.markdown("### Формат TopFiltersComponent")
    filters_format1 = {
        'status': selected_statuses,
        'contract_type': selected_types,
        'amount_filter': amount_filter,
        'amount_range': _convert_amount_filter_to_range(amount_filter),
    }
    st.json(filters_format1)

with col_test2:
    st.markdown("### Формат DashboardPage")
    filters_format2 = {
        'status_filter': selected_statuses[0] if selected_statuses else 'Все',
        'type_filter': selected_types[0] if selected_types else 'Все',
        'amount_filter': amount_filter,
    }
    st.json(filters_format2)

st.markdown("---")
st.markdown("### 📝 Инструкции по тестированию")
st.markdown("""
1. **Измените любой фильтр выше** - панель активных фильтров должна появиться автоматически
2. **Панель должна показывать только активные фильтры** (не все возможные значения)
3. **Кнопка "Сбросить все фильтры"** должна сбрасывать все фильтры и скрывать панель
4. **Данные в таблице** должны обновляться автоматически при изменении фильтров

**Ожидаемое поведение:**
- ✅ Панель появляется сразу при изменении фильтров (без кнопки "Обновить")
- ✅ Панель показывает только активные фильтры
- ✅ Кнопка сброса работает корректно
- ✅ Данные обновляются автоматически
""")
