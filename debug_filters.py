#!/usr/bin/env python3
"""
Отладочный скрипт для проверки проблемы с панелью активных фильтров
"""
import streamlit as st
import pandas as pd
import sys
import os
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.components.active_filters_panel import ActiveFiltersPanel
from app.components.top_filters import TopFiltersComponent

st.set_page_config(
    page_title="🐛 Отладка фильтров",
    page_icon="🐛",
    layout="wide"
)

st.title("🐛 Отладка проблемы с панелью активных фильтров")

# Создаем тестовые данные
@st.cache_data
def get_test_data():
    return pd.DataFrame({
        'status': ['active', 'completed', 'active', 'suspended', 'active', 'cancelled'],
        'contract_type': ['construction', 'services', 'construction', 'supply', 'services', 'maintenance'],
        'total_amount': [1000000, 2000000, 500000, 3000000, 1500000, 800000],
        'contract_name': ['Договор 1', 'Договор 2', 'Договор 3', 'Договор 4', 'Договор 5', 'Договор 6']
    })

# Получаем данные
df = get_test_data()

st.markdown("## 🔍 Тестирование TopFiltersComponent")

# Создаем компонент фильтров
top_filters = TopFiltersComponent()

# Отрисовываем фильтры и получаем результат
filters = top_filters.render(df, {})

st.markdown("### 📊 Результат фильтров от TopFiltersComponent:")
st.json(filters)

# Получаем доступные значения
available_statuses = sorted(list(df['status'].unique()))
available_types = sorted(list(df['contract_type'].unique()))

st.markdown("### 📋 Доступные значения:")
col1, col2 = st.columns(2)
with col1:
    st.write("**Статусы:**", available_statuses)
with col2:
    st.write("**Типы:**", available_types)

st.markdown("---")
st.markdown("## 🔍 Тестирование ActiveFiltersPanel")

# Создаем панель активных фильтров
active_filters_panel = ActiveFiltersPanel()

# Отрисовываем панель
st.markdown("### 🎯 Панель активных фильтров:")
reset_clicked = active_filters_panel.render(filters, available_statuses, available_types)

if reset_clicked:
    st.success("🔄 Кнопка сброса была нажата!")
    st.rerun()

# Применяем отступ для основного контента
active_filters_panel.apply_main_content_padding()

st.markdown("---")
st.markdown("## 📝 Отладочная информация")

st.markdown("### 🔍 Анализ фильтров:")

# Анализируем статусы
selected_statuses = filters.get('status', [])
st.write(f"**Выбранные статусы:** {selected_statuses}")
st.write(f"**Длина выбранных:** {len(selected_statuses)}")
st.write(f"**Длина доступных:** {len(available_statuses)}")
st.write(f"**Условие для показа панели:** {len(selected_statuses) < len(available_statuses)}")

# Анализируем типы
selected_types = filters.get('contract_type', [])
st.write(f"**Выбранные типы:** {selected_types}")
st.write(f"**Длина выбранных:** {len(selected_types)}")
st.write(f"**Длина доступных:** {len(available_types)}")
st.write(f"**Условие для показа панели:** {len(selected_types) < len(available_types)}")

# Проверяем, должна ли панель появиться
should_show_panel = (
    (selected_statuses and len(selected_statuses) < len(available_statuses)) or
    (selected_types and len(selected_types) < len(available_types))
)

if should_show_panel:
    st.success("✅ Панель ДОЛЖНА появиться!")
else:
    st.warning("⚠️ Панель НЕ должна появляться (выбраны все элементы)")

st.markdown("---")
st.markdown("### 📝 Инструкции по тестированию")
st.markdown("""
**Шаги для воспроизведения проблемы:**

1. **Убедитесь, что изначально выбраны ВСЕ статусы и типы** (панель не должна показываться)
2. **Снимите выбор с одного статуса** - панель должна появиться СРАЗУ
3. **Снимите выбор с одного типа** - панель должна появиться СРАЗУ
4. **Проверьте логи в консоли** - должны быть сообщения о нормализации фильтров

**Ожидаемое поведение:**
- ✅ При снятии выбора с любого элемента панель появляется мгновенно
- ✅ В логах видны сообщения о добавлении тегов фильтров
- ✅ Панель показывает только активные (неполные) фильтры

**Если панель не появляется:**
- 🔍 Проверьте логи в консоли браузера и терминале
- 🔍 Убедитесь, что условие `len(selected) < len(available)` выполняется
- 🔍 Проверьте, что методы нормализации возвращают правильные значения
""")

st.markdown("---")
st.markdown("### 🔧 Текущие настройки Streamlit")
st.write("**Session State Keys:**", list(st.session_state.keys()))

# Показываем ключи, связанные с фильтрами
filter_keys = {k: v for k, v in st.session_state.items() if 'filter' in k.lower() or 'top_' in k}
if filter_keys:
    st.write("**Ключи фильтров в Session State:**")
    st.json(filter_keys)
