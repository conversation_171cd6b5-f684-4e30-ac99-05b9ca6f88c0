/*
Модульные стили форм
Использует методологию BEM и CSS переменные
*/

/* ========================================
   БАЗОВЫЕ СТИЛИ ФОРМ
   ======================================== */

/* Базовый блок формы */
.form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* Группа полей формы */
.form__group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form__group--inline {
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-md);
}

.form__group--grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

/* Метка поля */
.form__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
}

.form__label--required::after {
  content: ' *';
  color: var(--color-danger);
}

/* Описание поля */
.form__description {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  line-height: var(--line-height-normal);
  margin-top: var(--spacing-xs);
}

/* Сообщение об ошибке */
.form__error {
  font-size: var(--font-size-xs);
  color: var(--color-danger);
  line-height: var(--line-height-normal);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form__error::before {
  content: '⚠';
  font-size: var(--font-size-sm);
}

/* Сообщение об успехе */
.form__success {
  font-size: var(--font-size-xs);
  color: var(--color-success);
  line-height: var(--line-height-normal);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form__success::before {
  content: '✓';
  font-size: var(--font-size-sm);
}

/* ========================================
   ПОЛЯ ВВОДА
   ======================================== */

/* Базовое поле ввода */
.form__input {
  /* Размеры */
  width: 100%;
  min-height: 2.5rem;
  padding: var(--spacing-sm) var(--spacing-md);
  
  /* Типографика */
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  
  /* Внешний вид */
  background: var(--color-background-secondary);
  border: var(--border-width-1) solid var(--color-border-light);
  border-radius: var(--border-radius-md);
  color: var(--color-text-primary);
  
  /* Переходы */
  transition: var(--transition-colors);
  
  /* Убираем стандартные стили */
  outline: none;
  appearance: none;
}

.form__input::placeholder {
  color: var(--color-text-muted);
}

.form__input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form__input:disabled {
  background: var(--color-background-muted);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

/* Состояния полей */
.form__input--error {
  border-color: var(--color-danger);
}

.form__input--error:focus {
  border-color: var(--color-danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form__input--success {
  border-color: var(--color-success);
}

.form__input--success:focus {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Размеры полей */
.form__input--sm {
  min-height: 2rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.form__input--lg {
  min-height: 3rem;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

/* ========================================
   СПЕЦИАЛЬНЫЕ ПОЛЯ
   ======================================== */

/* Текстовая область */
.form__textarea {
  min-height: 6rem;
  resize: vertical;
  font-family: var(--font-family-sans);
}

/* Поле выбора */
.form__select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--spacing-2xl);
}

/* Поле файла */
.form__file {
  padding: var(--spacing-md);
  border: 2px dashed var(--color-border-light);
  border-radius: var(--border-radius-md);
  text-align: center;
  cursor: pointer;
  transition: var(--transition-colors);
}

.form__file:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-lightest);
}

.form__file input[type="file"] {
  display: none;
}

/* ========================================
   ЧЕКБОКСЫ И РАДИОКНОПКИ
   ======================================== */

/* Контейнер для чекбокса/радио */
.form__check {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.form__check input[type="checkbox"],
.form__check input[type="radio"] {
  /* Скрываем стандартный элемент */
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Кастомный чекбокс */
.form__check-box {
  width: 1.25rem;
  height: 1.25rem;
  border: var(--border-width-2) solid var(--color-border-light);
  border-radius: var(--border-radius-sm);
  background: var(--color-background-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-colors);
  flex-shrink: 0;
  margin-top: 0.125rem; /* Выравнивание с текстом */
}

.form__check input[type="checkbox"]:checked + .form__check-box {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.form__check input[type="checkbox"]:checked + .form__check-box::after {
  content: '✓';
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

/* Кастомная радиокнопка */
.form__radio {
  width: 1.25rem;
  height: 1.25rem;
  border: var(--border-width-2) solid var(--color-border-light);
  border-radius: var(--border-radius-full);
  background: var(--color-background-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-colors);
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.form__check input[type="radio"]:checked + .form__radio {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.form__check input[type="radio"]:checked + .form__radio::after {
  content: '';
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: var(--border-radius-full);
}

/* Метка чекбокса/радио */
.form__check-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  cursor: pointer;
}

/* ========================================
   ПЕРЕКЛЮЧАТЕЛИ
   ======================================== */

/* Контейнер переключателя */
.form__switch {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.form__switch input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Трек переключателя */
.form__switch-track {
  width: 3rem;
  height: 1.5rem;
  background: var(--color-gray-300);
  border-radius: var(--border-radius-full);
  position: relative;
  transition: var(--transition-colors);
}

.form__switch input[type="checkbox"]:checked + .form__switch-track {
  background: var(--color-primary);
}

/* Ручка переключателя */
.form__switch-track::after {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background: white;
  border-radius: var(--border-radius-full);
  transition: var(--transition-transform);
  box-shadow: var(--shadow-sm);
}

.form__switch input[type="checkbox"]:checked + .form__switch-track::after {
  transform: translateX(1.5rem);
}

/* ========================================
   ГРУППЫ ПОЛЕЙ
   ======================================== */

/* Группа радиокнопок */
.form__radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form__radio-group--inline {
  flex-direction: row;
  gap: var(--spacing-lg);
}

/* Группа чекбоксов */
.form__checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form__checkbox-group--inline {
  flex-direction: row;
  gap: var(--spacing-lg);
}

/* ========================================
   STREAMLIT ИНТЕГРАЦИЯ
   ======================================== */

/* Стили для Streamlit компонентов */
.stSelectbox > div > div {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-1) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-md) !important;
  color: var(--color-text-primary) !important;
}

.stSelectbox > div > div:focus-within {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.stTextInput > div > div > input {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-1) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-md) !important;
  color: var(--color-text-primary) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
}

.stTextInput > div > div > input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.stTextArea > div > div > textarea {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-1) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-md) !important;
  color: var(--color-text-primary) !important;
  font-family: var(--font-family-sans) !important;
}

.stCheckbox > label > div {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-2) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-sm) !important;
}

.stCheckbox > label > div[data-checked="true"] {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.stRadio > div {
  background: var(--color-background-secondary) !important;
  border-radius: var(--border-radius-md) !important;
  padding: var(--spacing-sm) !important;
}

/* ========================================
   АДАПТИВНОСТЬ
   ======================================== */

@media (max-width: 768px) {
  .form__group--inline {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form__group--grid {
    grid-template-columns: 1fr;
  }
  
  .form__radio-group--inline,
  .form__checkbox-group--inline {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .form__input--lg {
    min-height: 2.75rem;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .form {
    gap: var(--spacing-md);
  }
  
  .form__group {
    gap: var(--spacing-xs);
  }
  
  .form__input {
    min-height: 2.25rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
  
  .form__textarea {
    min-height: 5rem;
  }
}
