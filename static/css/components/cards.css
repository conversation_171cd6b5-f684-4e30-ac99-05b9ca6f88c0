/*
Модульные стили карточек
Использует методологию BEM и CSS переменные
*/

/* ========================================
   БАЗОВЫЕ СТИЛИ КАРТОЧЕК
   ======================================== */

/* Базовый блок карточки */
.card {
  /* Позиционирование */
  position: relative;
  display: flex;
  flex-direction: column;
  
  /* Внешний вид */
  background: var(--color-background-secondary);
  border: var(--border-width-1) solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  
  /* Отступы */
  margin-bottom: var(--spacing-lg);
  
  /* Переходы */
  transition: var(--transition-all);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* ========================================
   ЭЛЕМЕНТЫ КАРТОЧКИ (BEM)
   ======================================== */

/* Заголовок карточки */
.card__header {
  padding: var(--spacing-lg);
  border-bottom: var(--border-width-1) solid var(--color-border-light);
  background: var(--color-background-muted);
}

.card__title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.card__subtitle {
  margin: var(--spacing-xs) 0 0 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* Тело карточки */
.card__body {
  padding: var(--spacing-lg);
  flex: 1;
}

.card__content {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.card__content > *:last-child {
  margin-bottom: 0;
}

/* Подвал карточки */
.card__footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: var(--border-width-1) solid var(--color-border-light);
  background: var(--color-background-muted);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Изображение карточки */
.card__image {
  width: 100%;
  height: auto;
  display: block;
}

.card__image--cover {
  object-fit: cover;
  height: 200px;
}

/* ========================================
   МОДИФИКАТОРЫ КАРТОЧЕК
   ======================================== */

/* Карточка с градиентным заголовком */
.card--gradient-header .card__header {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  border-bottom: none;
}

.card--gradient-header .card__title {
  color: var(--color-text-inverse);
}

.card--gradient-header .card__subtitle {
  color: rgba(255, 255, 255, 0.9);
}

/* Карточка метрики */
.card--metric {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  border: none;
  position: relative;
  overflow: hidden;
}

.card--metric::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.card--metric .card__body {
  position: relative;
  z-index: 1;
}

/* Интерактивная карточка */
.card--interactive {
  cursor: pointer;
  user-select: none;
}

.card--interactive:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card--interactive:active {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Карточка без тени */
.card--flat {
  box-shadow: none;
  border: var(--border-width-2) solid var(--color-border-light);
}

.card--flat:hover {
  box-shadow: var(--shadow-sm);
  transform: none;
}

/* Карточка с рамкой */
.card--outlined {
  border: var(--border-width-2) solid var(--color-primary);
  box-shadow: none;
}

.card--outlined:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-dark);
}

/* Компактная карточка */
.card--compact .card__header {
  padding: var(--spacing-md);
}

.card--compact .card__body {
  padding: var(--spacing-md);
}

.card--compact .card__footer {
  padding: var(--spacing-sm) var(--spacing-md);
}

/* Карточка на всю высоту */
.card--full-height {
  height: 100%;
}

/* ========================================
   СПЕЦИАЛЬНЫЕ КАРТОЧКИ ДАШБОРДА
   ======================================== */

/* Карточка статистики */
.dashboard-card {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: var(--border-width-1) solid var(--color-border-light);
  transition: var(--transition-all);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Карточка метрики дашборда */
.metric-card {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-inverse);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.metric-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

/* Значение метрики */
.metric-card__value {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-none);
  margin-bottom: var(--spacing-xs);
  font-variant-numeric: tabular-nums;
  position: relative;
  z-index: 1;
}

/* Метка метрики */
.metric-card__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: var(--line-height-tight);
  position: relative;
  z-index: 1;
}

/* Изменение метрики */
.metric-card__delta {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-top: var(--spacing-xs);
  position: relative;
  z-index: 1;
}

.metric-card__delta--positive {
  color: rgba(255, 255, 255, 0.9);
}

.metric-card__delta--negative {
  color: rgba(255, 255, 255, 0.7);
}

/* ========================================
   СЕТКА КАРТОЧЕК
   ======================================== */

/* Контейнер сетки карточек */
.cards-grid {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.cards-grid--1 {
  grid-template-columns: 1fr;
}

.cards-grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.cards-grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.cards-grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

.cards-grid--auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Карточка, занимающая несколько колонок */
.card--span-2 {
  grid-column: span 2;
}

.card--span-3 {
  grid-column: span 3;
}

.card--span-4 {
  grid-column: span 4;
}

/* ========================================
   СОСТОЯНИЯ КАРТОЧЕК
   ======================================== */

/* Загружающаяся карточка */
.card--loading {
  position: relative;
  overflow: hidden;
}

.card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: card-loading 1.5s infinite;
}

@keyframes card-loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Карточка с ошибкой */
.card--error {
  border-color: var(--color-danger);
  background: rgba(239, 68, 68, 0.05);
}

.card--error .card__header {
  background: var(--color-danger);
  color: var(--color-text-inverse);
}

/* Успешная карточка */
.card--success {
  border-color: var(--color-success);
  background: rgba(59, 130, 246, 0.05);
}

.card--success .card__header {
  background: var(--color-success);
  color: var(--color-text-inverse);
}

/* ========================================
   АДАПТИВНОСТЬ
   ======================================== */

@media (max-width: 1024px) {
  .cards-grid--4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .card--span-4 {
    grid-column: span 3;
  }
}

@media (max-width: 768px) {
  .cards-grid--3,
  .cards-grid--4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card--span-2,
  .card--span-3,
  .card--span-4 {
    grid-column: span 2;
  }
  
  .card__header,
  .card__body,
  .dashboard-card,
  .metric-card {
    padding: var(--spacing-md);
  }
  
  .metric-card__value {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .cards-grid--2,
  .cards-grid--3,
  .cards-grid--4 {
    grid-template-columns: 1fr;
  }
  
  .card--span-2,
  .card--span-3,
  .card--span-4 {
    grid-column: span 1;
  }
  
  .card__header,
  .card__body,
  .dashboard-card,
  .metric-card {
    padding: var(--spacing-sm);
  }
  
  .card__footer {
    padding: var(--spacing-xs) var(--spacing-sm);
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .metric-card__value {
    font-size: var(--font-size-2xl);
  }
  
  .card__title {
    font-size: var(--font-size-lg);
  }
}
