/*
Модульные стили кнопок
Использует методологию BEM и CSS переменные
*/

/* ========================================
   БАЗОВЫЕ СТИЛИ КНОПОК
   ======================================== */

/* Базовый блок кнопки */
.btn {
  /* Позиционирование и размеры */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  /* Отступы и размеры */
  padding: var(--spacing-sm) var(--spacing-lg);
  min-height: 2.5rem;
  
  /* Типографика */
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  white-space: nowrap;
  
  /* Внешний вид */
  border: var(--border-width-1) solid transparent;
  border-radius: var(--border-radius-md);
  background: transparent;
  
  /* Интерактивность */
  cursor: pointer;
  user-select: none;
  transition: var(--transition-all);
  
  /* Фокус */
  outline: none;
}

.btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* ========================================
   МОДИФИКАТОРЫ КНОПОК (BEM)
   ======================================== */

/* Основная кнопка */
.btn--primary {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn--primary:hover:not(:disabled) {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.btn--primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Вторичная кнопка */
.btn--secondary {
  background: var(--color-background-secondary);
  color: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-xs);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn--secondary:active {
  transform: translateY(1px);
}

/* Кнопка успеха */
.btn--success {
  background: var(--gradient-success);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn--success:hover:not(:disabled) {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Кнопка предупреждения */
.btn--warning {
  background: var(--gradient-warning);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn--warning:hover:not(:disabled) {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Кнопка опасности */
.btn--danger {
  background: var(--color-danger);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-sm);
}

.btn--danger:hover:not(:disabled) {
  background: var(--color-danger);
  opacity: 0.9;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Информационная кнопка */
.btn--info {
  background: var(--gradient-info);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn--info:hover:not(:disabled) {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Кнопка-призрак */
.btn--ghost {
  background: transparent;
  color: var(--color-primary);
  border-color: transparent;
}

.btn--ghost:hover:not(:disabled) {
  background: var(--color-primary-lightest);
  color: var(--color-primary-dark);
}

/* Кнопка-ссылка */
.btn--link {
  background: transparent;
  color: var(--color-primary);
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  text-decoration: underline;
}

.btn--link:hover:not(:disabled) {
  color: var(--color-primary-dark);
  text-decoration: none;
}

/* ========================================
   РАЗМЕРЫ КНОПОК
   ======================================== */

/* Маленькая кнопка */
.btn--sm {
  padding: var(--spacing-xs) var(--spacing-md);
  min-height: 2rem;
  font-size: var(--font-size-sm);
}

/* Большая кнопка */
.btn--lg {
  padding: var(--spacing-md) var(--spacing-xl);
  min-height: 3rem;
  font-size: var(--font-size-lg);
}

/* Очень большая кнопка */
.btn--xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  min-height: 3.5rem;
  font-size: var(--font-size-xl);
}

/* Кнопка на всю ширину */
.btn--full {
  width: 100%;
}

/* Квадратная кнопка (только иконка) */
.btn--square {
  padding: var(--spacing-sm);
  width: 2.5rem;
  height: 2.5rem;
}

.btn--square.btn--sm {
  width: 2rem;
  height: 2rem;
  padding: var(--spacing-xs);
}

.btn--square.btn--lg {
  width: 3rem;
  height: 3rem;
  padding: var(--spacing-md);
}

/* Круглая кнопка */
.btn--round {
  border-radius: var(--border-radius-full);
}

/* ========================================
   ГРУППЫ КНОПОК
   ======================================== */

/* Контейнер группы кнопок */
.btn-group {
  display: inline-flex;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right: var(--border-width-1) solid rgba(255, 255, 255, 0.2);
  margin: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
  border-right: none;
}

.btn-group .btn:only-child {
  border-radius: var(--border-radius-md);
  border-right: none;
}

/* Вертикальная группа кнопок */
.btn-group--vertical {
  flex-direction: column;
}

.btn-group--vertical .btn {
  border-right: none;
  border-bottom: var(--border-width-1) solid rgba(255, 255, 255, 0.2);
}

.btn-group--vertical .btn:first-child {
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.btn-group--vertical .btn:last-child {
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
  border-bottom: none;
}

/* ========================================
   СОСТОЯНИЯ КНОПОК
   ======================================== */

/* Активная кнопка */
.btn--active {
  background: var(--color-primary-dark);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-inner);
}

/* Загружающаяся кнопка */
.btn--loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.btn--loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1rem;
  height: 1rem;
  margin: -0.5rem 0 0 -0.5rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   ИКОНКИ В КНОПКАХ
   ======================================== */

/* Кнопка с иконкой слева */
.btn__icon {
  display: inline-flex;
  align-items: center;
  margin-right: var(--spacing-xs);
  font-size: 1.125em;
}

.btn__icon--right {
  margin-right: 0;
  margin-left: var(--spacing-xs);
}

.btn__icon--only {
  margin: 0;
}

/* ========================================
   АДАПТИВНОСТЬ
   ======================================== */

@media (max-width: 768px) {
  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 2.25rem;
  }
  
  .btn--lg {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    min-height: 2.75rem;
  }
  
  .btn--xl {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-height: 3rem;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .btn-group .btn {
    border-right: none;
    border-bottom: var(--border-width-1) solid rgba(255, 255, 255, 0.2);
  }
  
  .btn-group .btn:first-child {
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  }
  
  .btn-group .btn:last-child {
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    border-bottom: none;
  }
}

@media (max-width: 480px) {
  .btn--full-mobile {
    width: 100%;
  }
  
  .btn-group {
    width: 100%;
  }
  
  .btn-group .btn {
    flex: 1;
  }
}
