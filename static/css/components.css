/*
Стили для компонентов интерфейса
*/

/* Стили для кнопок */
.stButton > button {
    border-radius: 8px;
    border: 2px solid #3b82f6;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e40af;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.stButton > button:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Стили для селектбоксов */
.stSelectbox > div > div {
    border-radius: 8px;
    border: 2px solid #3b82f6;
    background: white;
    font-weight: 500;
}

.stSelectbox label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для текстовых полей */
.stTextInput > div > div > input {
    border-radius: 8px;
    border: 2px solid #3b82f6;
    background: white;
    color: #1e40af;
    font-weight: 500;
}

.stTextInput label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для чекбоксов */
.stCheckbox label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для слайдеров */
.stSlider > div > div > div > div {
    background-color: #3b82f6;
}

.stSlider label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для радиокнопок */
.stRadio label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для мультиселекта */
.stMultiSelect label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для загрузки файлов */
.stFileUploader label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для дат */
.stDateInput label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для времени */
.stTimeInput label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для числовых полей */
.stNumberInput label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для текстовых областей */
.stTextArea label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для цветовых пикеров */
.stColorPicker label {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

/* Стили для прогресс-баров */
.stProgress > div > div > div > div {
    background-color: #3b82f6;
}

/* Стили для спиннеров */
.stSpinner > div {
    border-top-color: #3b82f6;
}

/* Стили для алертов */
.stAlert {
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

/* Стили для экспандеров */
.streamlit-expanderHeader {
    color: #1e40af !important;
    font-weight: 600 !important;
}

/* Стили для боковой панели */
.css-1d391kg {
    background-color: #f8f9fa;
}

/* Стили для контейнеров */
.element-container {
    margin-bottom: 1rem;
}

/* Стили для колонок */
.css-ocqkz7 {
    gap: 1rem;
}

/* Стили для табов */
.stTabs [data-baseweb="tab-list"] {
    gap: 2px;
}

.stTabs [data-baseweb="tab"] {
    background-color: #f8fafc;
    border-radius: 8px 8px 0 0;
    color: #1e40af;
    font-weight: 600;
}

.stTabs [aria-selected="true"] {
    background-color: #3b82f6;
    color: white;
}
