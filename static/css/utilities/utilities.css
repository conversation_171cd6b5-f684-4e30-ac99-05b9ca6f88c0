/*
Утилитарные классы для быстрого стилизования
Основаны на CSS переменных и принципах Tailwind CSS
*/

/* ========================================
   ОТСТУПЫ (MARGIN)
   ======================================== */

/* Все стороны */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }
.m-6 { margin: var(--spacing-2xl) !important; }
.m-auto { margin: auto !important; }

/* Горизонтальные отступы */
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.mx-2 { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.mx-3 { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.mx-4 { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.mx-5 { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

/* Вертикальные отступы */
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-2 { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-3 { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-4 { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-5 { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }

/* Отдельные стороны */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.ml-5 { margin-left: var(--spacing-xl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }
.mr-5 { margin-right: var(--spacing-xl) !important; }

/* ========================================
   ВНУТРЕННИЕ ОТСТУПЫ (PADDING)
   ======================================== */

/* Все стороны */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }
.p-6 { padding: var(--spacing-2xl) !important; }

/* Горизонтальные отступы */
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.px-2 { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.px-3 { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.px-4 { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.px-5 { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }

/* Вертикальные отступы */
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-2 { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-3 { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-4 { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-5 { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }

/* ========================================
   ЦВЕТА ТЕКСТА
   ======================================== */

.text-primary { color: var(--color-text-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-muted { color: var(--color-text-muted) !important; }
.text-inverse { color: var(--color-text-inverse) !important; }

.text-blue { color: var(--color-primary) !important; }
.text-blue-dark { color: var(--color-primary-dark) !important; }
.text-blue-light { color: var(--color-primary-light) !important; }

.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-danger { color: var(--color-danger) !important; }
.text-info { color: var(--color-info) !important; }

.text-white { color: var(--color-white) !important; }
.text-gray-500 { color: var(--color-gray-500) !important; }
.text-gray-600 { color: var(--color-gray-600) !important; }
.text-gray-700 { color: var(--color-gray-700) !important; }

/* ========================================
   ЦВЕТА ФОНА
   ======================================== */

.bg-transparent { background-color: transparent !important; }
.bg-white { background-color: var(--color-white) !important; }
.bg-gray-50 { background-color: var(--color-gray-50) !important; }
.bg-gray-100 { background-color: var(--color-gray-100) !important; }
.bg-gray-200 { background-color: var(--color-gray-200) !important; }

.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-danger { background-color: var(--color-danger) !important; }
.bg-info { background-color: var(--color-info) !important; }

/* Градиентные фоны */
.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-secondary { background: var(--gradient-secondary) !important; }
.bg-gradient-success { background: var(--gradient-success) !important; }
.bg-gradient-warning { background: var(--gradient-warning) !important; }
.bg-gradient-info { background: var(--gradient-info) !important; }

/* ========================================
   ГРАНИЦЫ
   ======================================== */

.border { border: var(--border-width-1) solid var(--color-border-light) !important; }
.border-0 { border: none !important; }
.border-2 { border: var(--border-width-2) solid var(--color-border-light) !important; }
.border-4 { border: var(--border-width-4) solid var(--color-border-light) !important; }

.border-t { border-top: var(--border-width-1) solid var(--color-border-light) !important; }
.border-r { border-right: var(--border-width-1) solid var(--color-border-light) !important; }
.border-b { border-bottom: var(--border-width-1) solid var(--color-border-light) !important; }
.border-l { border-left: var(--border-width-1) solid var(--color-border-light) !important; }

/* Цвета границ */
.border-primary { border-color: var(--color-primary) !important; }
.border-secondary { border-color: var(--color-secondary) !important; }
.border-success { border-color: var(--color-success) !important; }
.border-warning { border-color: var(--color-warning) !important; }
.border-danger { border-color: var(--color-danger) !important; }
.border-info { border-color: var(--color-info) !important; }

.border-light { border-color: var(--color-border-light) !important; }
.border-muted { border-color: var(--color-border-muted) !important; }

/* ========================================
   СКРУГЛЕНИЕ УГЛОВ
   ======================================== */

.rounded-none { border-radius: var(--border-radius-none) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-2xl { border-radius: var(--border-radius-2xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* Отдельные углы */
.rounded-t { border-top-left-radius: var(--border-radius-md) !important; border-top-right-radius: var(--border-radius-md) !important; }
.rounded-r { border-top-right-radius: var(--border-radius-md) !important; border-bottom-right-radius: var(--border-radius-md) !important; }
.rounded-b { border-bottom-left-radius: var(--border-radius-md) !important; border-bottom-right-radius: var(--border-radius-md) !important; }
.rounded-l { border-top-left-radius: var(--border-radius-md) !important; border-bottom-left-radius: var(--border-radius-md) !important; }

/* ========================================
   ТЕНИ
   ======================================== */

.shadow-none { box-shadow: none !important; }
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }
.shadow-inner { box-shadow: var(--shadow-inner) !important; }

/* ========================================
   РАЗМЕРЫ
   ======================================== */

/* Ширина */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-1\/2 { width: 50% !important; }
.w-1\/3 { width: 33.333333% !important; }
.w-2\/3 { width: 66.666667% !important; }
.w-1\/4 { width: 25% !important; }
.w-3\/4 { width: 75% !important; }

/* Высота */
.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-1\/2 { height: 50% !important; }
.h-1\/3 { height: 33.333333% !important; }
.h-2\/3 { height: 66.666667% !important; }
.h-1\/4 { height: 25% !important; }
.h-3\/4 { height: 75% !important; }

/* Минимальные размеры */
.min-w-0 { min-width: 0 !important; }
.min-w-full { min-width: 100% !important; }
.min-h-0 { min-height: 0 !important; }
.min-h-full { min-height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

/* Максимальные размеры */
.max-w-none { max-width: none !important; }
.max-w-full { max-width: 100% !important; }
.max-w-screen { max-width: 100vw !important; }
.max-h-full { max-height: 100% !important; }
.max-h-screen { max-height: 100vh !important; }

/* ========================================
   FLEXBOX
   ======================================== */

.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }

/* Направление */
.flex-row { flex-direction: row !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-col { flex-direction: column !important; }
.flex-col-reverse { flex-direction: column-reverse !important; }

/* Перенос */
.flex-wrap { flex-wrap: wrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

/* Выравнивание по главной оси */
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

/* Выравнивание по поперечной оси */
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.items-center { align-items: center !important; }
.items-baseline { align-items: baseline !important; }
.items-stretch { align-items: stretch !important; }

/* Выравнивание содержимого */
.content-start { align-content: flex-start !important; }
.content-end { align-content: flex-end !important; }
.content-center { align-content: center !important; }
.content-between { align-content: space-between !important; }
.content-around { align-content: space-around !important; }
.content-evenly { align-content: space-evenly !important; }

/* Flex элементы */
.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

.flex-grow { flex-grow: 1 !important; }
.flex-grow-0 { flex-grow: 0 !important; }

.flex-shrink { flex-shrink: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }

/* ========================================
   GRID
   ======================================== */

.grid { display: grid !important; }
.inline-grid { display: inline-grid !important; }

/* Колонки */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }

/* Строки */
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)) !important; }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)) !important; }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)) !important; }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)) !important; }

/* Промежутки */
.gap-0 { gap: 0 !important; }
.gap-1 { gap: var(--spacing-xs) !important; }
.gap-2 { gap: var(--spacing-sm) !important; }
.gap-3 { gap: var(--spacing-md) !important; }
.gap-4 { gap: var(--spacing-lg) !important; }
.gap-5 { gap: var(--spacing-xl) !important; }
.gap-6 { gap: var(--spacing-2xl) !important; }

.gap-x-0 { column-gap: 0 !important; }
.gap-x-1 { column-gap: var(--spacing-xs) !important; }
.gap-x-2 { column-gap: var(--spacing-sm) !important; }
.gap-x-3 { column-gap: var(--spacing-md) !important; }
.gap-x-4 { column-gap: var(--spacing-lg) !important; }

.gap-y-0 { row-gap: 0 !important; }
.gap-y-1 { row-gap: var(--spacing-xs) !important; }
.gap-y-2 { row-gap: var(--spacing-sm) !important; }
.gap-y-3 { row-gap: var(--spacing-md) !important; }
.gap-y-4 { row-gap: var(--spacing-lg) !important; }

/* Размещение элементов */
.col-span-1 { grid-column: span 1 / span 1 !important; }
.col-span-2 { grid-column: span 2 / span 2 !important; }
.col-span-3 { grid-column: span 3 / span 3 !important; }
.col-span-4 { grid-column: span 4 / span 4 !important; }
.col-span-full { grid-column: 1 / -1 !important; }

.row-span-1 { grid-row: span 1 / span 1 !important; }
.row-span-2 { grid-row: span 2 / span 2 !important; }
.row-span-3 { grid-row: span 3 / span 3 !important; }
.row-span-4 { grid-row: span 4 / span 4 !important; }
.row-span-full { grid-row: 1 / -1 !important; }

/* ========================================
   ПОЗИЦИОНИРОВАНИЕ
   ======================================== */

.static { position: static !important; }
.fixed { position: fixed !important; }
.absolute { position: absolute !important; }
.relative { position: relative !important; }
.sticky { position: sticky !important; }

/* Z-index */
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }
.z-auto { z-index: auto !important; }

/* ========================================
   ВИДИМОСТЬ
   ======================================== */

.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* ========================================
   ПЕРЕХОДЫ И АНИМАЦИИ
   ======================================== */

.transition-none { transition: none !important; }
.transition-all { transition: var(--transition-all) !important; }
.transition-colors { transition: var(--transition-colors) !important; }
.transition-opacity { transition: var(--transition-opacity) !important; }
.transition-shadow { transition: var(--transition-shadow) !important; }
.transition-transform { transition: var(--transition-transform) !important; }

/* Длительность */
.duration-75 { transition-duration: var(--duration-75) !important; }
.duration-100 { transition-duration: var(--duration-100) !important; }
.duration-150 { transition-duration: var(--duration-150) !important; }
.duration-200 { transition-duration: var(--duration-200) !important; }
.duration-300 { transition-duration: var(--duration-300) !important; }
.duration-500 { transition-duration: var(--duration-500) !important; }

/* Функции плавности */
.ease-linear { transition-timing-function: var(--ease-linear) !important; }
.ease-in { transition-timing-function: var(--ease-in) !important; }
.ease-out { transition-timing-function: var(--ease-out) !important; }
.ease-in-out { transition-timing-function: var(--ease-in-out) !important; }

/* Трансформации */
.transform { transform: translateZ(0) !important; }
.transform-none { transform: none !important; }
.translate-x-0 { transform: translateX(0) !important; }
.translate-y-0 { transform: translateY(0) !important; }
.-translate-y-1 { transform: translateY(-0.25rem) !important; }
.-translate-y-2 { transform: translateY(-0.5rem) !important; }

.scale-0 { transform: scale(0) !important; }
.scale-50 { transform: scale(0.5) !important; }
.scale-75 { transform: scale(0.75) !important; }
.scale-90 { transform: scale(0.9) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }
.scale-110 { transform: scale(1.1) !important; }
.scale-125 { transform: scale(1.25) !important; }

/* ========================================
   АДАПТИВНЫЕ УТИЛИТЫ
   ======================================== */

/* Планшеты и меньше (768px) */
@media (max-width: 768px) {
  .md\:hidden { display: none !important; }
  .md\:block { display: block !important; }
  .md\:flex { display: flex !important; }
  .md\:grid { display: grid !important; }

  .md\:flex-col { flex-direction: column !important; }
  .md\:items-center { align-items: center !important; }
  .md\:justify-center { justify-content: center !important; }

  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }

  .md\:w-full { width: 100% !important; }
  .md\:h-auto { height: auto !important; }

  .md\:p-2 { padding: var(--spacing-sm) !important; }
  .md\:p-3 { padding: var(--spacing-md) !important; }
  .md\:px-2 { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
  .md\:py-2 { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }

  .md\:m-2 { margin: var(--spacing-sm) !important; }
  .md\:mx-auto { margin-left: auto !important; margin-right: auto !important; }

  .md\:text-sm { font-size: var(--font-size-sm) !important; }
  .md\:text-base { font-size: var(--font-size-base) !important; }
  .md\:text-lg { font-size: var(--font-size-lg) !important; }
}

/* Мобильные устройства (480px) */
@media (max-width: 480px) {
  .sm\:hidden { display: none !important; }
  .sm\:block { display: block !important; }
  .sm\:flex { display: flex !important; }
  .sm\:grid { display: grid !important; }

  .sm\:flex-col { flex-direction: column !important; }
  .sm\:items-stretch { align-items: stretch !important; }
  .sm\:justify-start { justify-content: flex-start !important; }

  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }

  .sm\:w-full { width: 100% !important; }
  .sm\:h-auto { height: auto !important; }

  .sm\:p-1 { padding: var(--spacing-xs) !important; }
  .sm\:p-2 { padding: var(--spacing-sm) !important; }
  .sm\:px-1 { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
  .sm\:py-1 { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }

  .sm\:m-1 { margin: var(--spacing-xs) !important; }
  .sm\:mx-auto { margin-left: auto !important; margin-right: auto !important; }

  .sm\:text-xs { font-size: var(--font-size-xs) !important; }
  .sm\:text-sm { font-size: var(--font-size-sm) !important; }
  .sm\:text-base { font-size: var(--font-size-base) !important; }

  .sm\:rounded { border-radius: var(--border-radius-sm) !important; }
  .sm\:shadow-sm { box-shadow: var(--shadow-sm) !important; }
}
