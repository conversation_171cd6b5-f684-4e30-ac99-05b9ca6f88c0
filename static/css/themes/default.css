/*
Стандартная тема дашборда
*/

/* CSS переменные для единой синей градиентной схемы */
:root {
    --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
    --success-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    --warning-gradient: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    --info-gradient: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
    --shadow-soft: 0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
    --shadow-medium: 0 20px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
    --shadow-strong: 0 25px 50px -12px rgba(59, 130, 246, 0.3);
    --border-radius: 1rem;
    --border-radius-lg: 1.5rem;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Градиентные фоны - Единая синяя схема */
.gradient-bg-primary {
    background: var(--primary-gradient);
}

.gradient-bg-success {
    background: var(--success-gradient);
}

.gradient-bg-warning {
    background: var(--warning-gradient);
}

.gradient-bg-info {
    background: var(--info-gradient);
}

/* Дополнительные синие градиенты */
.gradient-bg-blue-light {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
}

.gradient-bg-blue-medium {
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
}

.gradient-bg-blue-dark {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1e40af 100%);
}

/* Современные карточки метрик - Синий градиент */
.modern-metric-card {
    position: relative;
    background: var(--primary-gradient);
    border: 1px solid #3b82f6;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    overflow: hidden;
    backdrop-filter: blur(10px);
    color: white;
}

.modern-metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-metric-card:hover::before {
    left: 100%;
}

.modern-metric-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
}

/* Анимации */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes progress-shine {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Прогресс-индикаторы */
.progress-animated {
    position: relative;
    overflow: hidden;
}

.progress-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progress-shine 2s infinite;
}

/* Адаптивная сетка */
.modern-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    margin: 2rem 0;
}

@media (max-width: 768px) {
    .modern-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .modern-metric-card {
        padding: 1rem;
    }
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow-soft: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
        --shadow-strong: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }
    
    .modern-metric-card {
        background: #1f2937;
        color: #f9fafb;
    }
}
