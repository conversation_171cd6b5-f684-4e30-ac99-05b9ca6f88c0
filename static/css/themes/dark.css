/*
Темная тема для дашборда
Переопределяет CSS переменные для темного режима
*/

/* ========================================
   ТЕМНАЯ ТЕМА - ПЕРЕМЕННЫЕ
   ======================================== */

[data-theme="dark"] {
  /* Основные цвета фона */
  --color-background: #0f172a;
  --color-background-secondary: #1e293b;
  --color-background-muted: #334155;
  
  /* Цвета текста */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-muted: #94a3b8;
  --color-text-inverse: #1f2937;
  
  /* Цвета границ */
  --color-border: #334155;
  --color-border-light: #475569;
  --color-border-muted: #64748b;
  
  /* Цвета карточек */
  --color-card-bg: #1e293b;
  
  /* Серые цвета для темной темы */
  --color-gray-50: #1e293b;
  --color-gray-100: #334155;
  --color-gray-200: #475569;
  --color-gray-300: #64748b;
  --color-gray-400: #94a3b8;
  --color-gray-500: #cbd5e1;
  --color-gray-600: #e2e8f0;
  --color-gray-700: #f1f5f9;
  --color-gray-800: #f8fafc;
  --color-gray-900: #ffffff;
  
  /* Тени для темной темы */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 12px 0 rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 10px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.4);
  
  /* Градиенты остаются синими, но с учетом темного фона */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #1d4ed8 100%);
  --gradient-success: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  --gradient-warning: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  --gradient-info: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
}

/* ========================================
   АВТОМАТИЧЕСКАЯ ТЕМНАЯ ТЕМА
   ======================================== */

@media (prefers-color-scheme: dark) {
  :root {
    /* Применяем темную тему автоматически */
    --color-background: #0f172a;
    --color-background-secondary: #1e293b;
    --color-background-muted: #334155;
    
    --color-text-primary: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-muted: #94a3b8;
    --color-text-inverse: #1f2937;
    
    --color-border: #334155;
    --color-border-light: #475569;
    --color-border-muted: #64748b;
    
    --color-card-bg: #1e293b;
    
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 12px 0 rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 10px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
  }
}

/* ========================================
   СПЕЦИФИЧНЫЕ СТИЛИ ТЕМНОЙ ТЕМЫ
   ======================================== */

[data-theme="dark"] .stApp {
  background: var(--color-background);
  color: var(--color-text-primary);
}

[data-theme="dark"] .main {
  background: var(--color-background);
}

[data-theme="dark"] .block-container {
  background: transparent;
}

/* Карточки в темной теме */
[data-theme="dark"] .dashboard-card,
[data-theme="dark"] .card {
  background: var(--color-card-bg);
  border-color: var(--color-border);
  color: var(--color-text-primary);
}

[data-theme="dark"] .card__header {
  background: var(--color-background-muted);
  border-color: var(--color-border);
}

[data-theme="dark"] .card__footer {
  background: var(--color-background-muted);
  border-color: var(--color-border);
}

/* Карточки метрик остаются с градиентом */
[data-theme="dark"] .metric-card {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
}

/* Формы в темной теме */
[data-theme="dark"] .form__input,
[data-theme="dark"] .form__textarea,
[data-theme="dark"] .form__select {
  background: var(--color-card-bg);
  border-color: var(--color-border);
  color: var(--color-text-primary);
}

[data-theme="dark"] .form__input::placeholder {
  color: var(--color-text-muted);
}

[data-theme="dark"] .form__label {
  color: var(--color-text-primary);
}

[data-theme="dark"] .form__description {
  color: var(--color-text-muted);
}

/* Кнопки в темной теме */
[data-theme="dark"] .btn--secondary {
  background: var(--color-card-bg);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

[data-theme="dark"] .btn--ghost {
  color: var(--color-primary);
}

[data-theme="dark"] .btn--ghost:hover:not(:disabled) {
  background: var(--color-background-muted);
  color: var(--color-primary-light);
}

/* Таблицы в темной теме */
[data-theme="dark"] table {
  color: var(--color-text-primary);
}

[data-theme="dark"] th {
  background: var(--color-background-muted);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

[data-theme="dark"] td {
  border-color: var(--color-border);
  color: var(--color-text-secondary);
}

/* Код в темной теме */
[data-theme="dark"] code {
  background: var(--color-background-muted);
  color: var(--color-primary-light);
}

[data-theme="dark"] pre {
  background: var(--color-background-muted);
  color: var(--color-text-primary);
}

/* ========================================
   STREAMLIT КОМПОНЕНТЫ В ТЕМНОЙ ТЕМЕ
   ======================================== */

[data-theme="dark"] .stSelectbox > div > div {
  background: var(--color-card-bg) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text-primary) !important;
}

[data-theme="dark"] .stTextInput > div > div > input {
  background: var(--color-card-bg) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text-primary) !important;
}

[data-theme="dark"] .stTextArea > div > div > textarea {
  background: var(--color-card-bg) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text-primary) !important;
}

[data-theme="dark"] .stCheckbox > label > div {
  background: var(--color-card-bg) !important;
  border-color: var(--color-border) !important;
}

[data-theme="dark"] .stRadio > div {
  background: var(--color-card-bg) !important;
}

[data-theme="dark"] .stSlider > div > div > div {
  background: var(--color-primary) !important;
}

/* Боковая панель в темной теме */
[data-theme="dark"] .css-1d391kg {
  background: var(--color-card-bg);
  border-color: var(--color-border);
}

/* Графики в темной теме */
[data-theme="dark"] .chart-container {
  background: var(--color-card-bg);
  border-color: var(--color-border);
}

[data-theme="dark"] .chart-title {
  color: var(--color-text-primary);
}

/* ========================================
   ПЕРЕКЛЮЧАТЕЛЬ ТЕМЫ
   ======================================== */

.theme-toggle {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: var(--z-index-fixed);
  
  background: var(--color-card-bg);
  border: var(--border-width-1) solid var(--color-border);
  border-radius: var(--border-radius-full);
  padding: var(--spacing-sm);
  
  cursor: pointer;
  transition: var(--transition-all);
  
  box-shadow: var(--shadow-md);
}

.theme-toggle:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.theme-toggle__icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--color-text-primary);
  transition: var(--transition-colors);
}

/* Иконки для переключателя темы */
[data-theme="light"] .theme-toggle__icon--dark {
  display: block;
}

[data-theme="light"] .theme-toggle__icon--light {
  display: none;
}

[data-theme="dark"] .theme-toggle__icon--dark {
  display: none;
}

[data-theme="dark"] .theme-toggle__icon--light {
  display: block;
}

/* ========================================
   АНИМАЦИИ ДЛЯ ПЕРЕКЛЮЧЕНИЯ ТЕМЫ
   ======================================== */

* {
  transition: background-color var(--duration-300) var(--ease-smooth),
              border-color var(--duration-300) var(--ease-smooth),
              color var(--duration-300) var(--ease-smooth);
}

/* Исключения для элементов, которые не должны анимироваться */
.metric-card,
.btn--primary,
.btn--success,
.btn--warning,
.btn--danger,
.btn--info {
  transition: var(--transition-all);
}

/* ========================================
   ВЫСОКИЙ КОНТРАСТ (ДОСТУПНОСТЬ)
   ======================================== */

@media (prefers-contrast: high) {
  [data-theme="dark"] {
    --color-background: #000000;
    --color-background-secondary: #1a1a1a;
    --color-background-muted: #2d2d2d;
    
    --color-text-primary: #ffffff;
    --color-text-secondary: #e0e0e0;
    --color-text-muted: #b0b0b0;
    
    --color-border: #404040;
    --color-border-light: #606060;
    
    --color-primary: #4f9eff;
    --color-primary-dark: #3b82f6;
    --color-primary-light: #7db3ff;
  }
}

/* ========================================
   УМЕНЬШЕННАЯ АНИМАЦИЯ
   ======================================== */

@media (prefers-reduced-motion: reduce) {
  [data-theme="dark"] * {
    transition: none !important;
    animation: none !important;
  }
  
  [data-theme="dark"] .theme-toggle {
    transition: none !important;
  }
  
  [data-theme="dark"] .theme-toggle:hover {
    transform: none !important;
  }
}
