/* 
Основные стили дашборда освоения договоров
*/

/* Базовые стили приложения */
.main {
    padding: 1rem;
}

.block-container {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

/* Скрываем стандартные элементы Streamlit */
#MainMenu {
    visibility: hidden;
}

footer {
    visibility: hidden;
}

header {
    visibility: hidden;
}

/* Улучшенные стили для графиков */
.plotly-graph-div {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    border: 2px solid #3b82f6;
    background: white;
    margin: 1rem 0;
}

/* Стили для таблиц */
.dataframe {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    border: 2px solid #3b82f6;
    background: white;
}

/* Стили для метрик Streamlit */
.metric-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #3b82f6;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Улучшенные стили для заголовков */
h1, h2, h3 {
    color: #1e40af !important;
    font-weight: 700 !important;
}

/* Стили для разделителей */
hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #10b981);
    margin: 2rem 0;
}

/* Стили для секций дашборда */
.dashboard-section {
    margin: 2rem 0;
    padding: 1.5rem;
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3b82f6;
}

/* Стили для карточек метрик */
.metric-card {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    color: white;
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-label {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metric-delta {
    font-size: 0.8rem;
    opacity: 0.7;
}
