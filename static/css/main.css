/*
Главный файл модульной системы стилей
Импортирует все компоненты в правильном порядке
*/

/* ========================================
   БАЗОВЫЕ СТИЛИ (ПЕРВЫЙ СЛОЙ)
   ======================================== */

/* CSS переменные - должны загружаться первыми */
@import url('./base/variables.css');

/* CSS Reset - сбрасывает стандартные стили браузера */
@import url('./base/reset.css');

/* Типографика - базовые стили текста */
@import url('./base/typography.css');

/* ========================================
   КОМПОНЕНТНЫЕ СТИЛИ (ВТОРОЙ СЛОЙ)
   ======================================== */

/* Кнопки - интерактивные элементы */
@import url('./components/buttons.css');

/* Карточки - контейнеры контента */
@import url('./components/cards.css');

/* Формы - элементы ввода данных */
@import url('./components/forms.css');

/* ========================================
   УТИЛИТАРНЫЕ КЛАССЫ (ТРЕТИЙ СЛОЙ)
   ======================================== */

/* Утилиты - классы для быстрого стилизования */
@import url('./utilities/utilities.css');

/* ========================================
   ТЕМЫ (ЧЕТВЕРТЫЙ СЛОЙ)
   ======================================== */

/* Темная тема - переопределения для темного режима */
@import url('./themes/dark.css');

/* ========================================
   ДОПОЛНИТЕЛЬНЫЕ СТИЛИ ДАШБОРДА
   ======================================== */

/* Специфичные стили для дашборда, которые не вошли в компоненты */

/* Контейнер приложения */
.dashboard-app {
  min-height: 100vh;
  background: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
}

/* Заголовок дашборда */
.dashboard-header {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  padding: var(--spacing-lg) var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  text-align: center;
}

.dashboard-header__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dashboard-header__subtitle {
  font-size: var(--font-size-lg);
  margin: var(--spacing-sm) 0 0 0;
  opacity: 0.9;
}

/* Навигация дашборда */
.dashboard-nav {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.dashboard-nav__list {
  display: flex;
  gap: var(--spacing-md);
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}

.dashboard-nav__item {
  flex: 1;
  min-width: 120px;
}

.dashboard-nav__link {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-background-muted);
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  text-align: center;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-all);
  border: var(--border-width-1) solid transparent;
}

.dashboard-nav__link:hover {
  background: var(--color-primary-lightest);
  color: var(--color-primary-dark);
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.dashboard-nav__link--active {
  background: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary-dark);
}

/* Сетка дашборда */
.dashboard-grid {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.dashboard-grid--1 {
  grid-template-columns: 1fr;
}

.dashboard-grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.dashboard-grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.dashboard-grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

.dashboard-grid--auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Секции дашборда */
.dashboard-section {
  margin-bottom: var(--spacing-2xl);
}

.dashboard-section__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 3px solid var(--color-primary);
  position: relative;
}

.dashboard-section__title::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
}

.dashboard-section__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
}

/* Статистические блоки */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  transition: var(--transition-all);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(25px, -25px);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card__value {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-none);
  margin-bottom: var(--spacing-xs);
  position: relative;
  z-index: 1;
  font-variant-numeric: tabular-nums;
}

.stat-card__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  z-index: 1;
}

.stat-card__change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-top: var(--spacing-xs);
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-card__change--positive::before {
  content: '↗';
  color: rgba(255, 255, 255, 0.9);
}

.stat-card__change--negative::before {
  content: '↘';
  color: rgba(255, 255, 255, 0.7);
}

/* Индикаторы загрузки */
.loading-skeleton {
  background: linear-gradient(
    90deg,
    var(--color-background-muted) 25%,
    var(--color-background-secondary) 50%,
    var(--color-background-muted) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.loading-skeleton--text {
  height: 1rem;
  margin-bottom: var(--spacing-xs);
}

.loading-skeleton--title {
  height: 1.5rem;
  width: 60%;
  margin-bottom: var(--spacing-sm);
}

.loading-skeleton--card {
  height: 200px;
  margin-bottom: var(--spacing-lg);
}

/* Пустые состояния */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-muted);
}

.empty-state__icon {
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-state__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.empty-state__description {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
}

/* Уведомления и алерты */
.alert {
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: var(--border-width-1) solid transparent;
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.alert__icon {
  flex-shrink: 0;
  font-size: var(--font-size-lg);
  margin-top: 0.125rem;
}

.alert__content {
  flex: 1;
}

.alert__title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.alert__description {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.alert--info {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--color-info);
  color: var(--color-info);
}

.alert--success {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--color-success);
  color: var(--color-success);
}

.alert--warning {
  background: rgba(96, 165, 250, 0.1);
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.alert--danger {
  background: rgba(147, 197, 253, 0.1);
  border-color: var(--color-danger);
  color: var(--color-danger);
}

/* ========================================
   АДАПТИВНОСТЬ ДАШБОРДА
   ======================================== */

@media (max-width: 1024px) {
  .dashboard-grid--4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .dashboard-grid--3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--spacing-md);
  }
  
  .dashboard-header__title {
    font-size: var(--font-size-3xl);
  }
  
  .dashboard-nav__list {
    flex-direction: column;
  }
  
  .dashboard-nav__item {
    min-width: auto;
  }
  
  .dashboard-grid--2,
  .dashboard-grid--3,
  .dashboard-grid--4 {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card__value {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: var(--spacing-sm);
  }
  
  .dashboard-header__title {
    font-size: var(--font-size-2xl);
  }
  
  .dashboard-header__subtitle {
    font-size: var(--font-size-base);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: var(--spacing-md);
  }
  
  .stat-card__value {
    font-size: var(--font-size-2xl);
  }
  
  .dashboard-section__title {
    font-size: var(--font-size-xl);
  }
  
  .empty-state {
    padding: var(--spacing-lg);
  }
  
  .empty-state__icon {
    font-size: var(--font-size-4xl);
  }
}
