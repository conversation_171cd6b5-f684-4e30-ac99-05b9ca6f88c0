/* Современные стили для дашборда */

/* CSS переменные для цветовой схемы - Единая синяя градиентная схема */
:root {
    --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
    --success-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    --warning-gradient: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    --info-gradient: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
    --shadow-soft: 0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
    --shadow-medium: 0 20px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
    --shadow-strong: 0 25px 50px -12px rgba(59, 130, 246, 0.3);
    --border-radius: 1rem;
    --border-radius-lg: 1.5rem;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Анимации */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
    }
    50% {
        box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
    }
}

@keyframes progress-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Современные карточки метрик - Синий градиент */
.modern-metric-card {
    position: relative;
    background: var(--primary-gradient);
    border: 1px solid #3b82f6;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    overflow: hidden;
    backdrop-filter: blur(10px);
    color: white;
}

.modern-metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-metric-card:hover::before {
    left: 100%;
}

.modern-metric-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-strong);
}

/* Информационные панели */
.modern-info-panel {
    position: relative;
    border: 1px solid;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.modern-info-panel::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modern-info-panel:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

/* Разделители секций */
.modern-section-divider {
    margin: 2rem 0;
    animation: slideInRight 0.8s ease-out;
}

.modern-section-divider h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Прогресс индикаторы */
.modern-progress-container {
    margin: 1rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
}

.modern-progress-container:hover {
    box-shadow: var(--shadow-medium);
}

.progress-animated {
    position: relative;
    overflow: hidden;
}

.progress-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progress-shine 2s infinite;
}

/* Адаптивная сетка */
.modern-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    margin: 2rem 0;
}

@media (max-width: 768px) {
    .modern-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .modern-metric-card {
        padding: 1rem;
    }
    
    .modern-info-panel {
        padding: 1rem;
    }
}

/* Кнопки с современным дизайном */
.modern-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-bounce);
    overflow: hidden;
    cursor: pointer;
}

.modern-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-button:hover::before {
    left: 100%;
}

.modern-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-medium);
}

.modern-button:active {
    transform: translateY(0) scale(0.98);
}

/* Статусные бейджи */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.3s;
}

.status-badge:hover::before {
    left: 100%;
}

/* Карточки с глубокими тенями */
.depth-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.12),
        0 1px 2px rgba(0, 0, 0, 0.24),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.depth-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.depth-card:hover {
    box-shadow: 
        0 14px 28px rgba(0, 0, 0, 0.25),
        0 10px 10px rgba(0, 0, 0, 0.22),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    transform: translateY(-5px);
}

/* Градиентные фоны - Единая синяя схема */
.gradient-bg-primary {
    background: var(--primary-gradient);
}

.gradient-bg-success {
    background: var(--success-gradient);
}

.gradient-bg-warning {
    background: var(--warning-gradient);
}

.gradient-bg-info {
    background: var(--info-gradient);
}

/* Дополнительные синие градиенты */
.gradient-bg-blue-light {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
}

.gradient-bg-blue-medium {
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
}

.gradient-bg-blue-dark {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1e40af 100%);
}

/* Анимация появления элементов */
.fade-in-sequence > * {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.fade-in-sequence > *:nth-child(1) { animation-delay: 0.1s; }
.fade-in-sequence > *:nth-child(2) { animation-delay: 0.2s; }
.fade-in-sequence > *:nth-child(3) { animation-delay: 0.3s; }
.fade-in-sequence > *:nth-child(4) { animation-delay: 0.4s; }
.fade-in-sequence > *:nth-child(5) { animation-delay: 0.5s; }

/* Микроанимации */
.hover-lift {
    transition: var(--transition-smooth);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-scale {
    transition: var(--transition-bounce);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: var(--transition-smooth);
}

.hover-glow:hover {
    animation: pulse-glow 2s infinite;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow-soft: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
        --shadow-strong: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }
    
    .modern-metric-card,
    .modern-info-panel,
    .modern-progress-container,
    .depth-card {
        background: #1f2937;
        color: #f9fafb;
    }
}
