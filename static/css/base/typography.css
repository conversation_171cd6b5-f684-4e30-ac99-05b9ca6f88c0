/*
Типографические стили для дашборда
Оптимизированы для читаемости и синей цветовой схемы
*/

/* ========================================
   БАЗОВАЯ ТИПОГРАФИКА
   ======================================== */

/* Основные стили текста */
.text-xs {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
}

.text-sm {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.text-base {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
}

.text-lg {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
}

.text-xl {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-tight);
}

.text-2xl {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-tight);
}

.text-3xl {
  font-size: var(--font-size-3xl);
  line-height: var(--line-height-tight);
}

.text-4xl {
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-tight);
}

.text-5xl {
  font-size: var(--font-size-5xl);
  line-height: var(--line-height-none);
}

.text-6xl {
  font-size: var(--font-size-6xl);
  line-height: var(--line-height-none);
}

/* ========================================
   ВЕСА ШРИФТОВ
   ======================================== */

.font-thin {
  font-weight: var(--font-weight-thin);
}

.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.font-extrabold {
  font-weight: var(--font-weight-extrabold);
}

.font-black {
  font-weight: var(--font-weight-black);
}

/* ========================================
   ЦВЕТА ТЕКСТА - СИНЯЯ СХЕМА
   ======================================== */

.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-muted {
  color: var(--color-text-muted);
}

.text-inverse {
  color: var(--color-text-inverse);
}

/* Цвета бренда */
.text-blue {
  color: var(--color-primary);
}

.text-blue-dark {
  color: var(--color-primary-dark);
}

.text-blue-darker {
  color: var(--color-primary-darker);
}

.text-blue-light {
  color: var(--color-primary-light);
}

.text-blue-lighter {
  color: var(--color-primary-lighter);
}

/* Семантические цвета */
.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-danger {
  color: var(--color-danger);
}

.text-info {
  color: var(--color-info);
}

/* ========================================
   ВЫРАВНИВАНИЕ ТЕКСТА
   ======================================== */

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

/* ========================================
   ВЫСОТА СТРОК
   ======================================== */

.leading-none {
  line-height: var(--line-height-none);
}

.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-snug {
  line-height: var(--line-height-snug);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

.leading-loose {
  line-height: var(--line-height-loose);
}

/* ========================================
   СПЕЦИАЛЬНЫЕ ТИПОГРАФИЧЕСКИЕ КЛАССЫ
   ======================================== */

/* Заголовки дашборда */
.dashboard-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-darker);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-dark);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  border-bottom: 3px solid var(--color-primary);
  padding-bottom: var(--spacing-sm);
}

.subsection-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-sm);
}

/* Метрики и числа */
.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-inverse);
  line-height: var(--line-height-none);
  font-variant-numeric: tabular-nums;
}

.metric-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-inverse);
  line-height: var(--line-height-tight);
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-delta {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

/* Описательный текст */
.description-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
}

.help-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  line-height: var(--line-height-normal);
  font-style: italic;
}

/* Код и моноширинный текст */
.code-text {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  background-color: var(--color-background-muted);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  color: var(--color-primary-dark);
}

/* ========================================
   ЭФФЕКТЫ И АНИМАЦИИ ТЕКСТА
   ======================================== */

/* Градиентный текст */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Тень текста */
.text-shadow {
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* Анимированный текст */
.text-animated {
  transition: var(--transition-colors);
}

.text-animated:hover {
  color: var(--color-primary-light);
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* ========================================
   АДАПТИВНАЯ ТИПОГРАФИКА
   ======================================== */

@media (max-width: 768px) {
  .dashboard-title {
    font-size: var(--font-size-3xl);
  }
  
  .section-title {
    font-size: var(--font-size-xl);
  }
  
  .subsection-title {
    font-size: var(--font-size-lg);
  }
  
  .metric-value {
    font-size: var(--font-size-2xl);
  }
  
  .text-4xl {
    font-size: var(--font-size-3xl);
  }
  
  .text-3xl {
    font-size: var(--font-size-2xl);
  }
  
  .text-2xl {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .dashboard-title {
    font-size: var(--font-size-2xl);
  }
  
  .section-title {
    font-size: var(--font-size-lg);
  }
  
  .subsection-title {
    font-size: var(--font-size-base);
  }
  
  .metric-value {
    font-size: var(--font-size-xl);
  }
  
  .text-3xl {
    font-size: var(--font-size-xl);
  }
  
  .text-2xl {
    font-size: var(--font-size-lg);
  }
  
  .text-xl {
    font-size: var(--font-size-base);
  }
}

/* ========================================
   УТИЛИТЫ ДЛЯ ТЕКСТА
   ======================================== */

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.normal-case {
  text-transform: none;
}

.italic {
  font-style: italic;
}

.not-italic {
  font-style: normal;
}

.underline {
  text-decoration: underline;
}

.no-underline {
  text-decoration: none;
}

.line-through {
  text-decoration: line-through;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.subpixel-antialiased {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}
