/*
Единая система CSS переменных для дашборда
Основана на синей цветовой схеме проекта
*/

:root {
  /* ========================================
     ЦВЕТОВАЯ ПАЛИТРА - СИНЯЯ СХЕМА
     ======================================== */
  
  /* Основные цвета */
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-primary-darker: #1e40af;
  --color-primary-darkest: #1d4ed8;
  --color-primary-light: #60a5fa;
  --color-primary-lighter: #93c5fd;
  --color-primary-lightest: #dbeafe;
  
  /* Вторичные цвета */
  --color-secondary: #2563eb;
  --color-secondary-dark: #1e40af;
  --color-secondary-light: #60a5fa;
  
  /* Семантические цвета */
  --color-success: #1e40af;
  --color-success-light: #3b82f6;
  --color-warning: #60a5fa;
  --color-warning-light: #93c5fd;
  --color-danger: #93c5fd;
  --color-danger-light: #dbeafe;
  --color-info: #1d4ed8;
  --color-info-light: #2563eb;
  
  /* Нейтральные цвета */
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;
  
  /* Текстовые цвета */
  --color-text-primary: #1f2937;
  --color-text-secondary: #6b7280;
  --color-text-muted: #9ca3af;
  --color-text-inverse: #ffffff;
  
  /* Фоновые цвета */
  --color-background: #f8fafc;
  --color-background-secondary: #ffffff;
  --color-background-muted: #f1f5f9;
  --color-background-overlay: rgba(0, 0, 0, 0.5);
  
  /* Цвета границ */
  --color-border: #3b82f6;
  --color-border-light: #e2e8f0;
  --color-border-muted: #f1f5f9;
  
  /* ========================================
     ГРАДИЕНТЫ - СИНЯЯ СХЕМА
     ======================================== */
  
  /* Основные градиенты */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #1d4ed8 100%);
  --gradient-success: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  --gradient-warning: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  --gradient-info: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
  
  /* Дополнительные градиенты */
  --gradient-blue-light: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
  --gradient-blue-medium: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
  --gradient-blue-dark: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1e40af 100%);
  --gradient-blue-darker: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #1d4ed8 100%);
  
  /* Радиальные градиенты */
  --gradient-radial-primary: radial-gradient(circle, #3b82f6 0%, #1e40af 100%);
  --gradient-radial-secondary: radial-gradient(circle, #60a5fa 0%, #3b82f6 100%);
  
  /* ========================================
     РАЗМЕРЫ И ОТСТУПЫ
     ======================================== */
  
  /* Отступы */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 2.5rem;    /* 40px */
  --spacing-3xl: 3rem;      /* 48px */
  --spacing-4xl: 4rem;      /* 64px */
  
  /* Радиусы скругления */
  --border-radius-none: 0;
  --border-radius-xs: 0.25rem;   /* 4px */
  --border-radius-sm: 0.5rem;    /* 8px */
  --border-radius-md: 0.75rem;   /* 12px */
  --border-radius-lg: 1rem;      /* 16px */
  --border-radius-xl: 1.25rem;   /* 20px */
  --border-radius-2xl: 1.5rem;   /* 24px */
  --border-radius-full: 9999px;
  
  /* Ширины границ */
  --border-width-0: 0;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;
  
  /* ========================================
     ТЕНИ И ЭФФЕКТЫ
     ======================================== */
  
  /* Тени */
  --shadow-xs: 0 1px 2px 0 rgba(59, 130, 246, 0.05);
  --shadow-sm: 0 2px 4px 0 rgba(59, 130, 246, 0.1);
  --shadow-md: 0 4px 12px 0 rgba(59, 130, 246, 0.15);
  --shadow-lg: 0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(59, 130, 246, 0.3);
  --shadow-inner: inset 0 2px 4px 0 rgba(59, 130, 246, 0.1);
  
  /* Тени для темной темы */
  --shadow-dark-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark-md: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  --shadow-dark-lg: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --shadow-dark-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --shadow-dark-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  
  /* Размытие */
  --blur-none: 0;
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  
  /* ========================================
     ТИПОГРАФИКА
     ======================================== */
  
  /* Семейства шрифтов */
  --font-family-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-mono: "SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", monospace;
  
  /* Размеры шрифтов */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */
  
  /* Веса шрифтов */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Высота строк */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* ========================================
     АНИМАЦИИ И ПЕРЕХОДЫ
     ======================================== */
  
  /* Длительность анимаций */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
  
  /* Функции плавности */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Готовые переходы */
  --transition-all: all var(--duration-300) var(--ease-smooth);
  --transition-colors: color var(--duration-300) var(--ease-smooth), background-color var(--duration-300) var(--ease-smooth), border-color var(--duration-300) var(--ease-smooth);
  --transition-opacity: opacity var(--duration-300) var(--ease-smooth);
  --transition-shadow: box-shadow var(--duration-300) var(--ease-smooth);
  --transition-transform: transform var(--duration-300) var(--ease-smooth);
  
  /* ========================================
     РАЗМЕРЫ ЭКРАНОВ (BREAKPOINTS)
     ======================================== */
  
  --screen-sm: 640px;
  --screen-md: 768px;
  --screen-lg: 1024px;
  --screen-xl: 1280px;
  --screen-2xl: 1536px;
  
  /* ========================================
     Z-INDEX СЛОИ
     ======================================== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* ========================================
   ТЕМНАЯ ТЕМА
   ======================================== */

@media (prefers-color-scheme: dark) {
  :root {
    /* Переопределяем цвета для темной темы */
    --color-background: #0f172a;
    --color-background-secondary: #1e293b;
    --color-background-muted: #334155;
    
    --color-text-primary: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-muted: #94a3b8;
    
    --color-border-light: #334155;
    --color-border-muted: #475569;
    
    /* Используем темные тени */
    --shadow-sm: var(--shadow-dark-sm);
    --shadow-md: var(--shadow-dark-md);
    --shadow-lg: var(--shadow-dark-lg);
    --shadow-xl: var(--shadow-dark-xl);
    --shadow-2xl: var(--shadow-dark-2xl);
  }
}

/* ========================================
   ПОЛЬЗОВАТЕЛЬСКИЕ НАСТРОЙКИ ТЕМЫ
   ======================================== */

[data-theme="dark"] {
  --color-background: #0f172a;
  --color-background-secondary: #1e293b;
  --color-background-muted: #334155;
  
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-muted: #94a3b8;
  
  --color-border-light: #334155;
  --color-border-muted: #475569;
  
  --shadow-sm: var(--shadow-dark-sm);
  --shadow-md: var(--shadow-dark-md);
  --shadow-lg: var(--shadow-dark-lg);
  --shadow-xl: var(--shadow-dark-xl);
  --shadow-2xl: var(--shadow-dark-2xl);
}

[data-theme="light"] {
  /* Явно устанавливаем светлую тему */
  --color-background: #f8fafc;
  --color-background-secondary: #ffffff;
  --color-background-muted: #f1f5f9;
  
  --color-text-primary: #1f2937;
  --color-text-secondary: #6b7280;
  --color-text-muted: #9ca3af;
  
  --color-border-light: #e2e8f0;
  --color-border-muted: #f1f5f9;
}
