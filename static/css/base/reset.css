/*
CSS Reset и базовые стили для дашборда
Основан на современных практиках и оптимизирован для Streamlit
*/

/* ========================================
   СОВРЕМЕННЫЙ CSS RESET
   ======================================== */

/* Сброс отступов и границ для всех элементов */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Базовые стили для HTML и body */
html {
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ========================================
   STREAMLIT СПЕЦИФИЧНЫЕ СБРОСЫ
   ======================================== */

/* Скрываем стандартные элементы Streamlit */
#MainMenu {
  visibility: hidden !important;
  display: none !important;
}

footer {
  visibility: hidden !important;
  display: none !important;
}

header {
  visibility: hidden !important;
  display: none !important;
}

/* Убираем стандартные отступы Streamlit */
.main {
  padding: var(--spacing-md) !important;
}

.block-container {
  padding-top: var(--spacing-md) !important;
  padding-bottom: var(--spacing-md) !important;
  max-width: none !important;
}

/* Оптимизируем контейнеры Streamlit */
.stApp {
  background-color: var(--color-background);
}

.stApp > div {
  background-color: transparent;
}

/* ========================================
   БАЗОВЫЕ ЭЛЕМЕНТЫ
   ======================================== */

/* Заголовки */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

/* Параграфы */
p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Ссылки */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-colors);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Списки */
ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-secondary);
}

/* Изображения */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Таблицы */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-md);
}

th, td {
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: var(--border-width-1) solid var(--color-border-light);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  background-color: var(--color-background-muted);
}

/* Формы */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Кнопки */
button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Код */
code, pre {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

code {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-background-muted);
  border-radius: var(--border-radius-sm);
  color: var(--color-primary-dark);
}

pre {
  padding: var(--spacing-md);
  background-color: var(--color-background-muted);
  border-radius: var(--border-radius-md);
  overflow-x: auto;
  margin-bottom: var(--spacing-md);
}

pre code {
  padding: 0;
  background: none;
  border-radius: 0;
}

/* ========================================
   УТИЛИТАРНЫЕ КЛАССЫ
   ======================================== */

/* Скрытие элементов */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

/* Очистка float */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* Обрезка текста */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Центрирование */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* ========================================
   ФОКУС И ДОСТУПНОСТЬ
   ======================================== */

/* Улучшенные стили фокуса */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Убираем outline для мыши, оставляем для клавиатуры */
*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Стили для пользователей, предпочитающих уменьшенную анимацию */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ========================================
   ПЕЧАТЬ
   ======================================== */

@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
