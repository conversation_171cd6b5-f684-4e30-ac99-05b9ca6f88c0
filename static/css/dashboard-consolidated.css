/*
Консолидированные стили дашборда
Объединяет и оптимизирует все основные стили проекта
*/

/* ========================================
   ИМПОРТЫ БАЗОВЫХ СТИЛЕЙ
   ======================================== */

@import url('./base/variables.css');
@import url('./base/reset.css');
@import url('./base/typography.css');

/* ========================================
   ОСНОВНЫЕ СТИЛИ ПРИЛОЖЕНИЯ
   ======================================== */

/* Контейнер приложения */
.stApp {
  background: var(--color-background);
  min-height: 100vh;
}

/* Основной контейнер */
.main {
  padding: var(--spacing-lg) !important;
  background: var(--color-background);
}

.block-container {
  padding-top: var(--spacing-lg) !important;
  padding-bottom: var(--spacing-lg) !important;
  max-width: none !important;
  background: transparent;
}

/* Боковая панель */
.css-1d391kg {
  background: var(--color-background-secondary);
  border-right: var(--border-width-1) solid var(--color-border-light);
}

/* ========================================
   КАРТОЧКИ И КОНТЕЙНЕРЫ
   ======================================== */

/* Базовая карточка */
.dashboard-card {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: var(--border-width-1) solid var(--color-border-light);
  transition: var(--transition-shadow);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  transition: var(--transition-all);
}

/* Карточка метрики */
.metric-card {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-inverse);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.metric-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
  transition: var(--transition-all);
}

/* Карточка с заголовком */
.card-with-header {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.card-header {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

.card-body {
  padding: var(--spacing-lg);
}

/* ========================================
   КНОПКИ
   ======================================== */

/* Базовая кнопка */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border-radius: var(--border-radius-md);
  border: var(--border-width-1) solid transparent;
  cursor: pointer;
  transition: var(--transition-all);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Основная кнопка */
.btn-primary {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Вторичная кнопка */
.btn-secondary {
  background: var(--color-background-secondary);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-sm);
}

/* Кнопка-призрак */
.btn-ghost {
  background: transparent;
  color: var(--color-primary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-primary-lightest);
  color: var(--color-primary-dark);
}

/* Размеры кнопок */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* ========================================
   STREAMLIT КОМПОНЕНТЫ
   ======================================== */

/* Кнопки Streamlit */
.stButton > button {
  background: var(--gradient-primary) !important;
  color: var(--color-text-inverse) !important;
  border: none !important;
  border-radius: var(--border-radius-md) !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: var(--transition-all) !important;
}

.stButton > button:hover {
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-2px) !important;
}

.stButton > button:active {
  transform: translateY(0) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Селектбоксы */
.stSelectbox > div > div {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-1) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-md) !important;
}

.stSelectbox > div > div:focus-within {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Слайдеры */
.stSlider > div > div > div {
  background: var(--color-primary) !important;
}

.stSlider > div > div > div > div {
  background: var(--color-primary-dark) !important;
}

/* Чекбоксы */
.stCheckbox > label > div {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-2) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-sm) !important;
}

.stCheckbox > label > div[data-checked="true"] {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

/* Радиокнопки */
.stRadio > div {
  background: var(--color-background-secondary) !important;
  border-radius: var(--border-radius-md) !important;
  padding: var(--spacing-sm) !important;
}

/* Текстовые поля */
.stTextInput > div > div > input {
  background: var(--color-background-secondary) !important;
  border: var(--border-width-1) solid var(--color-border-light) !important;
  border-radius: var(--border-radius-md) !important;
  color: var(--color-text-primary) !important;
}

.stTextInput > div > div > input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* ========================================
   ГРАФИКИ И ДИАГРАММЫ
   ======================================== */

/* Контейнер графика */
.chart-container {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: var(--border-width-1) solid var(--color-border-light);
}

.chart-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

/* Plotly графики */
.js-plotly-plot {
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

/* ========================================
   ТАБЛИЦЫ
   ======================================== */

/* Контейнер таблицы */
.table-container {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

/* Заголовок таблицы */
.table-header {
  background: var(--gradient-primary);
  color: var(--color-text-inverse);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

/* Стили для DataFrame */
.stDataFrame {
  border-radius: var(--border-radius-md) !important;
  overflow: hidden !important;
}

.stDataFrame > div {
  border: none !important;
}

/* ========================================
   УТИЛИТАРНЫЕ КЛАССЫ
   ======================================== */

/* Отступы */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* Цвета фона */
.bg-primary { background: var(--color-primary) !important; }
.bg-secondary { background: var(--color-secondary) !important; }
.bg-white { background: var(--color-white) !important; }
.bg-gray-50 { background: var(--color-gray-50) !important; }
.bg-gray-100 { background: var(--color-gray-100) !important; }

/* Границы */
.border { border: var(--border-width-1) solid var(--color-border-light) !important; }
.border-primary { border-color: var(--color-primary) !important; }
.border-0 { border: none !important; }

.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* Тени */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-none { box-shadow: none !important; }

/* Flexbox */
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.items-center { align-items: center !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }

/* Grid */
.grid { display: grid !important; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }

.gap-1 { gap: var(--spacing-xs) !important; }
.gap-2 { gap: var(--spacing-sm) !important; }
.gap-3 { gap: var(--spacing-md) !important; }
.gap-4 { gap: var(--spacing-lg) !important; }

/* ========================================
   АДАПТИВНОСТЬ
   ======================================== */

@media (max-width: 768px) {
  .main {
    padding: var(--spacing-md) !important;
  }

  .dashboard-card,
  .metric-card {
    padding: var(--spacing-md);
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
}

@media (max-width: 480px) {
  .main {
    padding: var(--spacing-sm) !important;
  }

  .dashboard-card,
  .metric-card {
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
  }

  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
}
