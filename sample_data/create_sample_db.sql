-- Создание таблиц для дашборда освоения договоров

-- Таблица договоров
CREATE TABLE IF NOT EXISTS contracts (
    contract_id SERIAL PRIMARY KEY,
    contract_number VARCHAR(50) UNIQUE NOT NULL,
    contract_name VARCHAR(255) NOT NULL,
    contract_type VARCHAR(50) NOT NULL CHECK (contract_type IN ('construction', 'supply', 'services', 'maintenance')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'completed', 'suspended', 'cancelled')),
    total_amount DECIMAL(15,2) NOT NULL CHECK (total_amount > 0),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL CHECK (end_date > start_date),
    contractor_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица прогресса выполнения договоров
CREATE TABLE IF NOT EXISTS contract_progress (
    progress_id SERIAL PRIMARY KEY,
    contract_id INTEGER REFERENCES contracts(contract_id) ON DELETE CASCADE,
    report_date DATE NOT NULL,
    amount_planned DECIMAL(15,2) NOT NULL CHECK (amount_planned >= 0),
    amount_completed DECIMAL(15,2) NOT NULL CHECK (amount_completed >= 0),
    completion_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN amount_planned > 0 THEN (amount_completed / amount_planned * 100)
            ELSE 0 
        END
    ) STORED,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(contract_id, report_date)
);

-- Индексы для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status);
CREATE INDEX IF NOT EXISTS idx_contracts_type ON contracts(contract_type);
CREATE INDEX IF NOT EXISTS idx_contracts_dates ON contracts(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_progress_date ON contract_progress(report_date);
CREATE INDEX IF NOT EXISTS idx_progress_contract ON contract_progress(contract_id);

-- Вставка примерных данных
INSERT INTO contracts (contract_number, contract_name, contract_type, status, total_amount, start_date, end_date, contractor_name) VALUES
('Д-2024-001', 'Строительство жилого комплекса "Солнечный"', 'construction', 'active', 50000000.00, '2024-01-15', '2025-06-30', 'ООО "СтройИнвест"'),
('Д-2024-002', 'Поставка строительных материалов', 'supply', 'active', 15000000.00, '2024-02-01', '2024-12-31', 'ЗАО "МатериалСнаб"'),
('Д-2024-003', 'Ремонт дорожного покрытия ул. Центральная', 'construction', 'completed', 25000000.00, '2024-03-01', '2024-10-15', 'ООО "ДорСтрой"'),
('Д-2024-004', 'Установка систем отопления в школе №5', 'services', 'active', 8000000.00, '2024-04-01', '2024-11-30', 'ИП Иванов И.И.'),
('Д-2024-005', 'Благоустройство парка "Победы"', 'construction', 'active', 12000000.00, '2024-05-01', '2025-04-30', 'ООО "ЭкоСтрой"'),
('Д-2024-006', 'Строительство школы №12', 'construction', 'active', 75000000.00, '2024-06-01', '2025-12-31', 'ЗАО "ОбразованиеСтрой"'),
('Д-2024-007', 'Поставка медицинского оборудования', 'supply', 'completed', 30000000.00, '2024-01-10', '2024-08-15', 'ООО "МедТехника"'),
('Д-2024-008', 'Ремонт фасада административного здания', 'services', 'active', 5000000.00, '2024-07-01', '2024-12-15', 'ИП Петров П.П.'),
('Д-2024-009', 'Установка лифтов в жилом доме', 'services', 'suspended', 18000000.00, '2024-08-01', '2025-02-28', 'ООО "ЛифтСервис"'),
('Д-2024-010', 'Строительство пешеходного моста', 'construction', 'active', 45000000.00, '2024-09-01', '2025-08-31', 'ЗАО "МостСтрой"');

-- Вставка данных о прогрессе (помесячно)
INSERT INTO contract_progress (contract_id, report_date, amount_planned, amount_completed, notes) VALUES
-- Договор 1 (Жилой комплекс)
(1, '2024-01-31', 3000000.00, 2800000.00, 'Подготовительные работы завершены'),
(1, '2024-02-29', 6000000.00, 5500000.00, 'Фундамент заложен'),
(1, '2024-03-31', 9000000.00, 8200000.00, 'Возведение первого этажа'),
(1, '2024-04-30', 12000000.00, 11000000.00, 'Возведение второго этажа'),
(1, '2024-05-31', 15000000.00, 13500000.00, 'Возведение третьего этажа'),
(1, '2024-06-30', 18000000.00, 16800000.00, 'Кровельные работы'),

-- Договор 2 (Поставка материалов)
(2, '2024-02-29', 2000000.00, 2000000.00, 'Первая партия поставлена'),
(2, '2024-03-31', 4000000.00, 3800000.00, 'Вторая партия поставлена'),
(2, '2024-04-30', 6000000.00, 5900000.00, 'Третья партия поставлена'),
(2, '2024-05-31', 8000000.00, 7600000.00, 'Четвертая партия поставлена'),
(2, '2024-06-30', 10000000.00, 9800000.00, 'Пятая партия поставлена'),

-- Договор 3 (Ремонт дороги - завершен)
(3, '2024-03-31', 5000000.00, 4800000.00, 'Подготовка дорожного полотна'),
(3, '2024-04-30', 10000000.00, 9500000.00, 'Укладка первого слоя асфальта'),
(3, '2024-05-31', 15000000.00, 14200000.00, 'Укладка второго слоя'),
(3, '2024-06-30', 20000000.00, 19000000.00, 'Разметка и установка знаков'),
(3, '2024-07-31', 25000000.00, 25000000.00, 'Работы завершены'),

-- Договор 4 (Отопление в школе)
(4, '2024-04-30', 1500000.00, 1400000.00, 'Проектирование завершено'),
(4, '2024-05-31', 3000000.00, 2800000.00, 'Закупка оборудования'),
(4, '2024-06-30', 4500000.00, 4200000.00, 'Монтаж трубопроводов'),

-- Договор 5 (Благоустройство парка)
(5, '2024-05-31', 2000000.00, 1800000.00, 'Планировка территории'),
(5, '2024-06-30', 4000000.00, 3600000.00, 'Посадка деревьев и кустарников'),

-- Договор 6 (Строительство школы)
(6, '2024-06-30', 8000000.00, 7500000.00, 'Земляные работы');

-- Триггер для обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_contracts_updated_at 
    BEFORE UPDATE ON contracts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Представления для удобства работы с данными
CREATE OR REPLACE VIEW contracts_with_progress AS
SELECT 
    c.*,
    COALESCE(SUM(cp.amount_completed), 0) as total_completed,
    COALESCE(SUM(cp.amount_completed) / NULLIF(c.total_amount, 0) * 100, 0) as overall_completion_rate,
    COUNT(cp.progress_id) as progress_reports_count,
    MAX(cp.report_date) as last_report_date
FROM contracts c
LEFT JOIN contract_progress cp ON c.contract_id = cp.contract_id
GROUP BY c.contract_id;

CREATE OR REPLACE VIEW monthly_summary AS
SELECT 
    DATE_TRUNC('month', cp.report_date) as month,
    COUNT(DISTINCT cp.contract_id) as active_contracts,
    SUM(cp.amount_planned) as total_planned,
    SUM(cp.amount_completed) as total_completed,
    CASE 
        WHEN SUM(cp.amount_planned) > 0 
        THEN SUM(cp.amount_completed) / SUM(cp.amount_planned) * 100 
        ELSE 0 
    END as completion_rate
FROM contract_progress cp
GROUP BY DATE_TRUNC('month', cp.report_date)
ORDER BY month;
