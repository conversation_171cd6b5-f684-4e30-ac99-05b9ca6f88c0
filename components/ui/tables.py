"""
Унифицированные компоненты таблиц
"""
import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, List, Callable
from components.base import BaseComponent, ComponentConfig, ComponentType, ComponentUtils


class DataTable(BaseComponent):
    """Унифицированная таблица данных"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        data: pd.DataFrame,
        title: str = "",
        columns: Optional[List[str]] = None,
        formatters: Optional[Dict[str, Callable]] = None,
        page_size: int = 20,
        show_index: bool = False,
        sortable: bool = True,
        **kwargs
    ) -> None:
        """
        Отрисовывает таблицу данных
        
        Args:
            data: DataFrame с данными
            title: Заголовок таблицы
            columns: Список колонок для отображения
            formatters: Словарь форматтеров для колонок
            page_size: Количество строк на странице
            show_index: Показывать индекс
            sortable: Возможность сортировки
        """
        if not self.validate_data(data) or data.empty:
            st.warning("Нет данных для отображения таблицы")
            return
        
        # Заголовок таблицы
        if title:
            st.markdown(f"""
            <h3 style="
                color: {self.colors['text']};
                font-weight: 600;
                margin-bottom: 1rem;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid {self.colors['border']};
            ">{title}</h3>
            """, unsafe_allow_html=True)
        
        # Фильтрация колонок
        display_data = data[columns] if columns else data
        
        # Применение форматтеров
        if formatters:
            for col, formatter in formatters.items():
                if col in display_data.columns:
                    display_data[col] = display_data[col].apply(formatter)
        
        # Пагинация
        if len(display_data) > page_size:
            # Создаем пагинацию
            total_pages = (len(display_data) - 1) // page_size + 1
            
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                page = st.selectbox(
                    "Страница:",
                    range(1, total_pages + 1),
                    format_func=lambda x: f"Страница {x} из {total_pages}"
                )
            
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            display_data = display_data.iloc[start_idx:end_idx]
        
        # Отображение таблицы
        st.dataframe(
            display_data,
            use_container_width=True,
            hide_index=not show_index
        )
        
        # Информация о таблице
        st.markdown(f"""
        <div style="
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.5rem;
            text-align: center;
        ">
            Показано {len(display_data)} из {len(data)} записей
        </div>
        """, unsafe_allow_html=True)


class SummaryTable(BaseComponent):
    """Таблица сводки"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        data: Dict[str, Any],
        title: str = "Сводка",
        **kwargs
    ) -> None:
        """
        Отрисовывает таблицу сводки
        
        Args:
            data: Словарь с данными для сводки
            title: Заголовок таблицы
        """
        if not self.validate_data(data):
            return
        
        # Создаем HTML таблицу
        table_html = f"""
        <div style="
            background: white;
            {self.get_base_styles()}
            padding: 1.5rem;
            margin: 1rem 0;
        ">
            <h3 style="
                color: {self.colors['text']};
                font-weight: 600;
                margin-bottom: 1rem;
                text-align: center;
            ">{title}</h3>
            
            <table style="
                width: 100%;
                border-collapse: collapse;
                font-family: Arial, sans-serif;
            ">
        """
        
        for key, value in data.items():
            # Форматируем значение
            if isinstance(value, (int, float)):
                if key.lower().find('сумма') != -1 or key.lower().find('amount') != -1:
                    formatted_value = ComponentUtils.format_number(value, "currency")
                elif key.lower().find('процент') != -1 or key.lower().find('rate') != -1:
                    formatted_value = ComponentUtils.format_number(value, "percentage")
                else:
                    formatted_value = ComponentUtils.format_number(value)
            else:
                formatted_value = str(value)
            
            table_html += f"""
            <tr style="border-bottom: 1px solid #e2e8f0;">
                <td style="
                    padding: 0.75rem;
                    font-weight: 600;
                    color: {self.colors['text']};
                    width: 60%;
                ">{key}</td>
                <td style="
                    padding: 0.75rem;
                    text-align: right;
                    color: {self.colors['text']};
                    font-weight: 500;
                ">{formatted_value}</td>
            </tr>
            """
        
        table_html += """
            </table>
        </div>
        """
        
        st.markdown(table_html, unsafe_allow_html=True)


class ComparisonTable(BaseComponent):
    """Таблица сравнения"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        data: List[Dict[str, Any]],
        title: str = "Сравнение",
        highlight_best: bool = True,
        **kwargs
    ) -> None:
        """
        Отрисовывает таблицу сравнения
        
        Args:
            data: Список словарей с данными для сравнения
            title: Заголовок таблицы
            highlight_best: Выделять лучшие значения
        """
        if not self.validate_data(data) or not data:
            return
        
        # Преобразуем в DataFrame
        df = pd.DataFrame(data)
        
        if df.empty:
            st.warning("Нет данных для сравнения")
            return
        
        # Заголовок
        st.markdown(f"""
        <h3 style="
            color: {self.colors['text']};
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        ">{title}</h3>
        """, unsafe_allow_html=True)
        
        # Стилизация таблицы
        if highlight_best:
            # Находим лучшие значения для числовых колонок
            styled_df = df.style.apply(self._highlight_best_values, axis=0)
            st.dataframe(styled_df, use_container_width=True)
        else:
            st.dataframe(df, use_container_width=True)
    
    def _highlight_best_values(self, series):
        """Выделяет лучшие значения в серии"""
        if series.dtype in ['int64', 'float64']:
            max_val = series.max()
            return [f'background-color: {self.colors["bg"]}' if val == max_val else '' for val in series]
        return ['' for _ in series]


# Регистрируем компоненты в фабрике
from components.base import ComponentFactory

ComponentFactory.register_component(ComponentType.TABLE, DataTable)
