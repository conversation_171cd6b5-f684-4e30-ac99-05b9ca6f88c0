"""
Унифицированные компоненты графиков
"""
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from typing import Optional, Dict, Any, List
from components.base import BaseComponent, ComponentConfig, ComponentType, ColorScheme


class BaseChart(BaseComponent):
    """Базовый класс для графиков"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
        self.chart_colors = self._get_chart_colors()
    
    def _get_chart_colors(self) -> List[str]:
        """Возвращает цветовую палитру для графиков"""
        return [
            '#3b82f6',  # Основной синий
            '#1e40af',  # Темно-синий
            '#2563eb',  # Средний синий
            '#60a5fa',  # Светло-синий
            '#93c5fd',  # Очень светло-синий
            '#1d4ed8',  # Темно-синий альтернативный
        ]
    
    def _get_layout_config(self, title: str = "") -> Dict[str, Any]:
        """Возвращает базовую конфигурацию макета"""
        return {
            'title': {
                'text': title,
                'font': {'size': 18, 'color': self.colors['text'], 'family': 'Arial, sans-serif'},
                'x': 0.5,
                'xanchor': 'center'
            },
            'plot_bgcolor': 'white',
            'paper_bgcolor': 'white',
            'font': {'color': self.colors['text'], 'family': 'Arial, sans-serif'},
            'margin': {'l': 50, 'r': 50, 't': 80, 'b': 50},
            'showlegend': True,
            'legend': {
                'orientation': 'h',
                'yanchor': 'bottom',
                'y': -0.2,
                'xanchor': 'center',
                'x': 0.5
            }
        }


class PieChart(BaseChart):
    """Круговая диаграмма"""
    
    def render(
        self,
        data: pd.DataFrame,
        values_col: str,
        names_col: str,
        title: str = "",
        **kwargs
    ) -> None:
        """
        Отрисовывает круговую диаграмму
        
        Args:
            data: DataFrame с данными
            values_col: Колонка со значениями
            names_col: Колонка с названиями
            title: Заголовок графика
        """
        if not self.validate_data(data) or data.empty:
            st.warning("Нет данных для отображения круговой диаграммы")
            return
        
        fig = go.Figure(data=[go.Pie(
            labels=data[names_col],
            values=data[values_col],
            hole=0.3,
            marker=dict(
                colors=self.chart_colors,
                line=dict(color='white', width=2)
            ),
            textinfo='label+percent',
            textposition='outside',
            hovertemplate='<b>%{label}</b><br>Значение: %{value}<br>Процент: %{percent}<extra></extra>'
        )])
        
        fig.update_layout(**self._get_layout_config(title))
        
        st.plotly_chart(fig, use_container_width=True)


class BarChart(BaseChart):
    """Столбчатая диаграмма"""
    
    def render(
        self,
        data: pd.DataFrame,
        x_col: str,
        y_col: str,
        title: str = "",
        orientation: str = "v",
        **kwargs
    ) -> None:
        """
        Отрисовывает столбчатую диаграмму
        
        Args:
            data: DataFrame с данными
            x_col: Колонка для оси X
            y_col: Колонка для оси Y
            title: Заголовок графика
            orientation: Ориентация ('v' - вертикальная, 'h' - горизонтальная)
        """
        if not self.validate_data(data) or data.empty:
            st.warning("Нет данных для отображения столбчатой диаграммы")
            return
        
        if orientation == "h":
            fig = go.Figure(data=[go.Bar(
                x=data[y_col],
                y=data[x_col],
                orientation='h',
                marker=dict(color=self.chart_colors[0]),
                hovertemplate='<b>%{y}</b><br>Значение: %{x}<extra></extra>'
            )])
        else:
            fig = go.Figure(data=[go.Bar(
                x=data[x_col],
                y=data[y_col],
                marker=dict(color=self.chart_colors[0]),
                hovertemplate='<b>%{x}</b><br>Значение: %{y}<extra></extra>'
            )])
        
        layout_config = self._get_layout_config(title)
        layout_config.update({
            'xaxis': {'title': x_col if orientation == "v" else y_col},
            'yaxis': {'title': y_col if orientation == "v" else x_col}
        })
        
        fig.update_layout(**layout_config)
        
        st.plotly_chart(fig, use_container_width=True)


class LineChart(BaseChart):
    """Линейный график"""
    
    def render(
        self,
        data: pd.DataFrame,
        x_col: str,
        y_col: str,
        title: str = "",
        group_col: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Отрисовывает линейный график
        
        Args:
            data: DataFrame с данными
            x_col: Колонка для оси X
            y_col: Колонка для оси Y
            title: Заголовок графика
            group_col: Колонка для группировки линий
        """
        if not self.validate_data(data) or data.empty:
            st.warning("Нет данных для отображения линейного графика")
            return
        
        fig = go.Figure()
        
        if group_col and group_col in data.columns:
            # Множественные линии
            for i, group in enumerate(data[group_col].unique()):
                group_data = data[data[group_col] == group]
                fig.add_trace(go.Scatter(
                    x=group_data[x_col],
                    y=group_data[y_col],
                    mode='lines+markers',
                    name=str(group),
                    line=dict(color=self.chart_colors[i % len(self.chart_colors)], width=3),
                    marker=dict(size=6),
                    hovertemplate=f'<b>{group}</b><br>{x_col}: %{{x}}<br>{y_col}: %{{y}}<extra></extra>'
                ))
        else:
            # Одна линия
            fig.add_trace(go.Scatter(
                x=data[x_col],
                y=data[y_col],
                mode='lines+markers',
                line=dict(color=self.chart_colors[0], width=3),
                marker=dict(size=6),
                hovertemplate=f'<b>{x_col}: %{{x}}</b><br>{y_col}: %{{y}}<extra></extra>'
            ))
        
        layout_config = self._get_layout_config(title)
        layout_config.update({
            'xaxis': {'title': x_col},
            'yaxis': {'title': y_col}
        })
        
        fig.update_layout(**layout_config)
        
        st.plotly_chart(fig, use_container_width=True)


class GaugeChart(BaseChart):
    """Спидометр"""
    
    def render(
        self,
        value: float,
        max_value: float,
        title: str = "",
        unit: str = "",
        **kwargs
    ) -> None:
        """
        Отрисовывает спидометр
        
        Args:
            value: Текущее значение
            max_value: Максимальное значение
            title: Заголовок
            unit: Единица измерения
        """
        if not self.validate_data(value) or not self.validate_data(max_value):
            return
        
        percentage = (value / max_value * 100) if max_value > 0 else 0
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=percentage,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': title, 'font': {'size': 18, 'color': self.colors['text']}},
            number={'suffix': "%", 'font': {'size': 24, 'color': self.colors['text']}},
            gauge={
                'axis': {'range': [None, 100], 'tickcolor': self.colors['text']},
                'bar': {'color': self.chart_colors[0]},
                'steps': [
                    {'range': [0, 50], 'color': '#f1f5f9'},
                    {'range': [50, 80], 'color': '#e2e8f0'},
                    {'range': [80, 100], 'color': '#cbd5e1'}
                ],
                'threshold': {
                    'line': {'color': self.chart_colors[1], 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(
            paper_bgcolor='white',
            plot_bgcolor='white',
            font={'color': self.colors['text']},
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)


# Регистрируем компоненты в фабрике
from components.base import ComponentFactory

ComponentFactory.register_component(ComponentType.CHART, PieChart)
