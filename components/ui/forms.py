"""
Унифицированные компоненты форм
"""
import streamlit as st
from typing import Optional, Dict, Any, List, Union
from datetime import date, datetime
from components.base import BaseComponent, ComponentConfig, ComponentType


class FilterForm(BaseComponent):
    """Унифицированная форма фильтров"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        filters_config: List[Dict[str, Any]],
        title: str = "Фильтры",
        columns: int = 4,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Отрисовывает форму фильтров
        
        Args:
            filters_config: Конфигурация фильтров
            title: Заголовок формы
            columns: Количество колонок
            
        Returns:
            Словарь с выбранными значениями фильтров
        """
        if not self.validate_data(filters_config):
            return {}
        
        # Заголовок формы
        st.markdown(f"""
        <h3 style="
            color: {self.colors['text']};
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid {self.colors['border']};
        ">🔧 {title}</h3>
        """, unsafe_allow_html=True)
        
        # Создаем контейнер для фильтров
        with st.container():
            # Разбиваем фильтры на строки
            filters_per_row = columns
            filter_values = {}
            
            for i in range(0, len(filters_config), filters_per_row):
                row_filters = filters_config[i:i + filters_per_row]
                cols = st.columns(len(row_filters))
                
                for j, filter_config in enumerate(row_filters):
                    with cols[j]:
                        filter_value = self._render_filter_widget(filter_config)
                        filter_values[filter_config['key']] = filter_value
            
            # Кнопки действий
            self._render_action_buttons()
        
        return filter_values
    
    def _render_filter_widget(self, config: Dict[str, Any]) -> Any:
        """Отрисовывает виджет фильтра"""
        widget_type = config.get('type', 'selectbox')
        label = config.get('label', 'Фильтр')
        key = config.get('key', 'filter')
        
        if widget_type == 'selectbox':
            return st.selectbox(
                label,
                options=config.get('options', []),
                index=config.get('default_index', 0),
                format_func=config.get('format_func'),
                key=key
            )
        
        elif widget_type == 'multiselect':
            return st.multiselect(
                label,
                options=config.get('options', []),
                default=config.get('default', []),
                key=key
            )
        
        elif widget_type == 'text_input':
            return st.text_input(
                label,
                value=config.get('default', ''),
                placeholder=config.get('placeholder', ''),
                key=key
            )
        
        elif widget_type == 'number_input':
            return st.number_input(
                label,
                min_value=config.get('min_value'),
                max_value=config.get('max_value'),
                value=config.get('default', 0),
                step=config.get('step', 1),
                key=key
            )
        
        elif widget_type == 'date_input':
            return st.date_input(
                label,
                value=config.get('default', date.today()),
                min_value=config.get('min_value'),
                max_value=config.get('max_value'),
                key=key
            )
        
        elif widget_type == 'slider':
            return st.slider(
                label,
                min_value=config.get('min_value', 0),
                max_value=config.get('max_value', 100),
                value=config.get('default', 50),
                step=config.get('step', 1),
                key=key
            )
        
        elif widget_type == 'checkbox':
            return st.checkbox(
                label,
                value=config.get('default', False),
                key=key
            )
        
        else:
            st.warning(f"Неизвестный тип виджета: {widget_type}")
            return None
    
    def _render_action_buttons(self) -> Dict[str, bool]:
        """Отрисовывает кнопки действий"""
        st.markdown("---")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            apply_filters = st.button("✅ Применить", type="primary", use_container_width=True)
        with col2:
            reset_filters = st.button("🔄 Сбросить", use_container_width=True, key="forms_reset_filters")
        with col3:
            export_data = st.button("📥 Экспорт", use_container_width=True, key="forms_export_data")
        with col4:
            refresh_data = st.button("🔄 Обновить", use_container_width=True, key="forms_refresh_data")
        
        # Обработка кнопок
        if reset_filters or refresh_data:
            st.cache_data.clear()
            if refresh_data:
                st.success("Данные обновлены!")
            st.rerun()
        
        return {
            'apply_filters': apply_filters,
            'reset_filters': reset_filters,
            'export_data': export_data,
            'refresh_data': refresh_data
        }


class SearchForm(BaseComponent):
    """Форма поиска"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        placeholder: str = "Введите поисковый запрос...",
        search_types: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Отрисовывает форму поиска
        
        Args:
            placeholder: Подсказка для поля поиска
            search_types: Типы поиска для выбора
            
        Returns:
            Словарь с параметрами поиска
        """
        col1, col2, col3 = st.columns([3, 1, 1])
        
        with col1:
            search_query = st.text_input(
                "🔍 Поиск:",
                placeholder=placeholder,
                key="search_query"
            )
        
        with col2:
            if search_types:
                search_type = st.selectbox(
                    "Тип поиска:",
                    options=search_types,
                    key="search_type"
                )
            else:
                search_type = "all"
        
        with col3:
            search_button = st.button("Найти", type="primary", use_container_width=True)
        
        return {
            'query': search_query,
            'type': search_type,
            'search_clicked': search_button
        }


class ConfigForm(BaseComponent):
    """Форма конфигурации"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        config_options: Dict[str, Dict[str, Any]],
        title: str = "Настройки",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Отрисовывает форму конфигурации
        
        Args:
            config_options: Опции конфигурации
            title: Заголовок формы
            
        Returns:
            Словарь с выбранными настройками
        """
        if not self.validate_data(config_options):
            return {}
        
        st.markdown(f"### ⚙️ {title}")
        
        config_values = {}
        
        with st.expander("Настройки отображения", expanded=False):
            for key, option in config_options.items():
                option_type = option.get('type', 'checkbox')
                label = option.get('label', key)
                
                if option_type == 'checkbox':
                    config_values[key] = st.checkbox(
                        label,
                        value=option.get('default', False),
                        help=option.get('help'),
                        key=f"config_{key}"
                    )
                
                elif option_type == 'selectbox':
                    config_values[key] = st.selectbox(
                        label,
                        options=option.get('options', []),
                        index=option.get('default_index', 0),
                        help=option.get('help'),
                        key=f"config_{key}"
                    )
                
                elif option_type == 'slider':
                    config_values[key] = st.slider(
                        label,
                        min_value=option.get('min_value', 0),
                        max_value=option.get('max_value', 100),
                        value=option.get('default', 50),
                        help=option.get('help'),
                        key=f"config_{key}"
                    )
        
        return config_values


# Регистрируем компоненты в фабрике
from components.base import ComponentFactory

ComponentFactory.register_component(ComponentType.FORM, FilterForm)
