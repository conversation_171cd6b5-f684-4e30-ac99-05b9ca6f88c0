"""
Унифицированные компоненты метрик
"""
import streamlit as st
from typing import Optional, Dict, Any, Union
from components.base import BaseComponent, ComponentConfig, ComponentType, ColorScheme, ComponentUtils


class MetricCard(BaseComponent):
    """Унифицированная карточка метрики"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        label: str,
        value: Union[str, int, float],
        delta: Optional[str] = None,
        icon: str = "📊",
        trend: Optional[str] = None,
        format_type: str = "default",
        **kwargs
    ) -> None:
        """
        Отрисовывает карточку метрики
        
        Args:
            label: Название метрики
            value: Значение метрики
            delta: Дополнительная информация
            icon: Иконка метрики
            trend: Тренд (up, down, stable, positive, negative, neutral)
            format_type: Тип форматирования (default, currency, percentage, compact)
        """
        if not self.validate_data(value):
            return
        
        # Форматируем значение
        if isinstance(value, (int, float)):
            formatted_value = ComponentUtils.format_number(value, format_type)
        else:
            formatted_value = str(value)
        
        # Получаем иконку тренда
        trend_icon = ComponentUtils.get_trend_icon(trend)
        delta_with_trend = f"{trend_icon} {delta}" if trend and delta else delta or ""
        
        # Создаем HTML
        card_html = f"""
        <div style="
            background: {self.colors['gradient']};
            padding: 1.5rem;
            {self.get_base_styles()}
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
            {self.get_animation_styles()}
        ">
            <div style="
                font-size: 2rem;
                margin-bottom: 0.75rem;
                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
            ">{icon}</div>
            
            <div style="
                font-size: 1.75rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            ">{formatted_value}</div>
            
            <div style="
                font-size: 1rem;
                font-weight: 600;
                color: {self.colors['text_light']};
                margin-bottom: 0.25rem;
            ">{label}</div>
            
            {f'''<div style="
                font-size: 0.875rem;
                color: rgba(255,255,255,0.8);
                margin-top: 0.5rem;
            ">{delta_with_trend}</div>''' if delta_with_trend else ''}
            
            <div style="
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            " class="shine-effect"></div>
        </div>
        
        <style>
        .shine-effect:hover {{
            left: 100%;
        }}
        {self.get_responsive_styles()}
        </style>
        """
        
        st.markdown(card_html, unsafe_allow_html=True)


class ProgressBar(BaseComponent):
    """Унифицированный прогресс-бар"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        label: str,
        current: float,
        target: float,
        show_percentage: bool = True,
        show_values: bool = True,
        height: str = "20px",
        **kwargs
    ) -> None:
        """
        Отрисовывает прогресс-бар
        
        Args:
            label: Название индикатора
            current: Текущее значение
            target: Целевое значение
            show_percentage: Показывать процент
            show_values: Показывать значения
            height: Высота прогресс-бара
        """
        if not self.validate_data(current) or not self.validate_data(target):
            return
        
        percentage = min((current / target * 100) if target > 0 else 0, 100)
        
        progress_html = f"""
        <div style="
            background: white;
            padding: 1.5rem;
            {self.get_base_styles()}
            margin: 1rem 0;
        ">
            <div style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            ">
                <h4 style="
                    color: {self.colors['text']};
                    margin: 0;
                    font-weight: 600;
                ">{label}</h4>
                {f'<span style="color: {self.colors["text"]}; font-weight: 600;">{percentage:.1f}%</span>' if show_percentage else ''}
            </div>
            
            <div style="
                background: #f1f5f9;
                border-radius: 10px;
                height: {height};
                position: relative;
                overflow: hidden;
            ">
                <div style="
                    background: {self.colors['gradient']};
                    height: 100%;
                    width: {percentage}%;
                    border-radius: 10px;
                    transition: width 0.8s ease;
                    position: relative;
                ">
                    {'''<div style="
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                        animation: progress-shine 2s infinite;
                    "></div>''' if self.config.animated else ''}
                </div>
            </div>
            
            {f'''<div style="
                display: flex;
                justify-content: space-between;
                margin-top: 0.5rem;
                font-size: 0.875rem;
                color: #64748b;
            ">
                <span>{ComponentUtils.format_number(current)}</span>
                <span>{ComponentUtils.format_number(target)}</span>
            </div>''' if show_values else ''}
        </div>
        
        <style>
        @keyframes progress-shine {{
            0% {{ left: -100%; }}
            100% {{ left: 100%; }}
        }}
        {self.get_responsive_styles()}
        </style>
        """
        
        st.markdown(progress_html, unsafe_allow_html=True)


class MetricGrid(BaseComponent):
    """Сетка метрик"""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
    
    def render(
        self,
        metrics: list,
        columns: int = 4,
        **kwargs
    ) -> None:
        """
        Отрисовывает сетку метрик
        
        Args:
            metrics: Список словарей с данными метрик
            columns: Количество колонок
        """
        if not self.validate_data(metrics):
            return
        
        # Создаем колонки Streamlit
        cols = st.columns(columns)
        
        for i, metric_data in enumerate(metrics):
            col_index = i % columns
            
            with cols[col_index]:
                # Создаем конфигурацию для метрики
                metric_config = ComponentConfig(
                    ComponentType.METRIC,
                    color_scheme=ColorScheme(metric_data.get('color_scheme', 'primary')),
                    animated=self.config.animated,
                    responsive=self.config.responsive
                )
                
                # Создаем и отрисовываем метрику
                metric_card = MetricCard(metric_config)
                metric_card.render(**metric_data)


# Регистрируем компоненты в фабрике
from components.base import ComponentFactory

ComponentFactory.register_component(ComponentType.METRIC, MetricCard)
ComponentFactory.register_component(ComponentType.PROGRESS, ProgressBar)
