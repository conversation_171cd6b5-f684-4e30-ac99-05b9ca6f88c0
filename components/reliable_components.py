"""
Надежные визуальные компоненты с встроенными стилями для Streamlit
Альтернатива Tailwind CSS с гарантированной совместимостью
"""
import streamlit as st
from typing import Optional, Dict, Any, List


class ReliableComponents:
    """Класс для создания надежных визуальных компонентов со встроенными стилями"""
    
    @staticmethod
    def enhanced_metric_card(
        label: str, 
        value: str, 
        delta: Optional[str] = None,
        icon: str = "📊",
        color_scheme: str = "blue",
        trend: Optional[str] = None,
        animation_delay: float = 0.0
    ) -> None:
        """
        Создает улучшенную карточку метрики с встроенными стилями
        """
        
        # Цветовые схемы с градиентами
        color_schemes = {
            'blue': {
                'gradient': 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                'bg': '#eff6ff',
                'border': '#dbeafe',
                'text': '#1e40af',
                'icon_bg': '#dbeafe'
            },
            'green': {
                'gradient': 'linear-gradient(135deg, #10b981, #047857)',
                'bg': '#ecfdf5',
                'border': '#d1fae5',
                'text': '#065f46',
                'icon_bg': '#d1fae5'
            },
            'red': {
                'gradient': 'linear-gradient(135deg, #ef4444, #dc2626)',
                'bg': '#fef2f2',
                'border': '#fecaca',
                'text': '#991b1b',
                'icon_bg': '#fecaca'
            },
            'purple': {
                'gradient': 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
                'bg': '#f5f3ff',
                'border': '#e9d5ff',
                'text': '#6b21a8',
                'icon_bg': '#e9d5ff'
            },
            'orange': {
                'gradient': 'linear-gradient(135deg, #f59e0b, #d97706)',
                'bg': '#fffbeb',
                'border': '#fed7aa',
                'text': '#92400e',
                'icon_bg': '#fed7aa'
            }
        }
        
        scheme = color_schemes.get(color_scheme, color_schemes['blue'])
        
        # Иконки трендов
        trend_icons = {
            'up': '📈',
            'down': '📉',
            'stable': '➡️'
        }
        
        trend_icon = trend_icons.get(trend, '') if trend else ''
        delta_html = f'''
        <div style="
            display: flex; 
            align-items: center; 
            gap: 0.25rem; 
            font-size: 0.875rem; 
            color: #6b7280; 
            margin-top: 0.5rem;
        ">
            {trend_icon} {delta}
        </div>
        ''' if delta else ""
        
        card_html = f'''
        <div style="
            position: relative;
            background: white;
            border: 1px solid {scheme['border']};
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: fadeInUp 0.6s ease-out forwards;
            animation-delay: {animation_delay}s;
            opacity: 0;
            overflow: hidden;
        " onmouseover="
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
        " onmouseout="
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        ">
            <!-- Градиентная полоска сверху -->
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: {scheme['gradient']};
                border-radius: 1rem 1rem 0 0;
            "></div>
            
            <!-- Иконка в углу -->
            <div style="
                position: absolute;
                top: 1rem;
                right: 1rem;
                width: 3rem;
                height: 3rem;
                background: {scheme['icon_bg']};
                border-radius: 0.75rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
                transition: transform 0.3s ease;
            " onmouseover="this.style.transform = 'scale(1.1)'" onmouseout="this.style.transform = 'scale(1)'">
                {icon}
            </div>
            
            <!-- Основной контент -->
            <div style="padding-top: 1.5rem; padding-bottom: 1rem;">
                <div style="
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: #6b7280;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    margin-bottom: 0.5rem;
                ">
                    {label}
                </div>
                <div style="
                    font-size: 2rem;
                    font-weight: 700;
                    color: {scheme['text']};
                    margin-bottom: 0.25rem;
                    transition: transform 0.3s ease;
                " onmouseover="this.style.transform = 'scale(1.05)'" onmouseout="this.style.transform = 'scale(1)'">
                    {value}
                </div>
                {delta_html}
            </div>
        </div>
        '''
        
        st.markdown(card_html, unsafe_allow_html=True)
    
    @staticmethod
    def section_divider(title: str, icon: str = "", subtitle: str = "") -> None:
        """
        Создает современный разделитель секций со встроенными стилями
        """
        subtitle_html = f'''
        <p style="
            color: #6b7280; 
            font-size: 0.875rem; 
            margin: 0.25rem 0 0 0;
            font-weight: 300;
        ">{subtitle}</p>
        ''' if subtitle else ""
        
        divider_html = f'''
        <div style="
            margin: 2rem 0;
            animation: slideInRight 0.8s ease-out;
        ">
            <div style="
                display: flex; 
                align-items: center; 
                gap: 1rem; 
                margin-bottom: 0.5rem;
            ">
                <div style="
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #6366f1, #8b5cf6);
                    border-radius: 1rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                    transition: transform 0.3s ease;
                " onmouseover="this.style.transform = 'scale(1.05)'" onmouseout="this.style.transform = 'scale(1)'">
                    {icon}
                </div>
                <div>
                    <h2 style="
                        font-size: 1.5rem;
                        font-weight: 700;
                        color: #1f2937;
                        margin: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    ">{title}</h2>
                    {subtitle_html}
                </div>
            </div>
            <div style="
                height: 1px;
                background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
                margin: 1.5rem 0;
            "></div>
        </div>
        '''
        st.markdown(divider_html, unsafe_allow_html=True)
    
    @staticmethod
    def info_panel_modern(
        title: str,
        content: str,
        panel_type: str = "info",
        icon: str = "ℹ️",
        collapsible: bool = False
    ) -> None:
        """
        Создает современную информационную панель со встроенными стилями
        """
        
        panel_styles = {
            'info': {
                'gradient': 'linear-gradient(135deg, #3b82f6, #06b6d4)',
                'bg': '#eff6ff',
                'border': '#dbeafe',
                'text': '#1e40af'
            },
            'success': {
                'gradient': 'linear-gradient(135deg, #10b981, #34d399)',
                'bg': '#ecfdf5',
                'border': '#d1fae5',
                'text': '#065f46'
            },
            'warning': {
                'gradient': 'linear-gradient(135deg, #f59e0b, #fbbf24)',
                'bg': '#fffbeb',
                'border': '#fed7aa',
                'text': '#92400e'
            },
            'error': {
                'gradient': 'linear-gradient(135deg, #ef4444, #f87171)',
                'bg': '#fef2f2',
                'border': '#fecaca',
                'text': '#991b1b'
            }
        }
        
        style = panel_styles.get(panel_type, panel_styles['info'])
        
        if collapsible:
            with st.expander(f"{icon} {title}", expanded=True):
                st.markdown(f'''
                <div style="
                    background: {style['bg']};
                    border: 1px solid {style['border']};
                    color: {style['text']};
                    padding: 1rem;
                    border-radius: 0.5rem;
                ">
                    {content}
                </div>
                ''', unsafe_allow_html=True)
        else:
            panel_html = f'''
            <div style="
                position: relative;
                background: {style['bg']};
                border: 1px solid {style['border']};
                color: {style['text']};
                border-radius: 1rem;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                overflow: hidden;
            " onmouseover="
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
            " onmouseout="
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            ">
                <!-- Градиентная полоска сверху -->
                <div style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: {style['gradient']};
                    border-radius: 1rem 1rem 0 0;
                "></div>
                
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 1rem;
                ">
                    <div style="
                        width: 2.5rem;
                        height: 2.5rem;
                        background: {style['gradient']};
                        border-radius: 0.75rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 1.125rem;
                        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    ">
                        {icon}
                    </div>
                    <h3 style="
                        font-size: 1.125rem;
                        font-weight: 600;
                        color: {style['text']};
                        margin: 0;
                    ">{title}</h3>
                </div>
                <div style="
                    font-size: 0.875rem;
                    line-height: 1.6;
                ">
                    {content}
                </div>
            </div>
            '''
            st.markdown(panel_html, unsafe_allow_html=True)
    
    @staticmethod
    def progress_indicator_modern(
        label: str,
        current: float,
        target: float,
        color: str = "blue",
        show_percentage: bool = True,
        animated: bool = True
    ) -> None:
        """
        Создает современный индикатор прогресса со встроенными стилями
        """
        percentage = min((current / target * 100) if target > 0 else 0, 100)
        
        color_gradients = {
            'blue': 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
            'green': 'linear-gradient(90deg, #10b981, #047857)',
            'red': 'linear-gradient(90deg, #ef4444, #dc2626)',
            'purple': 'linear-gradient(90deg, #8b5cf6, #7c3aed)',
            'orange': 'linear-gradient(90deg, #f59e0b, #d97706)'
        }
        
        gradient = color_gradients.get(color, color_gradients['blue'])
        animation_style = '''
            position: relative;
            overflow: hidden;
        ''' if animated else ''
        
        animation_overlay = '''
            <div style="
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                animation: progressShine 2s infinite;
            "></div>
        ''' if animated else ''
        
        progress_html = f'''
        <style>
            @keyframes progressShine {{
                0% {{ left: -100%; }}
                100% {{ left: 100%; }}
            }}
        </style>
        <div style="
            margin: 1rem 0;
            padding: 1rem;
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        " onmouseover="
            this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
        " onmouseout="
            this.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
        ">
            <div style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;
            ">
                <span style="
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: #374151;
                ">{label}</span>
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <span style="
                        font-size: 0.875rem;
                        color: #6b7280;
                    ">{current:,.0f} / {target:,.0f}</span>
                    {f'<span style="font-size: 0.875rem; font-weight: 600; color: #374151;">({percentage:.1f}%)</span>' if show_percentage else ''}
                </div>
            </div>
            <div style="position: relative;">
                <div style="
                    width: 100%;
                    background: #e5e7eb;
                    border-radius: 9999px;
                    height: 0.75rem;
                    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
                ">
                    <div style="
                        background: {gradient};
                        height: 0.75rem;
                        border-radius: 9999px;
                        width: {percentage}%;
                        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                        transition: all 1s ease-out;
                        {animation_style}
                    ">
                        {animation_overlay}
                        <div style="
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 9999px;
                            animation: pulse 2s infinite;
                        "></div>
                    </div>
                </div>
            </div>
        </div>
        '''
        st.markdown(progress_html, unsafe_allow_html=True)


# Глобальный экземпляр надежных компонентов
reliable_components = ReliableComponents()
