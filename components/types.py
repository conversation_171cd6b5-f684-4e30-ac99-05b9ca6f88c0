"""
Типы данных для компонентов дашборда
"""
from typing import TypedDict, Optional, Dict, Any, List, Union, Literal
from enum import Enum
from datetime import date, datetime


# Базовые типы
ComponentValue = Union[str, int, float]
ComponentData = Union[Dict[str, Any], List[Dict[str, Any]]]


# Enum для типов компонентов
class ComponentTypeEnum(Enum):
    """Типы компонентов"""
    METRIC = "metric"
    CHART = "chart"
    TABLE = "table"
    FORM = "form"
    PANEL = "panel"
    BUTTON = "button"
    PROGRESS = "progress"
    GRID = "grid"


class ColorSchemeEnum(Enum):
    """Цветовые схемы"""
    PRIMARY = "primary"
    SUCCESS = "success"
    WARNING = "warning"
    DANGER = "danger"
    INFO = "info"


class ChartTypeEnum(Enum):
    """Типы графиков"""
    PIE = "pie"
    BAR = "bar"
    LINE = "line"
    AREA = "area"
    SCATTER = "scatter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"


class StatusEnum(Enum):
    """Статусы договоров"""
    ACTIVE = "active"
    COMPLETED = "completed"
    SUSPENDED = "suspended"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"
    PENDING = "pending"


class TrendEnum(Enum):
    """Типы трендов"""
    UP = "up"
    DOWN = "down"
    STABLE = "stable"
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


# TypedDict для конфигураций
class BaseComponentConfig(TypedDict, total=False):
    """Базовая конфигурация компонента"""
    component_type: ComponentTypeEnum
    color_scheme: ColorSchemeEnum
    animated: bool
    responsive: bool
    css_classes: List[str]
    custom_styles: Dict[str, str]


class MetricConfig(BaseComponentConfig):
    """Конфигурация метрики"""
    show_delta: bool
    show_trend: bool
    format_type: Literal["default", "currency", "percentage", "compact", "decimal"]
    precision: int
    animation_delay: float


class ChartConfig(BaseComponentConfig):
    """Конфигурация графика"""
    chart_type: ChartTypeEnum
    width: Optional[int]
    height: Optional[int]
    show_legend: bool
    show_grid: bool
    color_palette: List[str]
    interactive: bool


class TableConfig(BaseComponentConfig):
    """Конфигурация таблицы"""
    page_size: int
    sortable: bool
    filterable: bool
    show_index: bool
    show_pagination: bool
    column_widths: Dict[str, int]


class FormConfig(BaseComponentConfig):
    """Конфигурация формы"""
    columns: int
    show_buttons: bool
    validation_rules: Dict[str, List[str]]
    submit_button_text: str
    reset_button_text: str


class ProgressConfig(BaseComponentConfig):
    """Конфигурация прогресс-бара"""
    show_percentage: bool
    show_values: bool
    height: str
    striped: bool
    animated_progress: bool


# TypedDict для данных
class MetricData(TypedDict):
    """Данные метрики"""
    label: str
    value: ComponentValue
    delta: Optional[str]
    icon: str
    trend: Optional[TrendEnum]
    description: Optional[str]


class ChartData(TypedDict):
    """Данные графика"""
    title: str
    data: ComponentData
    x_column: Optional[str]
    y_column: Optional[str]
    group_column: Optional[str]
    labels: Optional[List[str]]
    values: Optional[List[Union[int, float]]]


class TableData(TypedDict):
    """Данные таблицы"""
    title: str
    data: ComponentData
    columns: Optional[List[str]]
    column_types: Optional[Dict[str, str]]
    formatters: Optional[Dict[str, str]]


class FilterData(TypedDict):
    """Данные фильтра"""
    key: str
    label: str
    type: Literal["selectbox", "multiselect", "text_input", "number_input", "date_input", "slider", "checkbox"]
    options: Optional[List[Any]]
    default: Optional[Any]
    placeholder: Optional[str]
    help_text: Optional[str]


class FormData(TypedDict):
    """Данные формы"""
    title: str
    filters: List[FilterData]
    submit_action: Optional[str]
    reset_action: Optional[str]


# TypedDict для результатов
class ValidationResult(TypedDict):
    """Результат валидации"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]


class RenderResult(TypedDict):
    """Результат рендеринга"""
    success: bool
    component_id: Optional[str]
    error_message: Optional[str]
    render_time: Optional[float]


class ComponentState(TypedDict):
    """Состояние компонента"""
    component_id: str
    component_type: ComponentTypeEnum
    config: BaseComponentConfig
    data: Optional[ComponentData]
    last_updated: datetime
    render_count: int
    error_count: int


# Типы для колбэков и обработчиков
from typing import Callable

ComponentCallback = Callable[[Any], None]
ValidationCallback = Callable[[Any], ValidationResult]
FormatterCallback = Callable[[Any], str]
FilterCallback = Callable[[ComponentData], ComponentData]


# Типы для стилей
class StyleConfig(TypedDict, total=False):
    """Конфигурация стилей"""
    background: str
    color: str
    border: str
    border_radius: str
    padding: str
    margin: str
    font_size: str
    font_weight: str
    box_shadow: str
    gradient: str


class ResponsiveConfig(TypedDict, total=False):
    """Адаптивная конфигурация"""
    mobile: StyleConfig
    tablet: StyleConfig
    desktop: StyleConfig


# Типы для анимаций
class AnimationConfig(TypedDict, total=False):
    """Конфигурация анимации"""
    type: Literal["fadeIn", "slideIn", "bounce", "pulse", "shake", "rotate"]
    duration: float
    delay: float
    easing: str
    repeat: bool


# Сложные типы для специфических компонентов
class DashboardSummary(TypedDict):
    """Сводка дашборда"""
    total_contracts: int
    active_contracts: int
    completed_contracts: int
    suspended_contracts: int
    cancelled_contracts: int
    overdue_contracts: int
    total_amount: float
    average_amount: float
    overdue_rate: float
    completion_rate: float


class ContractData(TypedDict):
    """Данные договора"""
    contract_id: str
    contract_number: str
    contract_name: str
    contract_type: str
    status: StatusEnum
    start_date: date
    end_date: date
    total_amount: float
    contractor: str
    description: Optional[str]


class FilterState(TypedDict):
    """Состояние фильтров"""
    status_filter: Optional[str]
    type_filter: Optional[str]
    amount_filter: Optional[str]
    date_range: Optional[tuple[date, date]]
    search_query: Optional[str]
    contractor_filter: Optional[str]
    sort_by: Optional[str]
    sort_order: Literal["asc", "desc"]


# Экспорт всех типов
__all__ = [
    # Базовые типы
    'ComponentValue',
    'ComponentData',
    
    # Enums
    'ComponentTypeEnum',
    'ColorSchemeEnum',
    'ChartTypeEnum',
    'StatusEnum',
    'TrendEnum',
    
    # Конфигурации
    'BaseComponentConfig',
    'MetricConfig',
    'ChartConfig',
    'TableConfig',
    'FormConfig',
    'ProgressConfig',
    
    # Данные
    'MetricData',
    'ChartData',
    'TableData',
    'FilterData',
    'FormData',
    
    # Результаты
    'ValidationResult',
    'RenderResult',
    'ComponentState',
    
    # Колбэки
    'ComponentCallback',
    'ValidationCallback',
    'FormatterCallback',
    'FilterCallback',
    
    # Стили
    'StyleConfig',
    'ResponsiveConfig',
    'AnimationConfig',
    
    # Специфические типы
    'DashboardSummary',
    'ContractData',
    'FilterState'
]
