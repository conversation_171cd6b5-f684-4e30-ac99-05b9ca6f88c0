"""
Компоненты для отображения таблиц и данных
"""
import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, date

from config.settings import DATA_FORMATS
from config.constants import COLUMN_NAMES, CONTRACT_STATUSES, CONTRACT_TYPES


class TableComponents:
    """Класс для создания различных типов таблиц"""
    
    def __init__(self):
        self.formats = DATA_FORMATS
        self.column_names = COLUMN_NAMES
        self.status_mapping = CONTRACT_STATUSES
        self.type_mapping = CONTRACT_TYPES
    
    def contracts_table(self, df: pd.DataFrame, 
                       show_filters: bool = True,
                       page_size: int = 20) -> pd.DataFrame:
        """Отображает таблицу договоров с фильтрами"""
        if df.empty:
            st.warning("Нет данных для отображения")
            return df
        
        # Подготавливаем данные для отображения
        display_df = self._prepare_contracts_display(df.copy())
        
        if show_filters:
            display_df = self._add_table_filters(display_df)
        
        # Пагинация
        if len(display_df) > page_size:
            display_df = self._add_pagination(display_df, page_size)
        
        # Отображаем таблицу
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True,
            column_config=self._get_column_config()
        )
        
        return display_df
    
    def progress_table(self, df: pd.DataFrame, 
                      title: str = "Прогресс выполнения") -> None:
        """Отображает таблицу прогресса выполнения"""
        if df.empty:
            st.warning("Нет данных о прогрессе")
            return
        
        st.subheader(title)
        
        # Подготавливаем данные
        display_df = df.copy()
        
        # Форматируем колонки
        if 'month' in display_df.columns:
            display_df['month'] = pd.to_datetime(display_df['month']).dt.strftime('%m.%Y')
        
        if 'planned_amount' in display_df.columns:
            display_df['planned_amount'] = display_df['planned_amount'].apply(
                lambda x: self.formats['currency'].format(x)
            )
        
        if 'completed_amount' in display_df.columns:
            display_df['completed_amount'] = display_df['completed_amount'].apply(
                lambda x: self.formats['currency'].format(x)
            )
        
        if 'completion_rate' in display_df.columns:
            display_df['completion_rate'] = display_df['completion_rate'].apply(
                lambda x: f"{x:.1f}%"
            )
        
        # Переименовываем колонки
        display_df = display_df.rename(columns=self.column_names)
        
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True
        )
    
    def summary_metrics_table(self, metrics: Dict[str, Any]) -> None:
        """Отображает таблицу сводных метрик"""
        if not metrics:
            st.warning("Нет метрик для отображения")
            return
        
        # Создаем DataFrame из метрик
        metrics_data = []
        for key, value in metrics.items():
            if key in self.column_names:
                name = self.column_names[key]
            else:
                name = key.replace('_', ' ').title()
            
            # Форматируем значение
            if 'amount' in key:
                formatted_value = self.formats['currency'].format(value)
            elif 'rate' in key or 'percentage' in key:
                formatted_value = f"{value:.1f}%"
            else:
                formatted_value = str(int(value)) if isinstance(value, (int, float)) else str(value)
            
            metrics_data.append({
                'Метрика': name,
                'Значение': formatted_value
            })
        
        metrics_df = pd.DataFrame(metrics_data)
        
        st.dataframe(
            metrics_df,
            use_container_width=True,
            hide_index=True
        )
    
    def top_contracts_table(self, df: pd.DataFrame, 
                           limit: int = 10,
                           sort_by: str = 'total_amount',
                           title: str = "Топ договоров") -> None:
        """Отображает таблицу топ договоров"""
        if df.empty:
            st.warning("Нет данных для отображения")
            return
        
        st.subheader(title)
        
        # Сортируем и берем топ
        top_df = df.nlargest(limit, sort_by)
        
        # Подготавливаем для отображения
        display_df = self._prepare_contracts_display(top_df)
        
        # Добавляем ранг
        display_df.insert(0, 'Ранг', range(1, len(display_df) + 1))
        
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True,
            column_config=self._get_column_config()
        )
    
    def overdue_contracts_table(self, df: pd.DataFrame) -> None:
        """Отображает таблицу просроченных договоров"""
        if df.empty:
            st.success("✅ Нет просроченных договоров")
            return
        
        st.subheader("⚠️ Просроченные договоры")
        
        # Подготавливаем данные
        display_df = self._prepare_contracts_display(df.copy())
        
        # Добавляем колонку с количеством дней просрочки
        today = date.today()
        display_df['Дней просрочки'] = (
            today - pd.to_datetime(df['end_date']).dt.date
        ).dt.days
        
        # Сортируем по количеству дней просрочки
        display_df = display_df.sort_values('Дней просрочки', ascending=False)
        
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True,
            column_config=self._get_column_config()
        )
    
    def _prepare_contracts_display(self, df: pd.DataFrame) -> pd.DataFrame:
        """Подготавливает данные договоров для отображения"""
        display_df = df.copy()
        
        # Форматируем суммы
        if 'total_amount' in display_df.columns:
            display_df['total_amount'] = display_df['total_amount'].apply(
                lambda x: self.formats['currency'].format(x) if pd.notnull(x) else ''
            )
        
        if 'completed_amount' in display_df.columns:
            display_df['completed_amount'] = display_df['completed_amount'].apply(
                lambda x: self.formats['currency'].format(x) if pd.notnull(x) else ''
            )
        
        # Форматируем даты
        date_columns = ['start_date', 'end_date', 'created_at', 'updated_at']
        for col in date_columns:
            if col in display_df.columns:
                display_df[col] = pd.to_datetime(display_df[col]).dt.strftime(
                    self.formats['date']
                )
        
        # Переводим статусы и типы
        if 'status' in display_df.columns:
            display_df['status'] = display_df['status'].map(
                self.status_mapping
            ).fillna(display_df['status'])
        
        if 'contract_type' in display_df.columns:
            display_df['contract_type'] = display_df['contract_type'].map(
                self.type_mapping
            ).fillna(display_df['contract_type'])
        
        # Переименовываем колонки
        display_df = display_df.rename(columns=self.column_names)
        
        return display_df
    
    def _add_table_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Добавляет фильтры для таблицы"""
        st.subheader("🔍 Фильтры")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # Фильтр по статусу
            if 'Статус' in df.columns:
                status_options = ['Все'] + df['Статус'].unique().tolist()
                selected_status = st.selectbox("Статус:", status_options)
                if selected_status != 'Все':
                    df = df[df['Статус'] == selected_status]
        
        with col2:
            # Фильтр по типу
            if 'Тип договора' in df.columns:
                type_options = ['Все'] + df['Тип договора'].unique().tolist()
                selected_type = st.selectbox("Тип договора:", type_options)
                if selected_type != 'Все':
                    df = df[df['Тип договора'] == selected_type]
        
        with col3:
            # Поиск по названию
            if 'Название договора' in df.columns:
                search_term = st.text_input("Поиск по названию:")
                if search_term:
                    df = df[df['Название договора'].str.contains(
                        search_term, case=False, na=False
                    )]
        
        return df
    
    def _add_pagination(self, df: pd.DataFrame, page_size: int) -> pd.DataFrame:
        """Добавляет пагинацию для таблицы"""
        total_pages = (len(df) - 1) // page_size + 1
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            page = st.selectbox(
                f"Страница (всего записей: {len(df)})",
                range(1, total_pages + 1),
                format_func=lambda x: f"Страница {x} из {total_pages}"
            )
        
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        return df.iloc[start_idx:end_idx]
    
    def _get_column_config(self) -> Dict[str, Any]:
        """Возвращает конфигурацию колонок для отображения"""
        return {
            "Общая сумма": st.column_config.TextColumn(
                "Общая сумма",
                help="Общая сумма договора"
            ),
            "Освоено": st.column_config.TextColumn(
                "Освоено",
                help="Освоенная сумма"
            ),
            "Статус": st.column_config.TextColumn(
                "Статус",
                help="Текущий статус договора"
            )
        }


# Глобальный экземпляр компонента таблиц
table_components = TableComponents()
