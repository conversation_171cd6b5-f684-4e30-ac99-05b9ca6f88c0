"""
Базовые классы для всех компонентов дашборда
"""
import streamlit as st
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Type
from enum import Enum
import logging
from datetime import datetime

# Импорты типов
from components.types import (
    ComponentTypeEnum,
    ColorSchemeEnum,
    BaseComponentConfig,
    ValidationResult,
    RenderResult,
    ComponentState,
    StyleConfig
)

logger = logging.getLogger(__name__)


# Используем типы из types.py
ComponentType = ComponentTypeEnum
ColorScheme = ColorSchemeEnum


class ComponentConfig:
    """Конфигурация компонента"""

    def __init__(
        self,
        component_type: ComponentType,
        color_scheme: ColorScheme = ColorScheme.PRIMARY,
        animated: bool = True,
        responsive: bool = True,
        css_classes: Optional[List[str]] = None,
        custom_styles: Optional[Dict[str, str]] = None,
        **kwargs: Any
    ) -> None:
        self.component_type = component_type
        self.color_scheme = color_scheme
        self.animated = animated
        self.responsive = responsive
        self.css_classes = css_classes or []
        self.custom_styles = custom_styles or {}
        self.extra_config = kwargs

        # Валидация конфигурации
        self._validate_config()

    def _validate_config(self) -> None:
        """Валидирует конфигурацию"""
        if not isinstance(self.component_type, ComponentType):
            raise ValueError(f"component_type должен быть экземпляром ComponentType")

        if not isinstance(self.color_scheme, ColorScheme):
            raise ValueError(f"color_scheme должен быть экземпляром ColorScheme")

        if not isinstance(self.animated, bool):
            raise ValueError(f"animated должен быть bool")

        if not isinstance(self.responsive, bool):
            raise ValueError(f"responsive должен быть bool")
    
    def get_colors(self) -> Dict[str, str]:
        """Возвращает цвета для выбранной схемы"""
        color_maps = {
            ColorScheme.PRIMARY: {
                'gradient': 'linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)',
                'bg': '#eff6ff',
                'border': '#3b82f6',
                'text': '#1e40af',
                'text_light': 'rgba(255,255,255,0.9)',
                'shadow': '0 10px 25px -5px rgba(59, 130, 246, 0.2)'
            },
            ColorScheme.SUCCESS: {
                'gradient': 'linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)',
                'bg': '#eff6ff',
                'border': '#2563eb',
                'text': '#1e40af',
                'text_light': 'rgba(255,255,255,0.9)',
                'shadow': '0 10px 25px -5px rgba(37, 99, 235, 0.2)'
            },
            ColorScheme.WARNING: {
                'gradient': 'linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%)',
                'bg': '#eff6ff',
                'border': '#60a5fa',
                'text': '#1e40af',
                'text_light': 'rgba(255,255,255,0.9)',
                'shadow': '0 10px 25px -5px rgba(96, 165, 250, 0.2)'
            },
            ColorScheme.DANGER: {
                'gradient': 'linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%)',
                'bg': '#eff6ff',
                'border': '#93c5fd',
                'text': '#1e40af',
                'text_light': 'rgba(255,255,255,0.9)',
                'shadow': '0 10px 25px -5px rgba(147, 197, 253, 0.2)'
            },
            ColorScheme.INFO: {
                'gradient': 'linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%)',
                'bg': '#eff6ff',
                'border': '#1d4ed8',
                'text': '#1e40af',
                'text_light': 'rgba(255,255,255,0.9)',
                'shadow': '0 10px 25px -5px rgba(29, 78, 216, 0.2)'
            }
        }
        return color_maps.get(self.color_scheme, color_maps[ColorScheme.PRIMARY])


class BaseComponent(ABC):
    """Базовый класс для всех компонентов"""

    def __init__(self, config: ComponentConfig) -> None:
        self.config = config
        self.colors = config.get_colors()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.component_id = f"{self.__class__.__name__}_{id(self)}"
        self.render_count = 0
        self.error_count = 0
        self.last_updated: Optional[datetime] = None

    def validate_data(self, data: Any, data_type: str = "any") -> ValidationResult:
        """Валидация входных данных"""
        errors: List[str] = []
        warnings: List[str] = []

        if data is None:
            errors.append(f"Получены пустые данные для {self.__class__.__name__}")
            self.logger.warning(errors[-1])

        # Дополнительная валидация по типу
        if data is not None:
            if data_type == "dataframe":
                import pandas as pd
                if not isinstance(data, pd.DataFrame):
                    errors.append("Данные должны быть DataFrame")
                elif data.empty:
                    warnings.append("DataFrame пуст")

            elif data_type == "list":
                if not isinstance(data, list):
                    errors.append("Данные должны быть списком")
                elif len(data) == 0:
                    warnings.append("Список пуст")

            elif data_type == "dict":
                if not isinstance(data, dict):
                    errors.append("Данные должны быть словарем")
                elif len(data) == 0:
                    warnings.append("Словарь пуст")

            elif data_type == "number":
                if not isinstance(data, (int, float)):
                    errors.append("Данные должны быть числом")

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def handle_error(self, error: str) -> None:
        """Обработка ошибок компонента"""
        self.error_count += 1
        self.logger.error(f"Ошибка в {self.__class__.__name__}: {error}")
        st.error(f"Ошибка отображения компонента: {error}")

    def get_base_styles(self) -> str:
        """Возвращает базовые CSS стили"""
        base_styles = f"""
        border-radius: 12px;
        box-shadow: {self.colors['shadow']};
        border: 2px solid {self.colors['border']};
        transition: all 0.3s ease;
        """

        # Добавляем кастомные стили
        if self.config.custom_styles:
            for prop, value in self.config.custom_styles.items():
                base_styles += f"{prop}: {value};\n"

        return base_styles

    def get_animation_styles(self) -> str:
        """Возвращает стили анимации"""
        if not self.config.animated:
            return ""

        return """
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        """

    def get_responsive_styles(self) -> str:
        """Возвращает адаптивные стили"""
        if not self.config.responsive:
            return ""

        return """
        @media (max-width: 768px) {
            padding: 1rem !important;
            font-size: 0.9rem !important;
        }

        @media (max-width: 480px) {
            padding: 0.75rem !important;
            font-size: 0.8rem !important;
        }
        """

    def get_css_classes(self) -> str:
        """Возвращает CSS классы"""
        classes = ["component", f"component-{self.config.component_type.value}"]
        classes.extend(self.config.css_classes)
        return " ".join(classes)

    def get_state(self) -> ComponentState:
        """Возвращает состояние компонента"""
        return ComponentState(
            component_id=self.component_id,
            component_type=self.config.component_type,
            config=self.config.__dict__,
            data=None,  # Переопределяется в наследниках
            last_updated=self.last_updated or datetime.now(),
            render_count=self.render_count,
            error_count=self.error_count
        )

    @abstractmethod
    def render(self, **kwargs: Any) -> RenderResult:
        """Основной метод отрисовки компонента"""
        pass

    def render_with_error_handling(self, **kwargs: Any) -> RenderResult:
        """Отрисовка с обработкой ошибок"""
        start_time = datetime.now()

        try:
            result = self.render(**kwargs)
            self.render_count += 1
            self.last_updated = datetime.now()

            # Обновляем результат с метриками
            if isinstance(result, dict):
                result['render_time'] = (datetime.now() - start_time).total_seconds()
                result['component_id'] = self.component_id

            return result

        except Exception as e:
            self.handle_error(str(e))
            return RenderResult(
                success=False,
                component_id=self.component_id,
                error_message=str(e),
                render_time=(datetime.now() - start_time).total_seconds()
            )


class ComponentFactory:
    """Фабрика для создания компонентов"""

    _components = {}
    _instances = {}  # Кеш экземпляров для синглтонов

    @classmethod
    def register_component(cls, component_type: ComponentType, component_class):
        """Регистрирует компонент в фабрике"""
        cls._components[component_type] = component_class
        logger.info(f"Зарегистрирован компонент: {component_type.value}")

    @classmethod
    def create_component(
        cls,
        component_type: ComponentType,
        config: Optional[ComponentConfig] = None,
        singleton: bool = False
    ) -> BaseComponent:
        """
        Создает компонент по типу

        Args:
            component_type: Тип компонента
            config: Конфигурация компонента
            singleton: Использовать паттерн синглтон
        """
        if component_type not in cls._components:
            available_types = [t.value for t in cls.get_available_types()]
            raise ValueError(
                f"Неизвестный тип компонента: {component_type.value}. "
                f"Доступные типы: {', '.join(available_types)}"
            )

        # Проверяем кеш для синглтонов
        if singleton and component_type in cls._instances:
            return cls._instances[component_type]

        if config is None:
            config = ComponentConfig(component_type)

        component_class = cls._components[component_type]
        instance = component_class(config)

        # Сохраняем в кеш для синглтонов
        if singleton:
            cls._instances[component_type] = instance

        return instance

    @classmethod
    def create_metric_card(
        cls,
        color_scheme: ColorScheme = ColorScheme.PRIMARY,
        animated: bool = True
    ):
        """Быстрое создание карточки метрики"""
        config = ComponentConfig(
            ComponentType.METRIC,
            color_scheme=color_scheme,
            animated=animated
        )
        return cls.create_component(ComponentType.METRIC, config)

    @classmethod
    def create_chart(
        cls,
        chart_type: str = "pie",
        color_scheme: ColorScheme = ColorScheme.PRIMARY
    ):
        """Быстрое создание графика"""
        config = ComponentConfig(
            ComponentType.CHART,
            color_scheme=color_scheme,
            chart_type=chart_type
        )
        return cls.create_component(ComponentType.CHART, config)

    @classmethod
    def create_table(
        cls,
        color_scheme: ColorScheme = ColorScheme.PRIMARY,
        responsive: bool = True
    ):
        """Быстрое создание таблицы"""
        config = ComponentConfig(
            ComponentType.TABLE,
            color_scheme=color_scheme,
            responsive=responsive
        )
        return cls.create_component(ComponentType.TABLE, config)

    @classmethod
    def get_available_types(cls) -> List[ComponentType]:
        """Возвращает список доступных типов компонентов"""
        return list(cls._components.keys())

    @classmethod
    def get_component_info(cls) -> Dict[ComponentType, str]:
        """Возвращает информацию о зарегистрированных компонентах"""
        return {
            comp_type: comp_class.__name__
            for comp_type, comp_class in cls._components.items()
        }

    @classmethod
    def clear_cache(cls):
        """Очищает кеш синглтонов"""
        cls._instances.clear()
        logger.info("Кеш компонентов очищен")


# Утилиты для работы с компонентами
class ComponentUtils:
    """Утилиты для компонентов"""
    
    @staticmethod
    def format_number(value: Union[int, float], format_type: str = "default") -> str:
        """Форматирует числа для отображения"""
        if format_type == "currency":
            return f"{value:,.0f} ₽"
        elif format_type == "percentage":
            return f"{value:.1f}%"
        elif format_type == "compact":
            if value >= 1_000_000:
                return f"{value/1_000_000:.1f}М"
            elif value >= 1_000:
                return f"{value/1_000:.1f}К"
            else:
                return f"{value:,.0f}"
        else:
            return f"{value:,}"
    
    @staticmethod
    def get_trend_icon(trend: Optional[str]) -> str:
        """Возвращает иконку тренда"""
        trend_icons = {
            "up": "📈",
            "down": "📉",
            "stable": "➡️",
            "positive": "✅",
            "negative": "❌",
            "neutral": "⚪"
        }
        return trend_icons.get(trend, "")
    
    @staticmethod
    def inject_css(css_content: str) -> None:
        """Внедряет CSS стили"""
        st.markdown(f"<style>{css_content}</style>", unsafe_allow_html=True)
