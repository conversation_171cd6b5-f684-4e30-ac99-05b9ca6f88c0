"""
Общие утилиты для компонентов
"""
import streamlit as st
import pandas as pd
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime, date
import logging

logger = logging.getLogger(__name__)


class ComponentValidator:
    """Валидатор для компонентов"""
    
    @staticmethod
    def validate_data(data: Any, data_type: str = "any") -> bool:
        """Валидирует входные данные"""
        if data is None:
            logger.warning(f"Получены пустые данные для типа {data_type}")
            return False
        
        if data_type == "dataframe":
            if not isinstance(data, pd.DataFrame) or data.empty:
                logger.warning("DataFrame пуст или не является DataFrame")
                return False
        
        elif data_type == "list":
            if not isinstance(data, list) or len(data) == 0:
                logger.warning("Список пуст или не является списком")
                return False
        
        elif data_type == "dict":
            if not isinstance(data, dict) or len(data) == 0:
                logger.warning("Словарь пуст или не является словарем")
                return False
        
        elif data_type == "number":
            if not isinstance(data, (int, float)):
                logger.warning("Данные не являются числом")
                return False
        
        return True
    
    @staticmethod
    def validate_config(config: Dict[str, Any], required_keys: List[str]) -> bool:
        """Валидирует конфигурацию"""
        for key in required_keys:
            if key not in config:
                logger.error(f"Отсутствует обязательный ключ конфигурации: {key}")
                return False
        return True


class DataFormatter:
    """Форматтер данных"""
    
    @staticmethod
    def format_number(
        value: Union[int, float], 
        format_type: str = "default",
        precision: int = 1
    ) -> str:
        """Форматирует числа для отображения"""
        if not isinstance(value, (int, float)):
            return str(value)
        
        if format_type == "currency":
            return f"{value:,.0f} ₽"
        elif format_type == "percentage":
            return f"{value:.{precision}f}%"
        elif format_type == "compact":
            if value >= 1_000_000_000:
                return f"{value/1_000_000_000:.{precision}f}Б"
            elif value >= 1_000_000:
                return f"{value/1_000_000:.{precision}f}М"
            elif value >= 1_000:
                return f"{value/1_000:.{precision}f}К"
            else:
                return f"{value:,.0f}"
        elif format_type == "decimal":
            return f"{value:.{precision}f}"
        else:
            return f"{value:,}"
    
    @staticmethod
    def format_date(value: Union[str, date, datetime], format_type: str = "default") -> str:
        """Форматирует даты"""
        if isinstance(value, str):
            try:
                value = datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                return str(value)
        
        if format_type == "short":
            return value.strftime("%d.%m.%Y")
        elif format_type == "long":
            return value.strftime("%d %B %Y")
        elif format_type == "datetime":
            return value.strftime("%d.%m.%Y %H:%M")
        else:
            return value.strftime("%d.%m.%Y")
    
    @staticmethod
    def format_status(status: str) -> Dict[str, str]:
        """Форматирует статусы с иконками и цветами"""
        status_map = {
            'active': {'icon': '✅', 'color': '#10b981', 'label': 'Активный'},
            'completed': {'icon': '✓', 'color': '#3b82f6', 'label': 'Завершен'},
            'suspended': {'icon': '⏸️', 'color': '#f59e0b', 'label': 'Приостановлен'},
            'cancelled': {'icon': '❌', 'color': '#ef4444', 'label': 'Отменен'},
            'overdue': {'icon': '⚠️', 'color': '#dc2626', 'label': 'Просрочен'},
            'pending': {'icon': '⏳', 'color': '#6b7280', 'label': 'Ожидает'},
        }
        
        return status_map.get(status.lower(), {
            'icon': '❓', 
            'color': '#6b7280', 
            'label': status.title()
        })


class StyleGenerator:
    """Генератор стилей"""
    
    @staticmethod
    def get_gradient_colors(scheme: str = "blue") -> Dict[str, str]:
        """Возвращает градиентные цвета для схемы"""
        gradients = {
            'blue': {
                'primary': 'linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)',
                'light': 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%)',
                'bg': '#eff6ff',
                'border': '#3b82f6',
                'text': '#1e40af'
            },
            'green': {
                'primary': 'linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)',
                'light': 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 50%, #86efac 100%)',
                'bg': '#eff6ff',
                'border': '#2563eb',
                'text': '#1e40af'
            },
            'red': {
                'primary': 'linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%)',
                'light': 'linear-gradient(135deg, #fee2e2 0%, #fecaca 50%, #fca5a5 100%)',
                'bg': '#eff6ff',
                'border': '#93c5fd',
                'text': '#1e40af'
            },
            'orange': {
                'primary': 'linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%)',
                'light': 'linear-gradient(135deg, #fed7aa 0%, #fdba74 50%, #fb923c 100%)',
                'bg': '#eff6ff',
                'border': '#60a5fa',
                'text': '#1e40af'
            }
        }
        
        return gradients.get(scheme, gradients['blue'])
    
    @staticmethod
    def get_animation_css(animation_type: str = "fadeInUp") -> str:
        """Возвращает CSS для анимаций"""
        animations = {
            'fadeInUp': """
                animation: fadeInUp 0.6s ease-out forwards;
                opacity: 0;
                
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            """,
            'slideInLeft': """
                animation: slideInLeft 0.5s ease-out forwards;
                opacity: 0;
                
                @keyframes slideInLeft {
                    from {
                        opacity: 0;
                        transform: translateX(-30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
            """,
            'pulse': """
                animation: pulse 2s infinite;
                
                @keyframes pulse {
                    0%, 100% {
                        opacity: 1;
                    }
                    50% {
                        opacity: 0.7;
                    }
                }
            """,
            'shine': """
                position: relative;
                overflow: hidden;
                
                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                    animation: shine 2s infinite;
                }
                
                @keyframes shine {
                    0% { left: -100%; }
                    100% { left: 100%; }
                }
            """
        }
        
        return animations.get(animation_type, animations['fadeInUp'])
    
    @staticmethod
    def get_responsive_css() -> str:
        """Возвращает адаптивные стили"""
        return """
        @media (max-width: 768px) {
            .responsive-component {
                padding: 1rem !important;
                font-size: 0.9rem !important;
            }
            
            .responsive-grid {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }
        }
        
        @media (max-width: 480px) {
            .responsive-component {
                padding: 0.75rem !important;
                font-size: 0.8rem !important;
            }
            
            .responsive-text {
                font-size: 0.875rem !important;
            }
        }
        """


class ComponentRenderer:
    """Рендерер компонентов"""
    
    @staticmethod
    def render_with_error_handling(render_func: Callable, *args, **kwargs) -> None:
        """Выполняет рендеринг с обработкой ошибок"""
        try:
            render_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Ошибка рендеринга компонента: {e}")
            st.error(f"Ошибка отображения компонента: {str(e)}")
    
    @staticmethod
    def inject_css(css_content: str) -> None:
        """Внедряет CSS стили"""
        st.markdown(f"<style>{css_content}</style>", unsafe_allow_html=True)
    
    @staticmethod
    def create_html_element(
        tag: str,
        content: str = "",
        attributes: Optional[Dict[str, str]] = None,
        styles: Optional[Dict[str, str]] = None
    ) -> str:
        """Создает HTML элемент"""
        attrs = []
        
        if attributes:
            for key, value in attributes.items():
                attrs.append(f'{key}="{value}"')
        
        if styles:
            style_str = "; ".join([f"{key}: {value}" for key, value in styles.items()])
            attrs.append(f'style="{style_str}"')
        
        attrs_str = " " + " ".join(attrs) if attrs else ""
        
        if content:
            return f"<{tag}{attrs_str}>{content}</{tag}>"
        else:
            return f"<{tag}{attrs_str}/>"


# Глобальные экземпляры утилит
validator = ComponentValidator()
formatter = DataFormatter()
style_generator = StyleGenerator()
renderer = ComponentRenderer()
