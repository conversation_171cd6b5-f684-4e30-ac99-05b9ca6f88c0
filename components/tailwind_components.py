"""
Компоненты с использованием Tailwind CSS для Streamlit
"""
import streamlit as st
from typing import Optional, Dict, Any


class TailwindComponents:
    """Класс для создания компонентов с Tailwind CSS"""
    
    @staticmethod
    def metric_card(label: str, value: str, delta: Optional[str] = None, 
                   status_class: str = "", icon: str = "") -> None:
        """
        Создает карточку метрики с Tailwind CSS
        
        Args:
            label: Название метрики
            value: Значение метрики
            delta: Дополнительная информация
            status_class: CSS класс для статуса (tw-status-active, tw-status-overdue, etc.)
            icon: Эмодзи иконка
        """
        delta_html = f'<div class="tw-metric-delta">{icon} {delta}</div>' if delta else ""
        
        card_html = f"""
        <div class="tw-metric-card">
            <div class="tw-metric-label">{label}</div>
            <div class="tw-metric-value {status_class}">{value}</div>
            {delta_html}
        </div>
        """
        st.markdown(card_html, unsafe_allow_html=True)
    
    @staticmethod
    def section_header(title: str, icon: str = "") -> None:
        """
        Создает заголовок секции с Tailwind CSS
        
        Args:
            title: Текст заголовка
            icon: Эмодзи иконка
        """
        header_html = f'<div class="tw-section-header">{icon} {title}</div>'
        st.markdown(header_html, unsafe_allow_html=True)
    
    @staticmethod
    def official_header(title: str, subtitle: str = "") -> None:
        """
        Создает официальный заголовок с Tailwind CSS
        
        Args:
            title: Основной заголовок
            subtitle: Подзаголовок
        """
        subtitle_html = f'<div class="tw-official-subtitle">{subtitle}</div>' if subtitle else ""
        
        header_html = f"""
        <div class="tw-official-header">
            <h1 class="tw-official-title">{title}</h1>
            {subtitle_html}
        </div>
        """
        st.markdown(header_html, unsafe_allow_html=True)
    
    @staticmethod
    def progress_bar(label: str, value: float, max_value: float = 100, 
                    color_class: str = "bg-blue-500") -> None:
        """
        Создает прогресс-бар с Tailwind CSS
        
        Args:
            label: Название прогресс-бара
            value: Текущее значение
            max_value: Максимальное значение
            color_class: Tailwind класс для цвета
        """
        percentage = (value / max_value * 100) if max_value > 0 else 0
        
        progress_html = f"""
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">{label}</span>
                <span class="text-sm text-gray-500">{value} ({percentage:.1f}%)</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="{color_class} h-2 rounded-full transition-all duration-300" 
                     style="width: {percentage}%"></div>
            </div>
        </div>
        """
        st.markdown(progress_html, unsafe_allow_html=True)
    
    @staticmethod
    def info_panel(title: str, content: str, icon: str = "ℹ️") -> None:
        """
        Создает информационную панель с Tailwind CSS
        
        Args:
            title: Заголовок панели
            content: Содержимое панели
            icon: Иконка
        """
        panel_html = f"""
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-xl shadow-lg mb-4">
            <h3 class="text-lg font-semibold mb-3 flex items-center">
                <span class="mr-2">{icon}</span>
                {title}
            </h3>
            <div class="text-sm opacity-90">
                {content}
            </div>
        </div>
        """
        st.markdown(panel_html, unsafe_allow_html=True)
    
    @staticmethod
    def status_badge(status: str, text: str = None) -> str:
        """
        Создает бейдж статуса с Tailwind CSS
        
        Args:
            status: Статус (active, completed, overdue, suspended)
            text: Текст для отображения (по умолчанию используется status)
        
        Returns:
            HTML строка с бейджем
        """
        if text is None:
            text = status
        
        status_classes = {
            'active': 'bg-green-100 text-green-800 border-green-200',
            'completed': 'bg-gray-100 text-gray-800 border-gray-200',
            'overdue': 'bg-red-100 text-red-800 border-red-200',
            'suspended': 'bg-yellow-100 text-yellow-800 border-yellow-200',
            'cancelled': 'bg-red-100 text-red-800 border-red-200'
        }
        
        css_class = status_classes.get(status, 'bg-gray-100 text-gray-800 border-gray-200')
        
        return f"""
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {css_class}">
            {text}
        </span>
        """
    
    @staticmethod
    def responsive_grid(items: list, columns: int = 4) -> None:
        """
        Создает адаптивную сетку с Tailwind CSS
        
        Args:
            items: Список элементов для отображения
            columns: Количество колонок на больших экранах
        """
        # Определяем Tailwind классы для разных размеров экрана
        grid_classes = {
            1: "grid-cols-1",
            2: "grid-cols-1 md:grid-cols-2",
            3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
            4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
            5: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5"
        }
        
        grid_class = grid_classes.get(columns, "grid-cols-1 md:grid-cols-2 lg:grid-cols-4")
        
        grid_html = f"""
        <div class="grid {grid_class} gap-4 mb-6">
            {''.join(items)}
        </div>
        """
        st.markdown(grid_html, unsafe_allow_html=True)
    
    @staticmethod
    def card_container(content: str, hover_effect: bool = True) -> str:
        """
        Создает контейнер карточки с Tailwind CSS
        
        Args:
            content: HTML содержимое карточки
            hover_effect: Включить эффект при наведении
        
        Returns:
            HTML строка с карточкой
        """
        hover_class = "hover:-translate-y-1 hover:shadow-xl" if hover_effect else ""
        
        return f"""
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 h-full {hover_class}">
            {content}
        </div>
        """
    
    @staticmethod
    def button_group(buttons: list) -> None:
        """
        Создает группу кнопок с Tailwind CSS
        
        Args:
            buttons: Список словарей с параметрами кнопок
                    [{'text': 'Button', 'onclick': 'function()', 'class': 'bg-blue-500'}]
        """
        buttons_html = []
        for button in buttons:
            btn_class = button.get('class', 'bg-blue-500 hover:bg-blue-600')
            onclick = button.get('onclick', '')
            text = button.get('text', 'Button')
            
            buttons_html.append(f"""
            <button onclick="{onclick}" 
                    class="px-4 py-2 {btn_class} text-white rounded-lg font-medium transition-colors duration-200 mr-2 mb-2">
                {text}
            </button>
            """)
        
        group_html = f"""
        <div class="flex flex-wrap items-center gap-2 mb-4">
            {''.join(buttons_html)}
        </div>
        """
        st.markdown(group_html, unsafe_allow_html=True)


# Глобальный экземпляр компонентов
tw_components = TailwindComponents()
