"""
Компоненты для строгого и функционального дизайна дашборда
"""
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime, date
from typing import Dict, Any, List, Optional
import numpy as np

from config.settings import COLOR_SCHEME


class DashboardComponents:
    """Класс для создания строгих и функциональных компонентов дашборда"""
    
    def __init__(self):
        self.colors = COLOR_SCHEME
        # Единая синяя цветовая схема для всех виджетов
        self.primary_color = "#3b82f6"    # Основной синий
        self.success_color = "#2563eb"    # Синий для успеха
        self.warning_color = "#60a5fa"    # Светло-синий для предупреждений
        self.danger_color = "#1e40af"     # Темно-синий для опасности
        self.info_color = "#93c5fd"       # Очень светло-синий для информации
        self.gray_color = "#1d4ed8"       # Темно-синий вместо серого
    
    def render_header(self):
        """Отрисовывает строгий заголовок дашборда"""
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            margin: -1rem -1rem 2rem -1rem;
            text-align: center;
            border-bottom: 4px solid #2563eb;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        ">
            <h1 style="
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                color: #ffffff;
            ">📊 Дашборд освоения договоров</h1>
            <p style="
                font-size: 1.1rem;
                margin: 0.5rem 0 0 0;
                opacity: 0.95;
                font-weight: 300;
                color: #e0f2fe;
            ">Система мониторинга и анализа выполнения государственных контрактов</p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_filter_panel(self, contracts_df: pd.DataFrame) -> Dict[str, Any]:
        """Отрисовывает панель фильтров в верхней части"""
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #3b82f6;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        ">
            <h3 style="
                color: #1e40af;
                font-size: 1.3rem;
                font-weight: 700;
                margin: 0 0 1rem 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            ">🔍 Фильтры и настройки</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Первая строка фильтров - основные параметры
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            status_options = ['Все'] + list(contracts_df['status'].unique()) if not contracts_df.empty else ['Все']
            status_filter = st.selectbox(
                "📊 Статус договора",
                options=status_options,
                key="dashboard_status_filter"
            )
        
        with col2:
            type_options = ['Все'] + list(contracts_df['contract_type'].unique()) if not contracts_df.empty else ['Все']
            type_filter = st.selectbox(
                "🏗️ Тип договора",
                options=type_options,
                key="dashboard_type_filter"
            )
        
        with col3:
            period_filter = st.selectbox(
                "📅 Период анализа",
                options=['Текущий год', 'Последние 12 месяцев', 'Последние 6 месяцев', 'Текущий квартал'],
                key="dashboard_period_filter"
            )
        
        with col4:
            amount_filter = st.selectbox(
                "💰 Диапазон сумм",
                options=['Все суммы', 'До 10 млн', '10-50 млн', '50-100 млн', 'Свыше 100 млн'],
                key="dashboard_amount_filter"
            )
        
        # Вторая строка - дополнительные фильтры
        col5, col6, col7, col8 = st.columns(4)
        
        with col5:
            search_query = st.text_input(
                "🔍 Поиск по названию",
                placeholder="Введите название договора...",
                key="dashboard_search_filter"
            )
        
        with col6:
            contractor_filter = st.text_input(
                "🏢 Подрядчик",
                placeholder="Название подрядчика...",
                key="dashboard_contractor_filter"
            )
        
        with col7:
            sort_filter = st.selectbox(
                "📈 Сортировка",
                options=['По дате создания', 'По сумме (убыв.)', 'По названию', 'По сроку окончания'],
                key="dashboard_sort_filter"
            )
        
        with col8:
            show_charts = st.checkbox(
                "📊 Показать графики",
                value=True,
                key="dashboard_show_charts"
            )
        
        return {
            'status_filter': status_filter,
            'type_filter': type_filter,
            'period_filter': period_filter,
            'amount_filter': amount_filter,
            'search_query': search_query,
            'contractor_filter': contractor_filter,
            'sort_filter': sort_filter,
            'show_charts': show_charts
        }
    
    def render_key_metrics(self, summary: Dict[str, Any]):
        """Отрисовывает ключевые показатели в строгом стиле"""
        st.markdown("""
        <div style="margin: 2rem 0;">
            <h2 style="
                color: #1e40af;
                font-size: 1.6rem;
                font-weight: 700;
                margin: 0 0 1.5rem 0;
                border-bottom: 3px solid #3b82f6;
                padding-bottom: 0.5rem;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                background: linear-gradient(90deg, #1e40af, #3b82f6);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            ">📈 Ключевые показатели</h2>
        </div>
        """, unsafe_allow_html=True)
        
        # Первая строка метрик
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            self._render_metric_card(
                title="Всего договоров",
                value=f"{int(summary.get('total_contracts', 0)):,}",
                icon="📋",
                color=self.primary_color,
                subtitle="В системе"
            )
        
        with col2:
            active_contracts = int(summary.get('active_contracts', 0))
            total_contracts = int(summary.get('total_contracts', 1))
            active_percentage = (active_contracts / total_contracts * 100) if total_contracts > 0 else 0
            self._render_metric_card(
                title="Активные договоры",
                value=f"{active_contracts:,}",
                icon="✅",
                color=self.success_color,
                subtitle=f"{active_percentage:.1f}% от общего числа"
            )
        
        with col3:
            total_amount = summary.get('total_amount', 0)
            self._render_metric_card(
                title="Общая сумма",
                value=f"{total_amount:,.0f} ₽",
                icon="💰",
                color=self.primary_color,
                subtitle="Всех договоров"
            )
        
        with col4:
            overdue_count = int(summary.get('overdue_contracts', 0))
            overdue_rate = summary.get('overdue_rate', 0)
            color = self.danger_color if overdue_count > 0 else self.success_color
            self._render_metric_card(
                title="Просроченные",
                value=f"{overdue_count:,}",
                icon="⚠️" if overdue_count > 0 else "✓",
                color=color,
                subtitle=f"{overdue_rate:.1f}% от активных"
            )
    
    def _render_metric_card(self, title: str, value: str, icon: str, color: str, subtitle: str = ""):
        """Отрисовывает карточку метрики в синем градиентном стиле"""
        # Определяем градиент на основе цвета
        if color == "#3b82f6":  # primary
            gradient = "linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)"
        elif color == "#2563eb":  # success
            gradient = "linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)"
        elif color == "#60a5fa":  # warning
            gradient = "linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%)"
        elif color == "#1e40af":  # danger
            gradient = "linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%)"
        else:  # default
            gradient = "linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)"

        st.markdown(f"""
        <div style="
            background: {gradient};
            border: 2px solid #3b82f6;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
            height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #93c5fd, #3b82f6, #1d4ed8);
            "></div>
            <div style="font-size: 2.2rem; margin-bottom: 0.5rem; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));">{icon}</div>
            <div style="
                font-size: 2rem;
                font-weight: 800;
                color: white;
                margin-bottom: 0.25rem;
                line-height: 1;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            ">{value}</div>
            <div style="
                font-size: 0.95rem;
                font-weight: 700;
                color: rgba(255,255,255,0.9);
                margin-bottom: 0.25rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            ">{title}</div>
            <div style="
                font-size: 0.8rem;
                color: rgba(255,255,255,0.7);
                line-height: 1.2;
                font-weight: 500;
            ">{subtitle}</div>
        </div>
        """, unsafe_allow_html=True)


    def render_progress_speedometer(self, completed_amount: float, total_amount: float):
        """Отрисовывает спидометр освоения договоров"""
        if total_amount == 0:
            completion_rate = 0
        else:
            completion_rate = (completed_amount / total_amount) * 100

        # Определяем цвет на основе процента выполнения с более яркими цветами
        if completion_rate >= 90:
            color = "#10b981"  # Яркий зеленый
            status_text = "Отличное выполнение"
        elif completion_rate >= 70:
            color = "#3b82f6"  # Яркий синий
            status_text = "Хорошее выполнение"
        elif completion_rate >= 50:
            color = "#f59e0b"  # Яркий оранжевый
            status_text = "Требует внимания"
        else:
            color = "#ef4444"  # Яркий красный
            status_text = "Критический уровень"

        # Создаем спидометр с помощью Plotly с улучшенными цветами
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = completion_rate,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {
                'text': "Процент освоения договоров",
                'font': {'size': 22, 'color': '#1e40af', 'family': 'Arial Black'}
            },
            delta = {'reference': 80, 'increasing': {'color': '#10b981'}, 'decreasing': {'color': '#ef4444'}},
            number = {'font': {'size': 48, 'color': color, 'family': 'Arial Black'}},
            gauge = {
                'axis': {
                    'range': [None, 100],
                    'tickwidth': 2,
                    'tickcolor': "#1e40af",
                    'tickfont': {'size': 14, 'color': '#1e40af', 'family': 'Arial'}
                },
                'bar': {'color': color, 'thickness': 0.8},
                'bgcolor': "#f8fafc",
                'borderwidth': 3,
                'bordercolor': "#3b82f6",
                'steps': [
                    {'range': [0, 50], 'color': '#fecaca'},    # Светло-красный
                    {'range': [50, 70], 'color': '#fed7aa'},   # Светло-оранжевый
                    {'range': [70, 90], 'color': '#bfdbfe'},   # Светло-синий
                    {'range': [90, 100], 'color': '#bbf7d0'}   # Светло-зеленый
                ],
                'threshold': {
                    'line': {'color': "#ef4444", 'width': 6},
                    'thickness': 0.8,
                    'value': 90
                }
            }
        ))

        fig.update_layout(
            height=400,
            font={'color': "#1e40af", 'family': "Arial", 'size': 12},
            paper_bgcolor='#ffffff',
            plot_bgcolor='#f8fafc',
            margin=dict(l=20, r=20, t=60, b=20)
        )

        st.plotly_chart(fig, use_container_width=True)

        # Дополнительная информация под спидометром
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric(
                label="Освоено средств",
                value=f"{completed_amount:,.0f} ₽",
                delta=f"{completion_rate:.1f}%"
            )
        with col2:
            st.metric(
                label="Общая сумма",
                value=f"{total_amount:,.0f} ₽",
                delta=status_text
            )
        with col3:
            remaining_amount = total_amount - completed_amount
            st.metric(
                label="Осталось освоить",
                value=f"{remaining_amount:,.0f} ₽",
                delta=f"{100-completion_rate:.1f}%"
            )

    def render_quarterly_chart(self, contracts_df: pd.DataFrame):
        """Отрисовывает график изменения суммы освоения по кварталам"""
        if contracts_df.empty:
            st.warning("Нет данных для отображения квартального графика")
            return

        # Подготавливаем данные по кварталам
        contracts_df['start_date'] = pd.to_datetime(contracts_df['start_date'])
        contracts_df['quarter'] = contracts_df['start_date'].dt.to_period('Q')

        # Группируем по кварталам
        quarterly_data = contracts_df.groupby('quarter').agg({
            'total_amount': 'sum',
            'contract_id': 'count'
        }).reset_index()

        quarterly_data['quarter_str'] = quarterly_data['quarter'].astype(str)
        quarterly_data = quarterly_data.sort_values('quarter')

        # Создаем комбинированный график
        fig = make_subplots(
            specs=[[{"secondary_y": True}]],
            subplot_titles=["Динамика освоения средств по кварталам"]
        )

        # Столбчатая диаграмма для сумм с градиентом
        fig.add_trace(
            go.Bar(
                x=quarterly_data['quarter_str'],
                y=quarterly_data['total_amount'],
                name='Сумма договоров',
                marker=dict(
                    color='#3b82f6',
                    line=dict(color='#1e40af', width=2),
                    opacity=0.9
                ),
                text=[f"{val:,.0f} ₽" for val in quarterly_data['total_amount']],
                textposition='outside',
                textfont=dict(color='#1e40af', size=12, family='Arial')
            ),
            secondary_y=False,
        )

        # Линейный график для количества договоров
        fig.add_trace(
            go.Scatter(
                x=quarterly_data['quarter_str'],
                y=quarterly_data['contract_id'],
                mode='lines+markers',
                name='Количество договоров',
                line=dict(color='#10b981', width=4),
                marker=dict(size=10, color='#10b981', line=dict(color='#059669', width=2))
            ),
            secondary_y=True,
        )

        # Настройка осей с улучшенными цветами
        fig.update_xaxes(
            title_text="Квартал",
            title_font=dict(size=14, color='#1e40af', family='Arial'),
            tickfont=dict(size=12, color='#374151')
        )
        fig.update_yaxes(
            title_text="Сумма договоров (₽)",
            secondary_y=False,
            title_font=dict(size=14, color='#3b82f6', family='Arial'),
            tickfont=dict(size=12, color='#3b82f6')
        )
        fig.update_yaxes(
            title_text="Количество договоров",
            secondary_y=True,
            title_font=dict(size=14, color='#10b981', family='Arial'),
            tickfont=dict(size=12, color='#10b981')
        )

        # Общие настройки с улучшенным дизайном
        fig.update_layout(
            height=500,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1,
                font=dict(size=12, color='#374151', family='Arial')
            ),
            template='plotly_white',
            title={
                'text': 'Квартальная динамика освоения договоров',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': '#1e40af', 'family': 'Arial Black'}
            },
            plot_bgcolor='#f8fafc',
            paper_bgcolor='#ffffff',
            font=dict(family='Arial', size=12, color='#374151')
        )

        st.plotly_chart(fig, use_container_width=True)

        # Дополнительная статистика по кварталам
        if len(quarterly_data) > 1:
            st.markdown("### 📊 Квартальная статистика")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                avg_quarterly_amount = quarterly_data['total_amount'].mean()
                st.metric(
                    label="Средняя сумма за квартал",
                    value=f"{avg_quarterly_amount:,.0f} ₽"
                )

            with col2:
                max_quarter = quarterly_data.loc[quarterly_data['total_amount'].idxmax()]
                st.metric(
                    label="Максимум",
                    value=f"{max_quarter['total_amount']:,.0f} ₽",
                    delta=f"{max_quarter['quarter_str']}"
                )

            with col3:
                min_quarter = quarterly_data.loc[quarterly_data['total_amount'].idxmin()]
                st.metric(
                    label="Минимум",
                    value=f"{min_quarter['total_amount']:,.0f} ₽",
                    delta=f"{min_quarter['quarter_str']}"
                )

            with col4:
                if len(quarterly_data) >= 2:
                    growth_rate = ((quarterly_data['total_amount'].iloc[-1] / quarterly_data['total_amount'].iloc[-2]) - 1) * 100
                    st.metric(
                        label="Рост к пред. кварталу",
                        value=f"{growth_rate:+.1f}%",
                        delta="Изменение"
                    )

    def render_contractor_analysis(self, contracts_df: pd.DataFrame):
        """Отрисовывает анализ по подрядчикам"""
        if contracts_df.empty:
            st.warning("Нет данных для анализа подрядчиков")
            return

        st.markdown("""
        <h2 style="
            color: #1e40af;
            font-size: 1.6rem;
            font-weight: 700;
            margin: 2rem 0 1.5rem 0;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            background: linear-gradient(90deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        ">🏢 Анализ по подрядчикам</h2>
        """, unsafe_allow_html=True)

        # Группируем по подрядчикам
        contractor_stats = contracts_df.groupby('contractor_name').agg({
            'contract_id': 'count',
            'total_amount': 'sum'
        }).reset_index()

        contractor_stats.columns = ['contractor_name', 'contracts_count', 'total_amount']
        contractor_stats = contractor_stats.sort_values('total_amount', ascending=False).head(10)

        col1, col2 = st.columns(2)

        with col1:
            # Топ подрядчиков по сумме с улучшенными цветами
            fig = go.Figure()
            fig.add_trace(go.Bar(
                y=contractor_stats['contractor_name'],
                x=contractor_stats['total_amount'],
                orientation='h',
                marker=dict(
                    color='#3b82f6',
                    line=dict(color='#1e40af', width=2),
                    opacity=0.9
                ),
                text=[f"{val:,.0f} ₽" for val in contractor_stats['total_amount']],
                textposition='outside',
                textfont=dict(color='#1e40af', size=11, family='Arial')
            ))

            fig.update_layout(
                title={
                    'text': "Топ-10 подрядчиков по сумме договоров",
                    'font': {'size': 16, 'color': '#1e40af', 'family': 'Arial Black'}
                },
                xaxis_title="Сумма договоров (₽)",
                yaxis_title="Подрядчик",
                height=400,
                template='plotly_white',
                plot_bgcolor='#f8fafc',
                paper_bgcolor='#ffffff',
                font=dict(family='Arial', size=11, color='#374151')
            )

            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Топ подрядчиков по количеству с улучшенными цветами
            fig2 = go.Figure()
            fig2.add_trace(go.Bar(
                y=contractor_stats['contractor_name'],
                x=contractor_stats['contracts_count'],
                orientation='h',
                marker=dict(
                    color='#10b981',
                    line=dict(color='#059669', width=2),
                    opacity=0.9
                ),
                text=contractor_stats['contracts_count'],
                textposition='outside',
                textfont=dict(color='#059669', size=11, family='Arial')
            ))

            fig2.update_layout(
                title={
                    'text': "Топ-10 подрядчиков по количеству договоров",
                    'font': {'size': 16, 'color': '#1e40af', 'family': 'Arial Black'}
                },
                xaxis_title="Количество договоров",
                yaxis_title="Подрядчик",
                height=400,
                template='plotly_white',
                plot_bgcolor='#f8fafc',
                paper_bgcolor='#ffffff',
                font=dict(family='Arial', size=11, color='#374151')
            )

            st.plotly_chart(fig2, use_container_width=True)

    def render_risk_analysis(self, contracts_df: pd.DataFrame):
        """Отрисовывает анализ рисков"""
        if contracts_df.empty:
            return

        st.markdown("""
        <h2 style="
            color: #1e40af;
            font-size: 1.6rem;
            font-weight: 700;
            margin: 2rem 0 1.5rem 0;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            background: linear-gradient(90deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        ">⚠️ Анализ рисков</h2>
        """, unsafe_allow_html=True)

        # Анализ просроченных договоров
        today = pd.Timestamp.now().date()
        contracts_df['end_date_dt'] = pd.to_datetime(contracts_df['end_date']).dt.date

        overdue_contracts = contracts_df[
            (contracts_df['end_date_dt'] < today) &
            (contracts_df['status'] == 'active')
        ]

        expiring_soon = contracts_df[
            (contracts_df['end_date_dt'] >= today) &
            (contracts_df['end_date_dt'] <= today + pd.Timedelta(days=30)) &
            (contracts_df['status'] == 'active')
        ]

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            self._render_risk_metric(
                title="Просроченные",
                value=len(overdue_contracts),
                total=len(contracts_df),
                color="#ef4444",  # Яркий красный
                icon="🚨"
            )

        with col2:
            self._render_risk_metric(
                title="Истекают в течение месяца",
                value=len(expiring_soon),
                total=len(contracts_df),
                color="#f59e0b",  # Яркий оранжевый
                icon="⏰"
            )

        with col3:
            high_value_contracts = contracts_df[contracts_df['total_amount'] > 50000000]
            self._render_risk_metric(
                title="Крупные договоры",
                value=len(high_value_contracts),
                total=len(contracts_df),
                color="#3b82f6",  # Яркий синий
                icon="💎"
            )

        with col4:
            suspended_contracts = contracts_df[contracts_df['status'] == 'suspended']
            self._render_risk_metric(
                title="Приостановленные",
                value=len(suspended_contracts),
                total=len(contracts_df),
                color="#6b7280",  # Серый
                icon="⏸️"
            )

    def _render_risk_metric(self, title: str, value: int, total: int, color: str, icon: str):
        """Отрисовывает метрику риска в синем градиентном стиле"""
        percentage = (value / total * 100) if total > 0 else 0

        # Определяем градиент на основе значения
        if percentage > 50:
            gradient = "linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%)"
        elif percentage > 20:
            gradient = "linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%)"
        else:
            gradient = "linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)"

        st.markdown(f"""
        <div style="
            background: {gradient};
            border: 2px solid #3b82f6;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
            height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #93c5fd, #3b82f6, #1d4ed8);
            "></div>
            <div style="font-size: 2rem; margin-bottom: 0.5rem; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));">{icon}</div>
            <div style="
                font-size: 1.8rem;
                font-weight: 800;
                color: white;
                margin-bottom: 0.25rem;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            ">{value}</div>
            <div style="
                font-size: 0.85rem;
                font-weight: 700;
                color: rgba(255,255,255,0.9);
                margin-bottom: 0.25rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            ">{title}</div>
            <div style="
                font-size: 0.75rem;
                color: rgba(255,255,255,0.7);
                font-weight: 500;
            ">{percentage:.1f}% от общего числа</div>
        </div>
        """, unsafe_allow_html=True)


# Глобальный экземпляр компонентов дашборда
dashboard_components = DashboardComponents()
