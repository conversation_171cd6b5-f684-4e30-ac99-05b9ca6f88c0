"""
Современные визуальные компоненты для дашборда с улучшенным дизайном
"""
import streamlit as st
from typing import Optional, Dict, Any, List
import time


class ModernComponents:
    """Класс для создания современных визуальных компонентов"""
    
    @staticmethod
    def enhanced_metric_card(
        label: str,
        value: str,
        delta: Optional[str] = None,
        icon: str = "📊",
        color_scheme: str = "blue",
        trend: Optional[str] = None,
        animation_delay: float = 0.0
    ) -> None:
        """
        Создает улучшенную карточку метрики с градиентами и анимациями
        
        Args:
            label: Название метрики
            value: Значение метрики
            delta: Дополнительная информация
            icon: Иконка метрики
            color_scheme: Цветовая схема (blue, green, red, purple, orange)
            trend: Тренд (up, down, stable)
            animation_delay: Задержка анимации в секундах
        """
        
        # Единая синяя цветовая схема для всех вариантов
        color_schemes = {
            'blue': {
                'gradient': 'from-blue-500 via-blue-600 to-blue-700',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900',
                'icon_bg': 'bg-blue-100',
                'shadow': 'shadow-blue-200/50'
            },
            'green': {
                'gradient': 'from-blue-400 via-blue-500 to-blue-600',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900',
                'icon_bg': 'bg-blue-100',
                'shadow': 'shadow-blue-200/50'
            },
            'red': {
                'gradient': 'from-blue-600 via-blue-700 to-blue-800',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900',
                'icon_bg': 'bg-blue-100',
                'shadow': 'shadow-blue-200/50'
            },
            'purple': {
                'gradient': 'from-blue-300 via-blue-400 to-blue-500',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900',
                'icon_bg': 'bg-blue-100',
                'shadow': 'shadow-blue-200/50'
            },
            'orange': {
                'gradient': 'from-blue-500 via-blue-600 to-blue-700',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900',
                'icon_bg': 'bg-blue-100',
                'shadow': 'shadow-blue-200/50'
            }
        }
        
        scheme = color_schemes.get(color_scheme, color_schemes['blue'])
        
        # Иконки трендов
        trend_icons = {
            'up': '📈',
            'down': '📉',
            'stable': '➡️'
        }
        
        trend_icon = trend_icons.get(trend, '') if trend else ''
        delta_html = f'<div class="flex items-center gap-1 text-sm text-gray-600 mt-2">{trend_icon} {delta}</div>' if delta else ""
        
        # Единая синяя градиентная схема для всех вариантов
        gradient_colors = {
            'blue': 'linear-gradient(135deg, #3b82f6, #1e40af, #1d4ed8)',
            'green': 'linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8)',
            'red': 'linear-gradient(135deg, #93c5fd, #60a5fa, #3b82f6)',
            'purple': 'linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb)',
            'orange': 'linear-gradient(135deg, #3b82f6, #1e40af, #1d4ed8)'
        }

        gradient = gradient_colors.get(color_scheme, gradient_colors['blue'])

        card_html = f"""
        <div class="modern-metric-card" style="animation-delay: {animation_delay}s;">
            <!-- Градиентная полоска сверху -->
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: {gradient}; border-radius: 1rem 1rem 0 0;"></div>

            <!-- Иконка в углу -->
            <div style="position: absolute; top: 1rem; right: 1rem; width: 3rem; height: 3rem; background: rgba(59, 130, 246, 0.1); border-radius: 0.75rem; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                {icon}
            </div>

            <!-- Основной контент -->
            <div style="padding-top: 1.5rem; padding-bottom: 1rem;">
                <div style="font-size: 0.875rem; font-weight: 500; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.5rem;">
                    {label}
                </div>
                <div style="font-size: 2rem; font-weight: 700; color: #1f2937; margin-bottom: 0.25rem;">
                    {value}
                </div>
                {delta_html}
            </div>
        </div>
        """
        
        st.markdown(card_html, unsafe_allow_html=True)
    
    @staticmethod
    def info_panel_modern(
        title: str,
        content: str,
        panel_type: str = "info",
        icon: str = "ℹ️",
        collapsible: bool = False
    ) -> None:
        """
        Создает современную информационную панель
        
        Args:
            title: Заголовок панели
            content: Содержимое панели
            panel_type: Тип панели (info, success, warning, error)
            icon: Иконка панели
            collapsible: Сделать панель сворачиваемой
        """
        
        panel_styles = {
            'info': {
                'gradient': 'from-blue-500 to-blue-700',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900'
            },
            'success': {
                'gradient': 'from-blue-400 to-blue-600',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900'
            },
            'warning': {
                'gradient': 'from-blue-300 to-blue-500',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900'
            },
            'error': {
                'gradient': 'from-blue-600 to-blue-800',
                'bg': 'bg-blue-50',
                'border': 'border-blue-200',
                'text': 'text-blue-900'
            }
        }
        
        style = panel_styles.get(panel_type, panel_styles['info'])
        
        if collapsible:
            with st.expander(f"{icon} {title}", expanded=True):
                st.markdown(f"""
                <div class="modern-info-panel {style['bg']} {style['border']} {style['text']}">
                    {content}
                </div>
                """, unsafe_allow_html=True)
        else:
            panel_html = f"""
            <div class="modern-info-panel {style['bg']} {style['border']} {style['text']}">
                <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-gradient-to-r {style['gradient']} rounded-xl flex items-center justify-center text-white text-lg shadow-lg">
                        {icon}
                    </div>
                    <h3 class="text-lg font-semibold {style['text']}">{title}</h3>
                </div>
                <div class="text-sm leading-relaxed">
                    {content}
                </div>
            </div>
            """
            st.markdown(panel_html, unsafe_allow_html=True)
    
    @staticmethod
    def section_divider(title: str, icon: str = "", subtitle: str = "") -> None:
        """
        Создает современный разделитель секций

        Args:
            title: Заголовок секции
            icon: Иконка секции
            subtitle: Подзаголовок секции
        """
        subtitle_html = f'<p style="color: #6b7280; font-size: 0.875rem; margin-top: 0.25rem;">{subtitle}</p>' if subtitle else ""

        divider_html = f"""
        <div style="margin: 2rem 0;">
            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #6366f1, #8b5cf6); border-radius: 1rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
                    {icon}
                </div>
                <div>
                    <h2 style="font-size: 1.5rem; font-weight: 700; color: #1f2937; margin: 0;">{title}</h2>
                    {subtitle_html}
                </div>
            </div>
            <div style="height: 1px; background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899); margin: 1.5rem 0;"></div>
        </div>
        """
        st.markdown(divider_html, unsafe_allow_html=True)
    
    @staticmethod
    def status_badge_enhanced(status: str, text: str = None, size: str = "md") -> str:
        """
        Создает улучшенный статусный бейдж
        
        Args:
            status: Статус (active, completed, overdue, suspended, cancelled)
            text: Текст для отображения
            size: Размер бейджа (sm, md, lg)
        
        Returns:
            HTML строка с бейджем
        """
        if text is None:
            text = status
        
        status_styles = {
            'active': {
                'bg': 'bg-gradient-to-r from-green-400 to-emerald-500',
                'text': 'text-white',
                'shadow': 'shadow-green-200',
                'icon': '✅'
            },
            'completed': {
                'bg': 'bg-gradient-to-r from-blue-400 to-indigo-500',
                'text': 'text-white',
                'shadow': 'shadow-blue-200',
                'icon': '✓'
            },
            'overdue': {
                'bg': 'bg-gradient-to-r from-red-400 to-rose-500',
                'text': 'text-white',
                'shadow': 'shadow-red-200',
                'icon': '⚠️'
            },
            'suspended': {
                'bg': 'bg-gradient-to-r from-yellow-400 to-orange-500',
                'text': 'text-white',
                'shadow': 'shadow-yellow-200',
                'icon': '⏸️'
            },
            'cancelled': {
                'bg': 'bg-gradient-to-r from-gray-400 to-slate-500',
                'text': 'text-white',
                'shadow': 'shadow-gray-200',
                'icon': '❌'
            }
        }
        
        sizes = {
            'sm': 'px-2 py-1 text-xs',
            'md': 'px-3 py-1.5 text-sm',
            'lg': 'px-4 py-2 text-base'
        }
        
        style = status_styles.get(status, status_styles['active'])
        size_class = sizes.get(size, sizes['md'])
        
        return f"""
        <span class="inline-flex items-center gap-1 {size_class} {style['bg']} {style['text']} 
                     rounded-full font-medium shadow-lg {style['shadow']} 
                     transform hover:scale-105 transition-all duration-200">
            <span class="text-xs">{style['icon']}</span>
            {text}
        </span>
        """
    
    @staticmethod
    def progress_indicator_modern(
        label: str,
        current: float,
        target: float,
        color: str = "blue",
        show_percentage: bool = True,
        animated: bool = True
    ) -> None:
        """
        Создает современный индикатор прогресса
        
        Args:
            label: Название индикатора
            current: Текущее значение
            target: Целевое значение
            color: Цвет индикатора
            show_percentage: Показывать процент
            animated: Анимированный прогресс
        """
        percentage = min((current / target * 100) if target > 0 else 0, 100)
        
        color_classes = {
            'blue': 'from-blue-400 to-blue-600',
            'green': 'from-green-400 to-green-600',
            'red': 'from-red-400 to-red-600',
            'purple': 'from-purple-400 to-purple-600',
            'orange': 'from-orange-400 to-orange-600'
        }
        
        gradient = color_classes.get(color, color_classes['blue'])
        animation_class = 'progress-animated' if animated else ''
        
        progress_html = f"""
        <div class="modern-progress-container">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">{label}</span>
                <div class="flex items-center gap-2">
                    <span class="text-sm text-gray-500">{current:,.0f} / {target:,.0f}</span>
                    {f'<span class="text-sm font-semibold text-gray-700">({percentage:.1f}%)</span>' if show_percentage else ''}
                </div>
            </div>
            <div class="relative">
                <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                    <div class="bg-gradient-to-r {gradient} h-3 rounded-full shadow-lg {animation_class} 
                               transition-all duration-1000 ease-out relative overflow-hidden"
                         style="width: {percentage}%">
                        <div class="absolute inset-0 bg-white opacity-20 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>
        </div>
        """
        st.markdown(progress_html, unsafe_allow_html=True)


# Глобальный экземпляр современных компонентов
modern_components = ModernComponents()
