"""
Компоненты для отображения графиков и диаграмм
"""
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from typing import Optional, Dict, Any, List
from datetime import datetime

from config.settings import COLOR_SCHEME, DASHBOARD_CONFIG


class ChartComponents:
    """Класс для создания различных типов графиков"""
    
    def __init__(self):
        self.colors = COLOR_SCHEME
        self.height = DASHBOARD_CONFIG['chart_height']
    
    def monthly_progress_chart(self, df: pd.DataFrame, title: str = "Динамика освоения по месяцам") -> go.Figure:
        """Создает график месячного прогресса"""
        if df.empty:
            return self._empty_chart("Нет данных для отображения")
        
        # Создаем subplot с двумя осями Y
        fig = make_subplots(
            specs=[[{"secondary_y": True}]],
            subplot_titles=[title]
        )
        
        # Добавляем столбчатую диаграмму для сумм
        fig.add_trace(
            go.Bar(
                x=df['month'],
                y=df['planned_amount'],
                name='Запланировано',
                marker_color=self.colors['info'],
                opacity=0.7
            ),
            secondary_y=False,
        )
        
        fig.add_trace(
            go.Bar(
                x=df['month'],
                y=df['completed_amount'],
                name='Выполнено',
                marker_color=self.colors['success'],
                opacity=0.8
            ),
            secondary_y=False,
        )
        
        # Добавляем линию процента выполнения
        fig.add_trace(
            go.Scatter(
                x=df['month'],
                y=df['completion_rate'],
                mode='lines+markers',
                name='% выполнения',
                line=dict(color=self.colors['warning'], width=3),
                marker=dict(size=8),
                yaxis='y2'
            ),
            secondary_y=True,
        )
        
        # Настройка осей
        fig.update_xaxes(title_text="Месяц")
        fig.update_yaxes(title_text="Сумма (₽)", secondary_y=False)
        fig.update_yaxes(title_text="Процент выполнения (%)", secondary_y=True)
        
        # Общие настройки
        fig.update_layout(
            height=self.height,
            showlegend=True,
            hovermode='x unified',
            template='plotly_white'
        )
        
        return fig
    
    def contracts_by_status_pie(self, df: pd.DataFrame, title: str = "Распределение договоров по статусам") -> go.Figure:
        """Создает круговую диаграмму по статусам договоров"""
        if df.empty:
            return self._empty_chart("Нет данных для отображения")
        
        # Подсчитываем количество договоров по статусам
        status_counts = df['status'].value_counts()
        
        # Цвета для разных статусов
        status_colors = {
            'active': self.colors['success'],
            'completed': self.colors['info'],
            'suspended': self.colors['warning'],
            'cancelled': self.colors['warning']
        }
        
        colors = [status_colors.get(status, self.colors['primary']) for status in status_counts.index]
        
        fig = go.Figure(data=[
            go.Pie(
                labels=status_counts.index,
                values=status_counts.values,
                hole=0.4,
                marker_colors=colors,
                textinfo='label+percent+value',
                textposition='outside'
            )
        ])
        
        fig.update_layout(
            title=title,
            height=self.height,
            showlegend=True,
            template='plotly_white'
        )
        
        return fig
    
    def top_contracts_bar(self, df: pd.DataFrame, limit: int = 10, 
                         title: str = "Топ договоров по сумме") -> go.Figure:
        """Создает горизонтальную столбчатую диаграмму топ договоров"""
        if df.empty:
            return self._empty_chart("Нет данных для отображения")
        
        # Сортируем и берем топ
        top_contracts = df.nlargest(limit, 'total_amount')
        
        fig = go.Figure(data=[
            go.Bar(
                y=top_contracts['contract_name'],
                x=top_contracts['total_amount'],
                orientation='h',
                marker_color=self.colors['primary'],
                text=top_contracts['total_amount'].apply(lambda x: f'{x:,.0f} ₽'),
                textposition='outside'
            )
        ])
        
        fig.update_layout(
            title=title,
            height=max(self.height, len(top_contracts) * 40),
            xaxis_title="Сумма договора (₽)",
            yaxis_title="Договор",
            template='plotly_white',
            margin=dict(l=200)  # Увеличиваем левый отступ для длинных названий
        )
        
        return fig
    
    def completion_rate_gauge(self, completion_rate: float, 
                            title: str = "Общий процент освоения") -> go.Figure:
        """Создает gauge диаграмму для процента выполнения"""
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=completion_rate,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': title},
            delta={'reference': 80},  # Целевое значение
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': self.colors['primary']},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(
            height=300,
            template='plotly_white'
        )
        
        return fig

    def contracts_by_type_bar(self, df: pd.DataFrame, title: str = "Распределение договоров по типам") -> go.Figure:
        """Создает столбчатую диаграмму по типам договоров"""
        if df.empty:
            return self._empty_chart("Нет данных для отображения")

        # Подсчитываем количество и суммы по типам
        type_stats = df.groupby('contract_type').agg({
            'contract_id': 'count',
            'total_amount': 'sum'
        }).reset_index()

        type_stats.columns = ['contract_type', 'count', 'total_amount']
        type_stats = type_stats.sort_values('total_amount', ascending=True)

        fig = go.Figure()

        fig.add_trace(go.Bar(
            y=type_stats['contract_type'],
            x=type_stats['total_amount'],
            orientation='h',
            marker_color=self.colors['primary'],
            text=[f"{val:,.0f} ₽" for val in type_stats['total_amount']],
            textposition='outside',
            name='Сумма договоров'
        ))

        fig.update_layout(
            title=title,
            xaxis_title="Сумма договоров (₽)",
            yaxis_title="Тип договора",
            height=self.height,
            template='plotly_white'
        )

        return fig

    def contracts_timeline(self, df: pd.DataFrame,
                          title: str = "Временная шкала договоров") -> go.Figure:
        """Создает временную шкалу договоров (Gantt chart)"""
        if df.empty:
            return self._empty_chart("Нет данных для отображения")
        
        # Подготавливаем данные для Gantt диаграммы
        df_gantt = df.copy()
        df_gantt['start_date'] = pd.to_datetime(df_gantt['start_date'])
        df_gantt['end_date'] = pd.to_datetime(df_gantt['end_date'])
        
        # Ограничиваем количество договоров для читаемости
        df_gantt = df_gantt.head(20)
        
        fig = px.timeline(
            df_gantt,
            x_start="start_date",
            x_end="end_date",
            y="contract_name",
            color="status",
            title=title,
            height=max(self.height, len(df_gantt) * 30)
        )
        
        fig.update_layout(
            xaxis_title="Период",
            yaxis_title="Договор",
            template='plotly_white'
        )
        
        return fig
    
    def monthly_contracts_count(self, df: pd.DataFrame, 
                              title: str = "Количество договоров по месяцам") -> go.Figure:
        """Создает график количества договоров по месяцам"""
        if df.empty:
            return self._empty_chart("Нет данных для отображения")
        
        # Группируем по месяцам
        df['start_month'] = pd.to_datetime(df['start_date']).dt.to_period('M')
        monthly_counts = df.groupby('start_month').size().reset_index(name='count')
        monthly_counts['start_month'] = monthly_counts['start_month'].astype(str)
        
        fig = go.Figure(data=[
            go.Scatter(
                x=monthly_counts['start_month'],
                y=monthly_counts['count'],
                mode='lines+markers',
                line=dict(color=self.colors['primary'], width=3),
                marker=dict(size=8),
                fill='tonexty'
            )
        ])
        
        fig.update_layout(
            title=title,
            height=self.height,
            xaxis_title="Месяц",
            yaxis_title="Количество договоров",
            template='plotly_white'
        )
        
        return fig
    
    def _empty_chart(self, message: str) -> go.Figure:
        """Создает пустой график с сообщением"""
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            height=self.height,
            template='plotly_white',
            xaxis={'visible': False},
            yaxis={'visible': False}
        )
        return fig


# Глобальный экземпляр компонента графиков
chart_components = ChartComponents()
