"""
Компоненты для расширенных фильтров
"""
import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
import plotly.express as px
import plotly.graph_objects as go

from config.constants import CONTRACT_STATUSES, CONTRACT_TYPES


class AdvancedFilters:
    """Класс для создания расширенных фильтров"""
    
    def __init__(self):
        self.status_mapping = CONTRACT_STATUSES
        self.type_mapping = CONTRACT_TYPES
    
    def render_filter_panel(self) -> Dict[str, Any]:
        """Отрисовывает панель расширенных фильтров"""
        st.markdown("### 🔍 Расширенные фильтры")
        
        # Создаем expander для фильтров
        with st.expander("Настройки фильтрации", expanded=True):
            # Основные фильтры
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("**📊 Основные параметры**")
                status_filter = self._render_status_filter()
                type_filter = self._render_type_filter()
                
            with col2:
                st.markdown("**💰 Финансовые параметры**")
                amount_range = self._render_amount_filter()
                completion_filter = self._render_completion_filter()
                
            with col3:
                st.markdown("**📅 Временные параметры**")
                date_range = self._render_date_filter()
                deadline_filter = self._render_deadline_filter()
            
            # Дополнительные фильтры
            st.markdown("---")
            col4, col5 = st.columns(2)
            
            with col4:
                st.markdown("**🔍 Поиск и текст**")
                search_filters = self._render_search_filters()
                
            with col5:
                st.markdown("**⚙️ Настройки отображения**")
                display_options = self._render_display_options()
            
            # Кнопки управления
            st.markdown("---")
            action_buttons = self._render_action_buttons()
        
        # Собираем все фильтры в один словарь
        filters = {
            'status_filter': status_filter,
            'type_filter': type_filter,
            'amount_range': amount_range,
            'completion_filter': completion_filter,
            'date_range': date_range,
            'deadline_filter': deadline_filter,
            **search_filters,
            **display_options,
            **action_buttons
        }
        
        return filters
    
    def _render_status_filter(self) -> str:
        """Фильтр по статусу"""
        status_options = ['Все'] + list(self.status_mapping.keys())
        status_labels = ['Все'] + list(self.status_mapping.values())
        
        return st.selectbox(
            "Статус договора:",
            options=status_options,
            format_func=lambda x: dict(zip(status_options, status_labels))[x],
            key="advanced_status_filter"
        )
    
    def _render_type_filter(self) -> str:
        """Фильтр по типу договора"""
        type_options = ['Все'] + list(self.type_mapping.keys())
        type_labels = ['Все'] + list(self.type_mapping.values())
        
        return st.selectbox(
            "Тип договора:",
            options=type_options,
            format_func=lambda x: dict(zip(type_options, type_labels))[x],
            key="advanced_type_filter"
        )
    
    def _render_amount_filter(self) -> tuple:
        """Фильтр по сумме договора"""
        st.write("Диапазон суммы (млн ₽):")
        min_amount = st.number_input(
            "От:", 
            min_value=0.0, 
            max_value=1000.0, 
            value=0.0, 
            step=1.0,
            key="min_amount_filter"
        )
        max_amount = st.number_input(
            "До:", 
            min_value=0.0, 
            max_value=1000.0, 
            value=100.0, 
            step=1.0,
            key="max_amount_filter"
        )
        return (min_amount * 1_000_000, max_amount * 1_000_000)
    
    def _render_completion_filter(self) -> str:
        """Фильтр по проценту выполнения"""
        return st.selectbox(
            "Процент выполнения:",
            options=['Все', 'Менее 25%', '25-50%', '50-75%', 'Более 75%', '100%'],
            key="completion_filter"
        )
    
    def _render_date_filter(self) -> tuple:
        """Фильтр по датам"""
        st.write("Период договора:")
        start_date = st.date_input(
            "Дата начала от:",
            value=date.today() - timedelta(days=365),
            key="start_date_filter"
        )
        end_date = st.date_input(
            "Дата окончания до:",
            value=date.today() + timedelta(days=365),
            key="end_date_filter"
        )
        return (start_date, end_date)
    
    def _render_deadline_filter(self) -> str:
        """Фильтр по срокам выполнения"""
        return st.selectbox(
            "Статус по срокам:",
            options=[
                'Все', 
                'Просроченные', 
                'Заканчиваются в течение недели',
                'Заканчиваются в течение месяца', 
                'Долгосрочные (более года)'
            ],
            key="deadline_status_filter"
        )
    
    def _render_search_filters(self) -> Dict[str, str]:
        """Фильтры поиска"""
        search_query = st.text_input(
            "Поиск по названию/номеру:",
            placeholder="Введите текст для поиска...",
            key="advanced_search_filter"
        )
        
        contractor_filter = st.text_input(
            "Поиск по подрядчику:",
            placeholder="Название подрядчика...",
            key="advanced_contractor_filter"
        )
        
        return {
            'search_query': search_query,
            'contractor_filter': contractor_filter
        }
    
    def _render_display_options(self) -> Dict[str, Any]:
        """Настройки отображения"""
        sort_options = [
            'По дате создания (новые)', 
            'По дате создания (старые)',
            'По сумме (убыв.)', 
            'По сумме (возр.)', 
            'По названию (А-Я)',
            'По названию (Я-А)',
            'По сроку окончания'
        ]
        
        sort_filter = st.selectbox(
            "Сортировка:",
            options=sort_options,
            key="advanced_sort_filter"
        )
        
        page_size = st.selectbox(
            "Записей на странице:",
            options=[10, 20, 50, 100],
            index=1,
            key="advanced_page_size_filter"
        )
        
        show_charts = st.checkbox(
            "Показать графики",
            value=True,
            key="show_charts_filter"
        )
        
        return {
            'sort_filter': sort_filter,
            'page_size': page_size,
            'show_charts': show_charts
        }
    
    def _render_action_buttons(self) -> Dict[str, bool]:
        """Кнопки действий"""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            apply_filters = st.button("✅ Применить", type="primary", key="apply_advanced_filters")
        with col2:
            reset_filters = st.button("🔄 Сбросить", key="reset_advanced_filters")
        with col3:
            save_preset = st.button("💾 Сохранить пресет", key="save_filter_preset")
        with col4:
            export_data = st.button("📥 Экспорт", key="export_advanced_data")
        
        return {
            'apply_filters': apply_filters,
            'reset_filters': reset_filters,
            'save_preset': save_preset,
            'export_data': export_data
        }
    
    def apply_filters(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """Применяет все фильтры к DataFrame"""
        if not isinstance(df, pd.DataFrame) or df.empty:
            return df if isinstance(df, pd.DataFrame) else pd.DataFrame()
        
        filtered_df = df.copy()
        
        # Фильтр по статусу
        if filters['status_filter'] != 'Все':
            filtered_df = filtered_df[filtered_df['status'] == filters['status_filter']]
        
        # Фильтр по типу
        if filters['type_filter'] != 'Все':
            filtered_df = filtered_df[filtered_df['contract_type'] == filters['type_filter']]
        
        # Фильтр по сумме
        min_amount, max_amount = filters['amount_range']
        if min_amount > 0 or max_amount < 100_000_000:
            filtered_df = filtered_df[
                (filtered_df['total_amount'] >= min_amount) & 
                (filtered_df['total_amount'] <= max_amount)
            ]
        
        # Фильтр по датам
        start_date, end_date = filters['date_range']
        filtered_df = filtered_df[
            (pd.to_datetime(filtered_df['start_date']).dt.date >= start_date) &
            (pd.to_datetime(filtered_df['end_date']).dt.date <= end_date)
        ]
        
        # Фильтр по срокам
        if filters['deadline_filter'] != 'Все':
            filtered_df = self._apply_deadline_filter(filtered_df, filters['deadline_filter'])
        
        # Поиск по тексту
        if filters['search_query']:
            search_mask = (
                filtered_df['contract_name'].str.contains(filters['search_query'], case=False, na=False) |
                filtered_df['contract_number'].str.contains(filters['search_query'], case=False, na=False)
            )
            filtered_df = filtered_df[search_mask]
        
        # Фильтр по подрядчику
        if filters['contractor_filter']:
            contractor_mask = filtered_df['contractor_name'].str.contains(
                filters['contractor_filter'], case=False, na=False
            )
            filtered_df = filtered_df[contractor_mask]
        
        # Сортировка
        filtered_df = self._apply_sorting(filtered_df, filters['sort_filter'])
        
        return filtered_df
    
    def _apply_deadline_filter(self, df: pd.DataFrame, deadline_filter: str) -> pd.DataFrame:
        """Применяет фильтр по срокам"""
        today = pd.Timestamp.now().date()
        
        if deadline_filter == 'Просроченные':
            return df[
                (pd.to_datetime(df['end_date']).dt.date < today) & 
                (df['status'] == 'active')
            ]
        elif deadline_filter == 'Заканчиваются в течение недели':
            week_later = today + timedelta(days=7)
            return df[
                (pd.to_datetime(df['end_date']).dt.date <= week_later) & 
                (pd.to_datetime(df['end_date']).dt.date >= today) &
                (df['status'] == 'active')
            ]
        elif deadline_filter == 'Заканчиваются в течение месяца':
            month_later = today + timedelta(days=30)
            return df[
                (pd.to_datetime(df['end_date']).dt.date <= month_later) & 
                (pd.to_datetime(df['end_date']).dt.date >= today) &
                (df['status'] == 'active')
            ]
        elif deadline_filter == 'Долгосрочные (более года)':
            year_later = today + timedelta(days=365)
            return df[pd.to_datetime(df['end_date']).dt.date > year_later]
        
        return df
    
    def _apply_sorting(self, df: pd.DataFrame, sort_filter: str) -> pd.DataFrame:
        """Применяет сортировку"""
        if sort_filter == 'По дате создания (новые)':
            return df.sort_values('start_date', ascending=False)
        elif sort_filter == 'По дате создания (старые)':
            return df.sort_values('start_date', ascending=True)
        elif sort_filter == 'По сумме (убыв.)':
            return df.sort_values('total_amount', ascending=False)
        elif sort_filter == 'По сумме (возр.)':
            return df.sort_values('total_amount', ascending=True)
        elif sort_filter == 'По названию (А-Я)':
            return df.sort_values('contract_name', ascending=True)
        elif sort_filter == 'По названию (Я-А)':
            return df.sort_values('contract_name', ascending=False)
        elif sort_filter == 'По сроку окончания':
            return df.sort_values('end_date', ascending=True)
        
        return df
    
    def render_filter_summary(self, original_count: int, filtered_count: int, filters: Dict[str, Any]) -> None:
        """Отображает сводку по примененным фильтрам"""
        # Информационная панель
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                "Всего записей", 
                original_count,
                help="Общее количество записей в базе данных"
            )
        
        with col2:
            st.metric(
                "После фильтрации", 
                filtered_count,
                delta=filtered_count - original_count,
                help="Количество записей после применения фильтров"
            )
        
        with col3:
            percentage = (filtered_count / original_count * 100) if original_count > 0 else 0
            st.metric(
                "Процент отбора", 
                f"{percentage:.1f}%",
                help="Процент записей, прошедших фильтрацию"
            )
        
        # Активные фильтры
        active_filters = self._get_active_filters(filters)
        if active_filters:
            st.markdown("**Активные фильтры:**")
            for filter_name, filter_value in active_filters.items():
                st.markdown(f"• **{filter_name}**: {filter_value}")

    def _get_active_filters(self, filters: Dict[str, Any]) -> Dict[str, str]:
        """Возвращает список активных фильтров"""
        active = {}

        if filters['status_filter'] != 'Все':
            active['Статус'] = self.status_mapping.get(filters['status_filter'], filters['status_filter'])

        if filters['type_filter'] != 'Все':
            active['Тип'] = self.type_mapping.get(filters['type_filter'], filters['type_filter'])

        min_amount, max_amount = filters['amount_range']
        if min_amount > 0 or max_amount < 100_000_000:
            active['Сумма'] = f"{min_amount/1_000_000:.1f} - {max_amount/1_000_000:.1f} млн ₽"

        if filters['deadline_filter'] != 'Все':
            active['Сроки'] = filters['deadline_filter']

        if filters['search_query']:
            active['Поиск'] = filters['search_query']

        if filters['contractor_filter']:
            active['Подрядчик'] = filters['contractor_filter']

        return active


# Глобальный экземпляр компонента фильтров
advanced_filters = AdvancedFilters()
