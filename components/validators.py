"""
Валидаторы для компонентов дашборда
"""
import pandas as pd
from typing import Any, List, Dict, Optional, Union
import logging
from datetime import datetime, date

from components.types import (
    ValidationResult,
    ComponentTypeEnum,
    ColorSchemeEnum,
    MetricData,
    ChartData,
    TableData,
    FilterData,
    DashboardSummary,
    ContractData
)

logger = logging.getLogger(__name__)


class ComponentValidator:
    """Валидатор для компонентов"""
    
    @staticmethod
    def validate_metric_data(data: Dict[str, Any]) -> ValidationResult:
        """Валидирует данные метрики"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем обязательные поля
        required_fields = ['label', 'value']
        for field in required_fields:
            if field not in data:
                errors.append(f"Отсутствует обязательное поле: {field}")
            elif data[field] is None:
                errors.append(f"Поле {field} не может быть None")
        
        # Проверяем типы данных
        if 'label' in data and not isinstance(data['label'], str):
            errors.append("Поле 'label' должно быть строкой")
        
        if 'value' in data and not isinstance(data['value'], (str, int, float)):
            errors.append("Поле 'value' должно быть строкой, числом или float")
        
        if 'icon' in data and not isinstance(data['icon'], str):
            warnings.append("Поле 'icon' должно быть строкой")
        
        # Проверяем длину строк
        if 'label' in data and isinstance(data['label'], str) and len(data['label']) > 100:
            warnings.append("Слишком длинный label (>100 символов)")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def validate_chart_data(data: Dict[str, Any]) -> ValidationResult:
        """Валидирует данные графика"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем наличие данных
        if 'data' not in data:
            errors.append("Отсутствуют данные для графика")
        else:
            chart_data = data['data']
            
            if isinstance(chart_data, pd.DataFrame):
                if chart_data.empty:
                    warnings.append("DataFrame пуст")
                
                # Проверяем колонки
                if 'x_column' in data and data['x_column'] not in chart_data.columns:
                    errors.append(f"Колонка '{data['x_column']}' не найдена в данных")
                
                if 'y_column' in data and data['y_column'] not in chart_data.columns:
                    errors.append(f"Колонка '{data['y_column']}' не найдена в данных")
            
            elif isinstance(chart_data, list):
                if len(chart_data) == 0:
                    warnings.append("Список данных пуст")
            
            else:
                errors.append("Данные должны быть DataFrame или списком")
        
        # Проверяем заголовок
        if 'title' in data and not isinstance(data['title'], str):
            warnings.append("Заголовок должен быть строкой")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def validate_table_data(data: Dict[str, Any]) -> ValidationResult:
        """Валидирует данные таблицы"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем наличие данных
        if 'data' not in data:
            errors.append("Отсутствуют данные для таблицы")
        else:
            table_data = data['data']
            
            if isinstance(table_data, pd.DataFrame):
                if table_data.empty:
                    warnings.append("DataFrame пуст")
                
                # Проверяем количество строк
                if len(table_data) > 10000:
                    warnings.append("Слишком много строк (>10000), рекомендуется пагинация")
                
                # Проверяем колонки
                if 'columns' in data and data['columns']:
                    missing_columns = set(data['columns']) - set(table_data.columns)
                    if missing_columns:
                        errors.append(f"Отсутствуют колонки: {', '.join(missing_columns)}")
            
            elif isinstance(table_data, list):
                if len(table_data) == 0:
                    warnings.append("Список данных пуст")
                elif not all(isinstance(row, dict) for row in table_data):
                    errors.append("Все элементы списка должны быть словарями")
            
            else:
                errors.append("Данные должны быть DataFrame или списком словарей")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def validate_filter_data(data: Dict[str, Any]) -> ValidationResult:
        """Валидирует данные фильтра"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем обязательные поля
        required_fields = ['key', 'label', 'type']
        for field in required_fields:
            if field not in data:
                errors.append(f"Отсутствует обязательное поле: {field}")
        
        # Проверяем тип фильтра
        valid_types = [
            'selectbox', 'multiselect', 'text_input', 'number_input',
            'date_input', 'slider', 'checkbox'
        ]
        
        if 'type' in data and data['type'] not in valid_types:
            errors.append(f"Неизвестный тип фильтра: {data['type']}")
        
        # Проверяем опции для select-фильтров
        if 'type' in data and data['type'] in ['selectbox', 'multiselect']:
            if 'options' not in data or not data['options']:
                errors.append(f"Для типа {data['type']} требуются опции")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def validate_dashboard_summary(data: Dict[str, Any]) -> ValidationResult:
        """Валидирует сводку дашборда"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем обязательные поля
        required_fields = [
            'total_contracts', 'active_contracts', 'total_amount'
        ]
        
        for field in required_fields:
            if field not in data:
                errors.append(f"Отсутствует обязательное поле: {field}")
            elif not isinstance(data[field], (int, float)):
                errors.append(f"Поле {field} должно быть числом")
        
        # Проверяем логические связи
        if 'total_contracts' in data and 'active_contracts' in data:
            if data['active_contracts'] > data['total_contracts']:
                errors.append("Активных договоров не может быть больше общего количества")
        
        # Проверяем отрицательные значения
        numeric_fields = [
            'total_contracts', 'active_contracts', 'completed_contracts',
            'total_amount', 'average_amount'
        ]
        
        for field in numeric_fields:
            if field in data and isinstance(data[field], (int, float)) and data[field] < 0:
                warnings.append(f"Поле {field} имеет отрицательное значение")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def validate_contract_data(data: Dict[str, Any]) -> ValidationResult:
        """Валидирует данные договора"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем обязательные поля
        required_fields = [
            'contract_id', 'contract_number', 'contract_name',
            'status', 'total_amount'
        ]
        
        for field in required_fields:
            if field not in data:
                errors.append(f"Отсутствует обязательное поле: {field}")
        
        # Проверяем статус
        valid_statuses = ['active', 'completed', 'suspended', 'cancelled', 'overdue', 'pending']
        if 'status' in data and data['status'] not in valid_statuses:
            errors.append(f"Неизвестный статус: {data['status']}")
        
        # Проверяем даты
        if 'start_date' in data and 'end_date' in data:
            try:
                start = data['start_date']
                end = data['end_date']
                
                if isinstance(start, str):
                    start = datetime.strptime(start, '%Y-%m-%d').date()
                if isinstance(end, str):
                    end = datetime.strptime(end, '%Y-%m-%d').date()
                
                if start > end:
                    errors.append("Дата начала не может быть позже даты окончания")
                    
            except (ValueError, TypeError):
                errors.append("Неверный формат даты")
        
        # Проверяем сумму
        if 'total_amount' in data:
            if not isinstance(data['total_amount'], (int, float)):
                errors.append("Сумма договора должна быть числом")
            elif data['total_amount'] <= 0:
                warnings.append("Сумма договора должна быть положительной")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )


class ConfigValidator:
    """Валидатор конфигураций"""
    
    @staticmethod
    def validate_component_config(config: Dict[str, Any]) -> ValidationResult:
        """Валидирует конфигурацию компонента"""
        errors: List[str] = []
        warnings: List[str] = []
        
        # Проверяем тип компонента
        if 'component_type' not in config:
            errors.append("Отсутствует тип компонента")
        else:
            try:
                ComponentTypeEnum(config['component_type'])
            except ValueError:
                errors.append(f"Неизвестный тип компонента: {config['component_type']}")
        
        # Проверяем цветовую схему
        if 'color_scheme' in config:
            try:
                ColorSchemeEnum(config['color_scheme'])
            except ValueError:
                errors.append(f"Неизвестная цветовая схема: {config['color_scheme']}")
        
        # Проверяем булевы параметры
        bool_params = ['animated', 'responsive']
        for param in bool_params:
            if param in config and not isinstance(config[param], bool):
                warnings.append(f"Параметр {param} должен быть булевым")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )


# Глобальные экземпляры валидаторов
component_validator = ComponentValidator()
config_validator = ConfigValidator()
