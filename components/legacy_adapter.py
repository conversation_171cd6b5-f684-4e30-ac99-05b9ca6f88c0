"""
Адаптер для обеспечения обратной совместимости со старыми компонентами
"""
import streamlit as st
from typing import Optional, Dict, Any, List
import logging

from components.base import ComponentFactory, ComponentConfig, ComponentType, ColorScheme
from components.utils import formatter, validator

logger = logging.getLogger(__name__)


class LegacyComponentAdapter:
    """Адаптер для старых компонентов"""
    
    def __init__(self):
        self.factory = ComponentFactory()
    
    # Адаптеры для ModernComponents
    def enhanced_metric_card(
        self,
        label: str,
        value: str,
        delta: Optional[str] = None,
        icon: str = "📊",
        color_scheme: str = "blue",
        trend: Optional[str] = None,
        animation_delay: float = 0.0
    ) -> None:
        """Адаптер для ModernComponents.enhanced_metric_card"""
        try:
            # Преобразуем старые параметры в новые
            color_map = {
                'blue': ColorScheme.PRIMARY,
                'green': ColorScheme.SUCCESS,
                'red': ColorScheme.DANGER,
                'orange': ColorScheme.WARNING,
                'purple': ColorScheme.INFO
            }
            
            config = ComponentConfig(
                ComponentType.METRIC,
                color_scheme=color_map.get(color_scheme, ColorScheme.PRIMARY),
                animated=True
            )
            
            metric_card = self.factory.create_component(ComponentType.METRIC, config)
            metric_card.render(
                label=label,
                value=value,
                delta=delta,
                icon=icon,
                trend=trend
            )
            
        except Exception as e:
            logger.error(f"Ошибка в enhanced_metric_card: {e}")
            st.error(f"Ошибка отображения метрики: {e}")
    
    def progress_indicator_modern(
        self,
        label: str,
        current: float,
        target: float,
        color: str = "blue",
        show_percentage: bool = True,
        animated: bool = True
    ) -> None:
        """Адаптер для ModernComponents.progress_indicator_modern"""
        try:
            color_map = {
                'blue': ColorScheme.PRIMARY,
                'green': ColorScheme.SUCCESS,
                'red': ColorScheme.DANGER,
                'orange': ColorScheme.WARNING
            }
            
            config = ComponentConfig(
                ComponentType.PROGRESS,
                color_scheme=color_map.get(color, ColorScheme.PRIMARY),
                animated=animated
            )
            
            progress_bar = self.factory.create_component(ComponentType.PROGRESS, config)
            progress_bar.render(
                label=label,
                current=current,
                target=target,
                show_percentage=show_percentage
            )
            
        except Exception as e:
            logger.error(f"Ошибка в progress_indicator_modern: {e}")
            st.error(f"Ошибка отображения прогресса: {e}")
    
    # Адаптеры для ReliableComponents
    def reliable_enhanced_metric_card(
        self,
        label: str,
        value: str,
        delta: Optional[str] = None,
        icon: str = "📊",
        color_scheme: str = "blue",
        trend: Optional[str] = None,
        animation_delay: float = 0.0
    ) -> None:
        """Адаптер для ReliableComponents.enhanced_metric_card"""
        # Используем тот же адаптер, что и для ModernComponents
        self.enhanced_metric_card(
            label, value, delta, icon, color_scheme, trend, animation_delay
        )
    
    def reliable_progress_indicator_modern(
        self,
        label: str,
        current: float,
        target: float,
        color: str = "blue",
        show_percentage: bool = True,
        animated: bool = True
    ) -> None:
        """Адаптер для ReliableComponents.progress_indicator_modern"""
        # Используем тот же адаптер, что и для ModernComponents
        self.progress_indicator_modern(
            label, current, target, color, show_percentage, animated
        )
    
    # Адаптеры для TailwindComponents
    def tailwind_metric_card(
        self,
        label: str,
        value: str,
        delta: Optional[str] = None,
        status_class: str = "",
        icon: str = ""
    ) -> None:
        """Адаптер для TailwindComponents.metric_card"""
        try:
            # Преобразуем Tailwind классы в цветовые схемы
            color_scheme = ColorScheme.PRIMARY
            if "success" in status_class or "green" in status_class:
                color_scheme = ColorScheme.SUCCESS
            elif "warning" in status_class or "yellow" in status_class:
                color_scheme = ColorScheme.WARNING
            elif "danger" in status_class or "red" in status_class:
                color_scheme = ColorScheme.DANGER
            
            config = ComponentConfig(
                ComponentType.METRIC,
                color_scheme=color_scheme,
                animated=True
            )
            
            metric_card = self.factory.create_component(ComponentType.METRIC, config)
            metric_card.render(
                label=label,
                value=value,
                delta=delta,
                icon=icon or "📊"
            )
            
        except Exception as e:
            logger.error(f"Ошибка в tailwind_metric_card: {e}")
            st.error(f"Ошибка отображения метрики: {e}")
    
    def tailwind_progress_bar(
        self,
        label: str,
        value: float,
        max_value: float = 100,
        color_class: str = "bg-blue-500"
    ) -> None:
        """Адаптер для TailwindComponents.progress_bar"""
        try:
            # Преобразуем Tailwind классы в цветовые схемы
            color_scheme = ColorScheme.PRIMARY
            if "green" in color_class:
                color_scheme = ColorScheme.SUCCESS
            elif "yellow" in color_class or "orange" in color_class:
                color_scheme = ColorScheme.WARNING
            elif "red" in color_class:
                color_scheme = ColorScheme.DANGER
            
            config = ComponentConfig(
                ComponentType.PROGRESS,
                color_scheme=color_scheme,
                animated=True
            )
            
            progress_bar = self.factory.create_component(ComponentType.PROGRESS, config)
            progress_bar.render(
                label=label,
                current=value,
                target=max_value,
                show_percentage=True
            )
            
        except Exception as e:
            logger.error(f"Ошибка в tailwind_progress_bar: {e}")
            st.error(f"Ошибка отображения прогресса: {e}")
    
    # Адаптеры для DashboardComponents
    def dashboard_render_key_metrics(self, summary: Dict[str, Any]) -> None:
        """Адаптер для DashboardComponents.render_key_metrics"""
        try:
            if not validator.validate_data(summary, "dict"):
                return
            
            # Создаем метрики с использованием новых компонентов
            metrics_data = [
                {
                    'label': 'Всего договоров',
                    'value': int(summary.get('total_contracts', 0)),
                    'icon': '📋',
                    'color_scheme': 'primary',
                    'format_type': 'default'
                },
                {
                    'label': 'Активные договоры',
                    'value': int(summary.get('active_contracts', 0)),
                    'icon': '✅',
                    'color_scheme': 'success',
                    'format_type': 'default'
                },
                {
                    'label': 'Общая сумма',
                    'value': summary.get('total_amount', 0),
                    'icon': '💰',
                    'color_scheme': 'warning',
                    'format_type': 'currency'
                },
                {
                    'label': 'Просроченные',
                    'value': int(summary.get('overdue_contracts', 0)),
                    'icon': '⚠️',
                    'color_scheme': 'danger',
                    'format_type': 'default'
                }
            ]
            
            # Отрисовываем метрики
            cols = st.columns(4)
            for i, metric_data in enumerate(metrics_data):
                with cols[i]:
                    self.enhanced_metric_card(
                        label=metric_data['label'],
                        value=formatter.format_number(
                            metric_data['value'], 
                            metric_data['format_type']
                        ),
                        icon=metric_data['icon'],
                        color_scheme=metric_data['color_scheme']
                    )
                    
        except Exception as e:
            logger.error(f"Ошибка в dashboard_render_key_metrics: {e}")
            st.error(f"Ошибка отображения ключевых метрик: {e}")


# Создаем глобальные экземпляры для обратной совместимости
legacy_adapter = LegacyComponentAdapter()

# Создаем псевдонимы для старых классов
class ModernComponents:
    """Псевдоним для обратной совместимости с ModernComponents"""
    
    @staticmethod
    def enhanced_metric_card(*args, **kwargs):
        return legacy_adapter.enhanced_metric_card(*args, **kwargs)
    
    @staticmethod
    def progress_indicator_modern(*args, **kwargs):
        return legacy_adapter.progress_indicator_modern(*args, **kwargs)


class ReliableComponents:
    """Псевдоним для обратной совместимости с ReliableComponents"""
    
    @staticmethod
    def enhanced_metric_card(*args, **kwargs):
        return legacy_adapter.reliable_enhanced_metric_card(*args, **kwargs)
    
    @staticmethod
    def progress_indicator_modern(*args, **kwargs):
        return legacy_adapter.reliable_progress_indicator_modern(*args, **kwargs)


class TailwindComponents:
    """Псевдоним для обратной совместимости с TailwindComponents"""
    
    @staticmethod
    def metric_card(*args, **kwargs):
        return legacy_adapter.tailwind_metric_card(*args, **kwargs)
    
    @staticmethod
    def progress_bar(*args, **kwargs):
        return legacy_adapter.tailwind_progress_bar(*args, **kwargs)


# Экспортируем псевдонимы
modern_components = ModernComponents()
reliable_components = ReliableComponents()
tailwind_components = TailwindComponents()
