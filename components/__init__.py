"""
Унифицированная система компонентов дашборда
"""

# Базовые классы и типы
from components.base import (
    BaseComponent,
    ComponentConfig,
    ComponentFactory,
    ComponentType,
    ColorScheme
)

# Адаптеры для обратной совместимости
from components.legacy_adapter import (
    LegacyComponentAdapter,
    legacy_adapter,
    modern_components,
    reliable_components,
    tailwind_components
)

# Создаем глобальную фабрику компонентов
factory = ComponentFactory()
