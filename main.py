"""
Главная точка входа в дашборд освоения договоров
"""
import streamlit as st
import logging
from datetime import datetime

# Импорты модулей проекта
from config.settings import APP_CONFIG
from app.layouts.sidebar import render_sidebar
from app.layouts.header import render_official_header
from app.pages.dashboard import DashboardPage
from app.styles.css_manager import css_manager

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Конфигурация страницы
st.set_page_config(
    page_title=APP_CONFIG['title'],
    page_icon=APP_CONFIG['icon'],
    layout=APP_CONFIG['layout'],
    initial_sidebar_state=APP_CONFIG['initial_sidebar_state']
)

# Проверяем поддержку HTML рендеринга
logger.info("✅ Страница Streamlit сконфигурирована")

# Тестируем HTML рендеринг
def test_html_rendering():
    """Тестирует HTML рендеринг в текущей среде"""
    try:
        # Простой тест HTML рендеринга
        test_html = '<div style="color: red;">HTML Test</div>'
        st.markdown(test_html, unsafe_allow_html=True)
        logger.info("✅ HTML рендеринг работает корректно")
        return True
    except Exception as e:
        logger.error(f"❌ Ошибка HTML рендеринга: {e}")
        return False


def load_css_styles():
    """Загружает CSS стили для приложения"""
    # Загружаем базовые встроенные стили
    inline_styles = css_manager.get_inline_styles()
    css_manager.inject_css(inline_styles)

    # Пытаемся загрузить внешние CSS файлы
    css_manager.load_all_styles()


def main():
    """Главная функция приложения"""
    try:
        # Тестируем HTML рендеринг
        html_works = test_html_rendering()

        # Загружаем стили
        load_css_styles()

        # Отрисовываем боковую панель
        custom_start, custom_end = render_sidebar()

        # Создаем и отрисовываем основной дашборд
        dashboard = DashboardPage()
        dashboard.render()

        # Информация о последнем обновлении в боковой панели
        st.sidebar.markdown("---")
        st.sidebar.info(f"Последнее обновление: {datetime.now().strftime('%d.%m.%Y %H:%M')}")

        logger.info("✅ Приложение запущено успешно")

    except Exception as e:
        logger.error(f"❌ Ошибка запуска приложения: {e}")
        st.error(f"Произошла ошибка: {e}")
        st.info("Обратитесь к администратору системы")


if __name__ == "__main__":
    main()
