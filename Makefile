.PHONY: help install dev test lint format clean run docker-dev docker-prod

# Цвета для вывода
BLUE=\033[0;34m
GREEN=\033[0;32m
YELLOW=\033[1;33m
RED=\033[0;31m
NC=\033[0m # No Color

help: ## Показать справку
	@echo "$(BLUE)Доступные команды:$(NC)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}'

install: ## Установить зависимости
	@echo "$(BLUE)Установка зависимостей...$(NC)"
	poetry install

dev: install ## Установить зависимости для разработки
	@echo "$(BLUE)Установка зависимостей для разработки...$(NC)"
	poetry install --with dev

test: ## Запустить тесты
	@echo "$(BLUE)Запуск тестов...$(NC)"
	poetry run pytest tests/ -v

test-cov: ## Запустить тесты с покрытием
	@echo "$(BLUE)Запуск тестов с покрытием...$(NC)"
	poetry run pytest --cov=. --cov-report=html --cov-report=term tests/

lint: ## Проверить код линтерами
	@echo "$(BLUE)Проверка кода...$(NC)"
	poetry run flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	poetry run flake8 . --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics
	poetry run mypy . --ignore-missing-imports

format: ## Форматировать код
	@echo "$(BLUE)Форматирование кода...$(NC)"
	poetry run black .
	poetry run isort .

format-check: ## Проверить форматирование кода
	@echo "$(BLUE)Проверка форматирования...$(NC)"
	poetry run black --check .
	poetry run isort --check-only .

clean: ## Очистить временные файлы
	@echo "$(BLUE)Очистка временных файлов...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/

run: ## Запустить приложение
	@echo "$(BLUE)Запуск дашборда...$(NC)"
	@echo "$(YELLOW)Приложение будет доступно по адресу: http://localhost:8501$(NC)"
	poetry run streamlit run app.py

run-dev: ## Запустить приложение в режиме разработки
	@echo "$(BLUE)Запуск дашборда в режиме разработки...$(NC)"
	@echo "$(YELLOW)Приложение будет доступно по адресу: http://localhost:8501$(NC)"
	poetry run streamlit run app.py --server.runOnSave true

docker-dev: ## Запустить через Docker (разработка)
	@echo "$(BLUE)Запуск через Docker (разработка)...$(NC)"
	cd docker && make dev

docker-prod: ## Запустить через Docker (продакшн)
	@echo "$(BLUE)Запуск через Docker (продакшн)...$(NC)"
	cd docker && make prod

docker-tools: ## Запустить с дополнительными инструментами (pgAdmin)
	@echo "$(BLUE)Запуск с pgAdmin...$(NC)"
	cd docker && make dev-tools

setup: ## Первоначальная настройка проекта
	@echo "$(BLUE)Настройка проекта...$(NC)"
	@if [ ! -f .env ]; then \
		echo "$(YELLOW)Создание файла .env из .env.example...$(NC)"; \
		cp .env.example .env; \
		echo "$(RED)⚠️  Не забудьте отредактировать файл .env!$(NC)"; \
	else \
		echo "$(GREEN)Файл .env уже существует$(NC)"; \
	fi
	$(MAKE) install

check: format-check lint test ## Полная проверка кода

build: clean ## Собрать пакет
	@echo "$(BLUE)Сборка пакета...$(NC)"
	poetry build

publish: build ## Опубликовать пакет
	@echo "$(BLUE)Публикация пакета...$(NC)"
	poetry publish

update: ## Обновить зависимости
	@echo "$(BLUE)Обновление зависимостей...$(NC)"
	poetry update

shell: ## Активировать виртуальное окружение
	@echo "$(BLUE)Активация виртуального окружения...$(NC)"
	poetry shell

info: ## Показать информацию о проекте
	@echo "$(BLUE)Информация о проекте:$(NC)"
	@echo "$(GREEN)Название:$(NC) gaz-gaz"
	@echo "$(GREEN)Версия:$(NC) 0.1.0"
	@echo "$(GREEN)Python:$(NC) $(shell poetry run python --version)"
	@echo "$(GREEN)Poetry:$(NC) $(shell poetry --version)"
	@echo "$(GREEN)Виртуальное окружение:$(NC) $(shell poetry env info --path)"
