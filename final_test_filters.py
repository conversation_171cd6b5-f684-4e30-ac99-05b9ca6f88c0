#!/usr/bin/env python3
"""
Финальный тест панели активных фильтров
"""
import streamlit as st
import pandas as pd
import sys
import os
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.components.active_filters_panel import ActiveFiltersPanel
from app.components.top_filters import TopFiltersComponent

st.set_page_config(
    page_title="✅ Финальный тест фильтров",
    page_icon="✅",
    layout="wide"
)

st.title("✅ Финальный тест панели активных фильтров")
st.markdown("**Проверяем, что панель появляется СРАЗУ при изменении фильтров**")

# Создаем тестовые данные
@st.cache_data
def get_test_data():
    return pd.DataFrame({
        'status': ['active', 'completed', 'active', 'suspended', 'active', 'cancelled'],
        'contract_type': ['construction', 'services', 'construction', 'supply', 'services', 'maintenance'],
        'total_amount': [1000000, 2000000, 500000, 3000000, 1500000, 800000],
        'contract_name': ['Договор 1', 'Договор 2', 'Договор 3', 'Договор 4', 'Договор 5', 'Договор 6']
    })

# Получаем данные
df = get_test_data()

# Создаем компонент фильтров
top_filters = TopFiltersComponent()

# Отрисовываем фильтры и получаем результат
filters = top_filters.render(df, {})

# Получаем доступные значения
available_statuses = sorted(list(df['status'].unique()))
available_types = sorted(list(df['contract_type'].unique()))

# ВАЖНО: Отрисовываем панель активных фильтров ПОСЛЕ получения фильтров
active_filters_panel = ActiveFiltersPanel()
reset_clicked = active_filters_panel.render(filters, available_statuses, available_types)

if reset_clicked:
    st.success("🔄 Кнопка сброса была нажата!")
    st.rerun()

# Применяем отступ для основного контента
active_filters_panel.apply_main_content_padding()

st.markdown("---")
st.markdown("## 📊 Результаты тестирования")

# Анализируем фильтры
selected_statuses = filters.get('status', [])
selected_types = filters.get('contract_type', [])

col1, col2 = st.columns(2)

with col1:
    st.markdown("### 📋 Статусы")
    st.write(f"**Доступные:** {available_statuses}")
    st.write(f"**Выбранные:** {selected_statuses}")
    st.write(f"**Количество выбранных:** {len(selected_statuses)}")
    st.write(f"**Количество доступных:** {len(available_statuses)}")
    
    status_should_show = len(selected_statuses) < len(available_statuses)
    if status_should_show:
        st.success("✅ Статус: панель ДОЛЖНА показываться")
    else:
        st.info("ℹ️ Статус: панель НЕ должна показываться (выбраны все)")

with col2:
    st.markdown("### 🏗️ Типы договоров")
    st.write(f"**Доступные:** {available_types}")
    st.write(f"**Выбранные:** {selected_types}")
    st.write(f"**Количество выбранных:** {len(selected_types)}")
    st.write(f"**Количество доступных:** {len(available_types)}")
    
    type_should_show = len(selected_types) < len(available_types)
    if type_should_show:
        st.success("✅ Тип: панель ДОЛЖНА показываться")
    else:
        st.info("ℹ️ Тип: панель НЕ должна показываться (выбраны все)")

# Общий результат
should_show_panel = status_should_show or type_should_show

st.markdown("### 🎯 Общий результат")
if should_show_panel:
    st.success("✅ **ПАНЕЛЬ ДОЛЖНА БЫТЬ ВИДНА В ВЕРХНЕЙ ЧАСТИ ЭКРАНА!**")
    st.markdown("**Если панель не видна, проверьте:**")
    st.markdown("- Прокрутите страницу в самый верх")
    st.markdown("- Панель фиксированная и должна быть поверх всего контента")
    st.markdown("- Панель имеет серый фон с тенью")
else:
    st.info("ℹ️ Панель не должна показываться (выбраны все элементы)")

st.markdown("---")
st.markdown("## 🔍 Отфильтрованные данные")

# Применяем фильтры к данным
filtered_df = df.copy()

if selected_statuses:
    filtered_df = filtered_df[filtered_df['status'].isin(selected_statuses)]

if selected_types:
    filtered_df = filtered_df[filtered_df['contract_type'].isin(selected_types)]

st.dataframe(filtered_df, use_container_width=True)

st.markdown("---")
st.markdown("## 📝 Инструкции по тестированию")

st.markdown("""
### 🎯 Шаги для проверки:

1. **Изначально** все статусы и типы выбраны → панель НЕ показывается ❌
2. **Снимите выбор с одного статуса** → панель должна появиться СРАЗУ ✅
3. **Снимите выбор с одного типа** → панель должна появиться СРАЗУ ✅
4. **Выберите обратно все элементы** → панель должна исчезнуть ❌

### 🔍 Что искать:

- **Панель в верхней части экрана** с серым градиентным фоном
- **Анимация появления** (плавное выезжание сверху)
- **Теги фильтров** показывают только активные (неполные) фильтры
- **Кнопка "Сбросить все фильтры"** справа

### ⚠️ Если панель не появляется:

1. Проверьте логи в консоли браузера (F12)
2. Убедитесь, что страница прокручена в самый верх
3. Проверьте, что условие `len(selected) < len(available)` выполняется
4. Панель может быть скрыта за другими элементами - проверьте z-index

### ✅ Ожидаемое поведение:

- Панель появляется **мгновенно** при изменении фильтров
- Панель показывает **только активные фильтры**
- Панель **фиксированная** в верхней части экрана
- Кнопка сброса **работает корректно**
""")

# Показываем текущее состояние session_state для отладки
with st.expander("🔧 Отладочная информация"):
    st.write("**Все фильтры:**")
    st.json(filters)
    
    st.write("**Session State (ключи фильтров):**")
    filter_keys = {k: v for k, v in st.session_state.items() if 'filter' in k.lower() or 'top_' in k}
    st.json(filter_keys)
