[tool.poetry]
name = "gaz-gaz"
version = "0.1.0"
description = "Интерактивный дашборд для мониторинга и анализа динамики освоения договоров"
authors = ["igornet0 <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.9,<3.9.7 || >3.9.7,<4.0"
streamlit = "^1.47.1"
pandas = "^2.0.0"
plotly = "^5.0.0"
numpy = "^1.24.0"
psycopg2-binary = "^2.9.0"
sqlalchemy = {extras = ["asyncio"], version = "^2.0.41"}
python-dotenv = "^1.0.0"
requests = "^2.31.0"
openpyxl = "^3.1.0"
pydantic = "^2.0.0"
psutil = "^5.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-cov = "^4.0.0"
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.0.0"

[tool.poetry.scripts]
dashboard = "app:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["config", "data", "components", "utils"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
