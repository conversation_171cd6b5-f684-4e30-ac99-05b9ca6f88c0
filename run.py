#!/usr/bin/env python3
"""
Скрипт для запуска дашборда освоения договоров
"""
import subprocess
import sys
import os
from pathlib import Path


def check_poetry():
    """Проверяет наличие Poetry"""
    try:
        subprocess.run(["poetry", "--version"], capture_output=True, check=True)
        print("✅ Poetry найден")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Poetry не найден")
        print("Установите Poetry: https://python-poetry.org/docs/#installation")
        return False


def check_dependencies():
    """Проверяет установленные зависимости"""
    try:
        # Проверяем через poetry show
        result = subprocess.run(
            ["poetry", "show"],
            capture_output=True,
            text=True,
            check=True
        )

        required_packages = [
            "streamlit", "pandas", "plotly", "psycopg2-binary",
            "sqlalchemy", "python-dotenv"
        ]

        installed_packages = result.stdout.lower()
        missing_packages = []

        for package in required_packages:
            if package.lower() not in installed_packages:
                missing_packages.append(package)

        if missing_packages:
            print(f"❌ Отсутствуют зависимости: {', '.join(missing_packages)}")
            print("Установите зависимости командой: poetry install")
            return False

        print("✅ Все зависимости установлены")
        return True

    except subprocess.CalledProcessError:
        print("❌ Ошибка проверки зависимостей")
        print("Установите зависимости командой: poetry install")
        return False


def check_env_file():
    """Проверяет наличие файла .env"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  Файл .env не найден")
            print("Скопируйте .env.example в .env и заполните настройки:")
            print("cp .env.example .env")
            return False
        else:
            print("❌ Файлы .env и .env.example не найдены")
            return False
    
    print("✅ Файл конфигурации .env найден")
    return True


def run_streamlit():
    """Запускает Streamlit приложение через Poetry"""
    try:
        print("🚀 Запуск дашборда...")
        print("Приложение будет доступно по адресу: http://localhost:8501")
        print("Для остановки нажмите Ctrl+C")

        subprocess.run([
            "poetry", "run", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 Дашборд остановлен")
    except Exception as e:
        print(f"❌ Ошибка запуска: {e}")
        print("Попробуйте запустить вручную: poetry run streamlit run app.py")


def main():
    """Главная функция"""
    print("📊 Дашборд освоения договоров")
    print("=" * 40)

    # Проверяем Poetry
    if not check_poetry():
        sys.exit(1)

    # Проверяем зависимости
    if not check_dependencies():
        sys.exit(1)

    # Проверяем конфигурацию
    if not check_env_file():
        sys.exit(1)

    # Запускаем приложение
    run_streamlit()


if __name__ == "__main__":
    main()
