version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: gaz_gaz_postgres_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-contracts_db}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ../src/dashbord_streamlit/sample_data:/docker-entrypoint-initdb.d:ro
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - gaz_gaz_network_prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-contracts_db}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Streamlit Application
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: gaz_gaz_app_prod
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-contracts_db}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      
      # Application Configuration
      APP_TITLE: ${APP_TITLE:-"Дашборд освоения договоров"}
      APP_ICON: ${APP_ICON:-"📊"}
      DEBUG: "false"
      
      # Dashboard Configuration
      DEFAULT_DATE_RANGE: ${DEFAULT_DATE_RANGE:-12}
      REFRESH_INTERVAL: ${REFRESH_INTERVAL:-300}
    ports:
      - "${APP_PORT:-8501}:8501"
    volumes:
      # Mount logs directory
      - app_logs_prod:/app/logs
    networks:
      - gaz_gaz_network_prod
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Named volumes
volumes:
  postgres_data_prod:
    driver: local
  app_logs_prod:
    driver: local

# Networks
networks:
  gaz_gaz_network_prod:
    driver: bridge
