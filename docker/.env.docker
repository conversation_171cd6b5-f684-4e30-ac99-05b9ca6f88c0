# Docker Environment Configuration for Gaz Gaz Dashboard
# Copy this file to .env and modify the values as needed

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=contracts_db
DB_USER=postgres
DB_PASSWORD=postgres

# Application Configuration
APP_TITLE=Дашборд освоения договоров
APP_ICON=📊
APP_PORT=8501
DEBUG=false

# Dashboard Configuration
DEFAULT_DATE_RANGE=12
REFRESH_INTERVAL=300

# PostgreSQL Configuration (for production)
POSTGRES_DB=contracts_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# pgAdmin Configuration (optional)
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin
