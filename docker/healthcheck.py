#!/usr/bin/env python3
"""
Health check script for the Gaz Gaz Dashboard application
"""
import sys
import requests
import time
from typing import bool

def check_streamlit_health(host: str = "localhost", port: int = 8501, timeout: int = 5) -> bool:
    """
    Check if Streamlit application is healthy
    
    Args:
        host: Host to check
        port: Port to check
        timeout: Request timeout in seconds
        
    Returns:
        True if healthy, False otherwise
    """
    try:
        # Check Streamlit health endpoint
        health_url = f"http://{host}:{port}/_stcore/health"
        response = requests.get(health_url, timeout=timeout)
        
        if response.status_code == 200:
            return True
        else:
            print(f"Health check failed: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("Health check failed: Connection refused")
        return False
    except requests.exceptions.Timeout:
        print("Health check failed: Request timeout")
        return False
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def check_database_connection() -> bool:
    """
    Check if database connection is working
    
    Returns:
        True if database is accessible, False otherwise
    """
    try:
        import os
        import psycopg2
        
        # Get database configuration from environment
        db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'contracts_db'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres'),
        }
        
        # Test connection
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False

def main():
    """Main health check function"""
    print("Running health checks...")
    
    # Check Streamlit application
    streamlit_healthy = check_streamlit_health()
    if streamlit_healthy:
        print("✅ Streamlit application is healthy")
    else:
        print("❌ Streamlit application is not healthy")
    
    # Check database connection
    db_healthy = check_database_connection()
    if db_healthy:
        print("✅ Database connection is healthy")
    else:
        print("❌ Database connection is not healthy")
    
    # Overall health status
    if streamlit_healthy and db_healthy:
        print("✅ All health checks passed")
        sys.exit(0)
    else:
        print("❌ Some health checks failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
