# Git
.git
.gitignore
.gitattributes

# Docker
docker/
Dockerfile*
docker-compose*
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env/
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
.readthedocs.yml

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environment variables (keep .env.example)
.env

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Database
*.db
*.sqlite
*.sqlite3

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Large data files
*.csv
*.xlsx
*.xls
data/
datasets/

# Cache directories
.cache/
*.cache
