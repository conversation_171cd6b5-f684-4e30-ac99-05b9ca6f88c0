# Docker Configuration for Gaz Gaz Dashboard

This directory contains Docker configuration files to containerize and run the Gaz Gaz Python dashboard project.

## Files Overview

- `Dockerfile` - Main Docker image configuration
- `docker-compose.yml` - Development environment with PostgreSQL and pgAdmin
- `docker-compose.prod.yml` - Production environment configuration
- `.dockerignore` - Files to exclude from Docker build context
- `README.md` - This documentation file

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+

## Quick Start (Development)

1. **Clone the repository and navigate to the project root:**
   ```bash
   cd /path/to/gaz_gaz
   ```

2. **Start the services:**
   ```bash
   docker-compose -f docker/docker-compose.yml up -d
   ```

3. **Access the application:**
   - Dashboard: http://localhost:8501
   - pgAdmin (optional): http://localhost:5050
     - Email: <EMAIL>
     - Password: admin

4. **Stop the services:**
   ```bash
   docker-compose -f docker/docker-compose.yml down
   ```

## Production Deployment

1. **Create environment file:**
   ```bash
   cp .env.example .env
   # Edit .env with your production settings
   ```

2. **Start production services:**
   ```bash
   docker-compose -f docker/docker-compose.prod.yml up -d
   ```

3. **View logs:**
   ```bash
   docker-compose -f docker/docker-compose.prod.yml logs -f app
   ```

## Environment Variables

### Database Configuration
- `DB_HOST` - Database host (default: postgres)
- `DB_PORT` - Database port (default: 5432)
- `DB_NAME` - Database name (default: contracts_db)
- `DB_USER` - Database user (default: postgres)
- `DB_PASSWORD` - Database password (default: postgres)

### Application Configuration
- `APP_TITLE` - Application title
- `APP_ICON` - Application icon
- `APP_PORT` - Application port (default: 8501)
- `DEBUG` - Debug mode (default: false)

### Dashboard Configuration
- `DEFAULT_DATE_RANGE` - Default date range in months (default: 12)
- `REFRESH_INTERVAL` - Refresh interval in seconds (default: 300)

## Services

### PostgreSQL Database
- **Image:** postgres:15-alpine
- **Port:** 5432
- **Volume:** Persistent data storage
- **Health Check:** Built-in PostgreSQL health check

### Streamlit Application
- **Build:** Custom Dockerfile
- **Port:** 8501
- **Dependencies:** Waits for PostgreSQL to be healthy
- **Health Check:** Streamlit health endpoint

### pgAdmin (Development Only)
- **Image:** dpage/pgadmin4:latest
- **Port:** 5050
- **Profile:** tools (optional service)

## Useful Commands

### Development
```bash
# Start all services
docker-compose -f docker/docker-compose.yml up -d

# Start with pgAdmin
docker-compose -f docker/docker-compose.yml --profile tools up -d

# View logs
docker-compose -f docker/docker-compose.yml logs -f app

# Rebuild application image
docker-compose -f docker/docker-compose.yml build app

# Execute commands in running container
docker-compose -f docker/docker-compose.yml exec app bash

# Stop and remove all containers
docker-compose -f docker/docker-compose.yml down -v
```

### Production
```bash
# Start production services
docker-compose -f docker/docker-compose.prod.yml up -d

# Scale application (if needed)
docker-compose -f docker/docker-compose.prod.yml up -d --scale app=2

# Update application
docker-compose -f docker/docker-compose.prod.yml pull app
docker-compose -f docker/docker-compose.prod.yml up -d app
```

## Troubleshooting

### Database Connection Issues
1. Check if PostgreSQL container is running:
   ```bash
   docker-compose -f docker/docker-compose.yml ps postgres
   ```

2. Check PostgreSQL logs:
   ```bash
   docker-compose -f docker/docker-compose.yml logs postgres
   ```

3. Test database connection:
   ```bash
   docker-compose -f docker/docker-compose.yml exec postgres psql -U postgres -d contracts_db -c "SELECT 1;"
   ```

### Application Issues
1. Check application logs:
   ```bash
   docker-compose -f docker/docker-compose.yml logs app
   ```

2. Restart application:
   ```bash
   docker-compose -f docker/docker-compose.yml restart app
   ```

3. Rebuild application image:
   ```bash
   docker-compose -f docker/docker-compose.yml build --no-cache app
   ```

## Additional Tools

### Startup Script
Use the provided startup script for easier management:
```bash
# Make executable (first time only)
chmod +x docker/start.sh

# Start development environment
./docker/start.sh dev

# Start with pgAdmin
./docker/start.sh dev --with-pgadmin

# Start production environment
./docker/start.sh prod

# Show logs
./docker/start.sh logs

# Stop all services
./docker/start.sh stop

# Clean up everything
./docker/start.sh clean
```

### Makefile
Use the Makefile for common tasks:
```bash
cd docker

# Start development
make dev

# Start with tools
make dev-tools

# Show status
make status

# View logs
make logs

# Open application shell
make shell

# Open database shell
make db-shell

# Backup database
make backup

# Clean up
make clean
```

## Data Persistence

- **PostgreSQL Data:** Stored in named volume `postgres_data`
- **Application Logs:** Stored in named volume `app_logs`
- **pgAdmin Data:** Stored in named volume `pgadmin_data`

### Database Backup and Restore
```bash
# Using Makefile
cd docker
make backup    # Creates timestamped backup
make restore   # Restores from backup file

# Manual backup
docker-compose -f docker/docker-compose.yml exec postgres pg_dump -U postgres contracts_db > backup.sql

# Manual restore
docker-compose -f docker/docker-compose.yml exec -T postgres psql -U postgres -d contracts_db < backup.sql
```
