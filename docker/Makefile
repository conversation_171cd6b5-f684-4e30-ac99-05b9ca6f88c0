# Makefile for Gaz Gaz Dashboard Docker Management

.PHONY: help dev prod stop logs status clean build test

# Default target
help:
	@echo "Gaz Gaz Dashboard Docker Management"
	@echo ""
	@echo "Available targets:"
	@echo "  dev         - Start development environment"
	@echo "  dev-tools   - Start development environment with pgAdmin"
	@echo "  prod        - Start production environment"
	@echo "  stop        - Stop all services"
	@echo "  logs        - Show application logs"
	@echo "  logs-db     - Show database logs"
	@echo "  status      - Show services status"
	@echo "  clean       - Stop and remove all containers and volumes"
	@echo "  build       - Build application image"
	@echo "  test        - Run health checks"
	@echo "  shell       - Open shell in running application container"
	@echo "  db-shell    - Open PostgreSQL shell"
	@echo "  backup      - Backup database"
	@echo "  restore     - Restore database from backup"

# Development environment
dev:
	@echo "Starting development environment..."
	docker-compose -f docker-compose.yml up -d

# Development environment with tools
dev-tools:
	@echo "Starting development environment with pgAdmin..."
	docker-compose -f docker-compose.yml --profile tools up -d

# Production environment
prod:
	@echo "Starting production environment..."
	@if [ ! -f ../.env ]; then \
		echo "Warning: .env file not found. Creating from template..."; \
		cp .env.docker ../.env; \
		echo "Please edit .env file with your production settings."; \
		exit 1; \
	fi
	docker-compose -f docker-compose.prod.yml up -d

# Stop all services
stop:
	@echo "Stopping all services..."
	docker-compose -f docker-compose.yml down 2>/dev/null || true
	docker-compose -f docker-compose.prod.yml down 2>/dev/null || true

# Show application logs
logs:
	@if docker-compose -f docker-compose.yml ps app >/dev/null 2>&1; then \
		docker-compose -f docker-compose.yml logs -f app; \
	elif docker-compose -f docker-compose.prod.yml ps app >/dev/null 2>&1; then \
		docker-compose -f docker-compose.prod.yml logs -f app; \
	else \
		echo "No running application found"; \
	fi

# Show database logs
logs-db:
	@if docker-compose -f docker-compose.yml ps postgres >/dev/null 2>&1; then \
		docker-compose -f docker-compose.yml logs -f postgres; \
	elif docker-compose -f docker-compose.prod.yml ps postgres >/dev/null 2>&1; then \
		docker-compose -f docker-compose.prod.yml logs -f postgres; \
	else \
		echo "No running database found"; \
	fi

# Show services status
status:
	@echo "Development services:"
	@docker-compose -f docker-compose.yml ps 2>/dev/null || echo "  No development services running"
	@echo ""
	@echo "Production services:"
	@docker-compose -f docker-compose.prod.yml ps 2>/dev/null || echo "  No production services running"

# Clean up everything
clean:
	@echo "This will stop and remove all containers and volumes. Are you sure? [y/N]"
	@read -r response; \
	if [ "$$response" = "y" ] || [ "$$response" = "Y" ]; then \
		echo "Cleaning up..."; \
		docker-compose -f docker-compose.yml down -v 2>/dev/null || true; \
		docker-compose -f docker-compose.prod.yml down -v 2>/dev/null || true; \
		echo "Cleanup completed"; \
	else \
		echo "Cleanup cancelled"; \
	fi

# Build application image
build:
	@echo "Building application image..."
	docker-compose -f docker-compose.yml build app

# Run health checks
test:
	@echo "Running health checks..."
	@if docker-compose -f docker-compose.yml ps app >/dev/null 2>&1; then \
		docker-compose -f docker-compose.yml exec app python /usr/local/bin/healthcheck.py; \
	elif docker-compose -f docker-compose.prod.yml ps app >/dev/null 2>&1; then \
		docker-compose -f docker-compose.prod.yml exec app python /usr/local/bin/healthcheck.py; \
	else \
		echo "No running application found"; \
	fi

# Open shell in application container
shell:
	@if docker-compose -f docker-compose.yml ps app >/dev/null 2>&1; then \
		docker-compose -f docker-compose.yml exec app bash; \
	elif docker-compose -f docker-compose.prod.yml ps app >/dev/null 2>&1; then \
		docker-compose -f docker-compose.prod.yml exec app bash; \
	else \
		echo "No running application found"; \
	fi

# Open PostgreSQL shell
db-shell:
	@if docker-compose -f docker-compose.yml ps postgres >/dev/null 2>&1; then \
		docker-compose -f docker-compose.yml exec postgres psql -U postgres -d contracts_db; \
	elif docker-compose -f docker-compose.prod.yml ps postgres >/dev/null 2>&1; then \
		docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres -d contracts_db; \
	else \
		echo "No running database found"; \
	fi

# Backup database
backup:
	@echo "Creating database backup..."
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if docker-compose -f docker-compose.yml ps postgres >/dev/null 2>&1; then \
		docker-compose -f docker-compose.yml exec postgres pg_dump -U postgres contracts_db > backup_$$timestamp.sql; \
	elif docker-compose -f docker-compose.prod.yml ps postgres >/dev/null 2>&1; then \
		docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres contracts_db > backup_$$timestamp.sql; \
	else \
		echo "No running database found"; \
		exit 1; \
	fi; \
	echo "Backup created: backup_$$timestamp.sql"

# Restore database from backup
restore:
	@echo "Available backup files:"
	@ls -la backup_*.sql 2>/dev/null || echo "No backup files found"
	@echo "Enter backup filename to restore:"
	@read -r backup_file; \
	if [ ! -f "$$backup_file" ]; then \
		echo "Backup file not found: $$backup_file"; \
		exit 1; \
	fi; \
	echo "This will overwrite the current database. Are you sure? [y/N]"; \
	read -r response; \
	if [ "$$response" = "y" ] || [ "$$response" = "Y" ]; then \
		if docker-compose -f docker-compose.yml ps postgres >/dev/null 2>&1; then \
			docker-compose -f docker-compose.yml exec -T postgres psql -U postgres -d contracts_db < "$$backup_file"; \
		elif docker-compose -f docker-compose.prod.yml ps postgres >/dev/null 2>&1; then \
			docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres -d contracts_db < "$$backup_file"; \
		else \
			echo "No running database found"; \
			exit 1; \
		fi; \
		echo "Database restored from $$backup_file"; \
	else \
		echo "Restore cancelled"; \
	fi
