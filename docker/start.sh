#!/bin/bash

# Gaz Gaz Dashboard Docker Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    print_success "docker-compose is available"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development environment"
    echo "  prod        Start production environment"
    echo "  stop        Stop all services"
    echo "  logs        Show application logs"
    echo "  status      Show services status"
    echo "  clean       Stop and remove all containers and volumes"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  --with-pgadmin    Include pgAdmin service (development only)"
    echo "  --build           Force rebuild of images"
    echo ""
    echo "Examples:"
    echo "  $0 dev                    # Start development environment"
    echo "  $0 dev --with-pgadmin     # Start with pgAdmin"
    echo "  $0 prod                   # Start production environment"
    echo "  $0 logs                   # Show application logs"
    echo "  $0 stop                   # Stop all services"
}

# Parse command line arguments
COMMAND=${1:-help}
WITH_PGADMIN=false
BUILD_FLAG=""

shift || true
while [[ $# -gt 0 ]]; do
    case $1 in
        --with-pgadmin)
            WITH_PGADMIN=true
            shift
            ;;
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main script logic
case $COMMAND in
    dev)
        print_status "Starting development environment..."
        check_docker
        check_docker_compose
        
        if [ "$WITH_PGADMIN" = true ]; then
            print_status "Including pgAdmin service"
            docker-compose -f docker/docker-compose.yml --profile tools up -d $BUILD_FLAG
        else
            docker-compose -f docker/docker-compose.yml up -d $BUILD_FLAG
        fi
        
        print_success "Development environment started!"
        print_status "Dashboard: http://localhost:8501"
        if [ "$WITH_PGADMIN" = true ]; then
            print_status "pgAdmin: http://localhost:5050 (<EMAIL> / admin)"
        fi
        ;;
        
    prod)
        print_status "Starting production environment..."
        check_docker
        check_docker_compose
        
        if [ ! -f .env ]; then
            print_warning ".env file not found. Creating from .env.docker template..."
            cp docker/.env.docker .env
            print_warning "Please edit .env file with your production settings before running again."
            exit 1
        fi
        
        docker-compose -f docker/docker-compose.prod.yml up -d $BUILD_FLAG
        print_success "Production environment started!"
        print_status "Dashboard: http://localhost:8501"
        ;;
        
    stop)
        print_status "Stopping all services..."
        docker-compose -f docker/docker-compose.yml down 2>/dev/null || true
        docker-compose -f docker/docker-compose.prod.yml down 2>/dev/null || true
        print_success "All services stopped"
        ;;

    logs)
        print_status "Showing application logs..."
        if docker-compose -f docker/docker-compose.yml ps app > /dev/null 2>&1; then
            docker-compose -f docker/docker-compose.yml logs -f app
        elif docker-compose -f docker/docker-compose.prod.yml ps app > /dev/null 2>&1; then
            docker-compose -f docker/docker-compose.prod.yml logs -f app
        else
            print_error "No running application found"
            exit 1
        fi
        ;;

    status)
        print_status "Services status:"
        echo ""
        echo "Development:"
        docker-compose -f docker/docker-compose.yml ps 2>/dev/null || echo "  No development services running"
        echo ""
        echo "Production:"
        docker-compose -f docker/docker-compose.prod.yml ps 2>/dev/null || echo "  No production services running"
        ;;

    clean)
        print_warning "This will stop and remove all containers and volumes. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            print_status "Cleaning up..."
            docker-compose -f docker/docker-compose.yml down -v 2>/dev/null || true
            docker-compose -f docker/docker-compose.prod.yml down -v 2>/dev/null || true
            print_success "Cleanup completed"
        else
            print_status "Cleanup cancelled"
        fi
        ;;
        
    help)
        show_usage
        ;;
        
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
