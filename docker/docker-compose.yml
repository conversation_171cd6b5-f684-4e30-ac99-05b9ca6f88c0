services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: gaz_gaz_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: contracts_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../sample_data:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - gaz_gaz_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d contracts_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Streamlit Application
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: gaz_gaz_app
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: contracts_db
      DB_USER: postgres
      DB_PASSWORD: postgres

      # Application Configuration
      APP_TITLE: "Дашборд освоения договоров"
      APP_ICON: "📊"
      DEBUG: "false"

      # Dashboard Configuration
      DEFAULT_DATE_RANGE: 12
      REFRESH_INTERVAL: 300
    ports:
      - "8501:8501"
    volumes:
      # Mount source code for development (comment out for production)
      - ../:/app:ro
    networks:
      - gaz_gaz_network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "/usr/local/bin/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # pgAdmin (optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: gaz_gaz_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - gaz_gaz_network
    depends_on:
      - postgres
    profiles:
      - tools

# Named volumes
volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

# Networks
networks:
  gaz_gaz_network:
    driver: bridge
