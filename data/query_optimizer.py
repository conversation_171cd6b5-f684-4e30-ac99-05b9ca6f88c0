"""
Оптимизатор SQL запросов для дашборда
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import time
from contextlib import contextmanager

from data.database import db_connection

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """Класс для оптимизации SQL запросов"""
    
    def __init__(self):
        self.db = db_connection
        self.slow_query_threshold = 1.0  # секунды
        self.query_stats = {}
    
    @contextmanager
    def measure_query_time(self, query_name: str):
        """Контекстный менеджер для измерения времени выполнения запроса"""
        start_time = time.time()
        try:
            yield
        finally:
            execution_time = time.time() - start_time
            self._record_query_stats(query_name, execution_time)
            
            if execution_time > self.slow_query_threshold:
                logger.warning(f"Медленный запрос '{query_name}': {execution_time:.2f}s")
    
    def _record_query_stats(self, query_name: str, execution_time: float):
        """Записывает статистику выполнения запроса"""
        if query_name not in self.query_stats:
            self.query_stats[query_name] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'avg_time': 0
            }
        
        stats = self.query_stats[query_name]
        stats['count'] += 1
        stats['total_time'] += execution_time
        stats['min_time'] = min(stats['min_time'], execution_time)
        stats['max_time'] = max(stats['max_time'], execution_time)
        stats['avg_time'] = stats['total_time'] / stats['count']
    
    def get_optimized_contracts_overview_query(
        self, 
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        status_filter: Optional[str] = None,
        type_filter: Optional[str] = None
    ) -> Tuple[str, tuple]:
        """Возвращает оптимизированный запрос для обзора договоров"""
        
        # Базовый запрос с индексированными колонками
        base_query = """
        SELECT 
            contract_id,
            contract_number,
            contract_name,
            contract_type,
            status,
            total_amount,
            start_date,
            end_date,
            contractor_name,
            created_at
        FROM contracts
        """
        
        # Условия фильтрации
        conditions = []
        params = []
        
        if status_filter:
            conditions.append("status = %s")
            params.append(status_filter)
        
        if type_filter:
            conditions.append("contract_type = %s")
            params.append(type_filter)
        
        # Добавляем WHERE если есть условия
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)
        
        # Сортировка по индексированной колонке
        base_query += " ORDER BY created_at DESC, contract_id DESC"
        
        # Пагинация
        if limit:
            base_query += " LIMIT %s"
            params.append(limit)
            
            if offset:
                base_query += " OFFSET %s"
                params.append(offset)
        
        return base_query, tuple(params)
    
    def get_optimized_dashboard_summary_query(self) -> str:
        """Возвращает оптимизированный запрос для сводки дашборда"""
        return """
        SELECT
            COUNT(*) as total_contracts,
            COUNT(*) FILTER (WHERE status = 'active') as active_contracts,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_contracts,
            COUNT(*) FILTER (WHERE status = 'suspended') as suspended_contracts,
            COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_contracts,
            COUNT(*) FILTER (WHERE end_date < CURRENT_DATE AND status = 'active') as overdue_contracts,
            COALESCE(SUM(total_amount), 0) as total_amount,
            COALESCE(AVG(total_amount), 0) as average_amount,
            COALESCE(AVG(end_date - start_date), 0) as average_duration
        FROM contracts
        """
    
    def get_optimized_monthly_progress_query(self) -> str:
        """Возвращает оптимизированный запрос для месячного прогресса"""
        return """
        SELECT 
            DATE_TRUNC('month', cp.report_date) as month,
            SUM(cp.amount_completed) as completed_amount,
            SUM(cp.amount_planned) as planned_amount,
            COUNT(DISTINCT cp.contract_id) as contracts_count,
            CASE 
                WHEN SUM(cp.amount_planned) > 0 
                THEN SUM(cp.amount_completed) / SUM(cp.amount_planned) * 100 
                ELSE 0 
            END as completion_rate
        FROM contract_progress cp
        INNER JOIN contracts c ON cp.contract_id = c.contract_id
        WHERE cp.report_date >= %s 
            AND cp.report_date <= %s
            AND c.status IN ('active', 'completed')
        GROUP BY DATE_TRUNC('month', cp.report_date)
        ORDER BY month
        """
    
    def get_contracts_by_status_batch_query(self, statuses: List[str]) -> Tuple[str, tuple]:
        """Возвращает batch запрос для получения договоров по статусам"""
        placeholders = ','.join(['%s'] * len(statuses))
        
        query = f"""
        SELECT 
            contract_id,
            contract_number,
            contract_name,
            contract_type,
            status,
            total_amount,
            start_date,
            end_date,
            contractor_name
        FROM contracts 
        WHERE status IN ({placeholders})
        ORDER BY 
            CASE status
                WHEN 'active' THEN 1
                WHEN 'overdue' THEN 2
                WHEN 'completed' THEN 3
                WHEN 'suspended' THEN 4
                WHEN 'cancelled' THEN 5
                ELSE 6
            END,
            total_amount DESC
        """
        
        return query, tuple(statuses)
    
    def get_top_contracts_optimized_query(self, limit: int = 10) -> Tuple[str, tuple]:
        """Возвращает оптимизированный запрос для топ договоров"""
        query = """
        SELECT 
            contract_id,
            contract_number,
            contract_name,
            contract_type,
            status,
            total_amount,
            contractor_name
        FROM contracts 
        WHERE status IN ('active', 'completed')
        ORDER BY total_amount DESC
        LIMIT %s
        """
        
        return query, (limit,)
    
    def create_database_indexes(self) -> List[str]:
        """Создает индексы для оптимизации запросов"""
        indexes = [
            # Индекс для фильтрации по статусу
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_status ON contracts(status)",
            
            # Индекс для фильтрации по типу договора
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_type ON contracts(contract_type)",
            
            # Индекс для сортировки по дате окончания (для просроченных)
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_end_date ON contracts(end_date)",
            
            # Индекс для сортировки по сумме
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_amount ON contracts(total_amount DESC)",
            
            # Составной индекс для активных договоров
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_active ON contracts(status, end_date) WHERE status = 'active'",
            
            # Индекс для прогресса по дате отчета
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_report_date ON contract_progress(report_date)",
            
            # Составной индекс для прогресса
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_contract_date ON contract_progress(contract_id, report_date)",
            
            # Индекс для подрядчиков
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_contractor ON contracts(contractor_name)",
            
            # Индекс для даты создания (для сортировки)
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contracts_created ON contracts(created_at DESC)"
        ]
        
        created_indexes = []
        
        try:
            engine = self.db.get_engine()
            if engine is None:
                logger.error("Не удалось получить подключение к БД для создания индексов")
                return created_indexes
            
            with engine.connect() as conn:
                for index_sql in indexes:
                    try:
                        with self.measure_query_time(f"create_index"):
                            conn.execute(index_sql)
                            conn.commit()
                        
                        index_name = index_sql.split("IF NOT EXISTS ")[1].split(" ON")[0]
                        created_indexes.append(index_name)
                        logger.info(f"Создан индекс: {index_name}")
                        
                    except Exception as e:
                        logger.warning(f"Не удалось создать индекс: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Ошибка создания индексов: {e}")
        
        return created_indexes
    
    def analyze_query_performance(self, query: str, params: Optional[tuple] = None) -> Dict[str, Any]:
        """Анализирует производительность запроса с помощью EXPLAIN"""
        try:
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            
            engine = self.db.get_engine()
            if engine is None:
                return {}
            
            with engine.connect() as conn:
                if params:
                    result = conn.execute(explain_query, params)
                else:
                    result = conn.execute(explain_query)
                
                explain_result = result.fetchone()[0]
                
                # Извлекаем ключевые метрики
                plan = explain_result[0]['Plan']
                
                return {
                    'execution_time': explain_result[0].get('Execution Time', 0),
                    'planning_time': explain_result[0].get('Planning Time', 0),
                    'total_cost': plan.get('Total Cost', 0),
                    'rows': plan.get('Actual Rows', 0),
                    'node_type': plan.get('Node Type', ''),
                    'index_used': 'Index' in plan.get('Node Type', '')
                }
                
        except Exception as e:
            logger.error(f"Ошибка анализа производительности запроса: {e}")
            return {}
    
    def get_query_statistics(self) -> Dict[str, Any]:
        """Возвращает статистику выполнения запросов"""
        return {
            'query_stats': self.query_stats,
            'slow_queries': {
                name: stats for name, stats in self.query_stats.items()
                if stats['avg_time'] > self.slow_query_threshold
            },
            'total_queries': sum(stats['count'] for stats in self.query_stats.values()),
            'total_time': sum(stats['total_time'] for stats in self.query_stats.values())
        }


# Глобальный экземпляр оптимизатора
query_optimizer = QueryOptimizer()
