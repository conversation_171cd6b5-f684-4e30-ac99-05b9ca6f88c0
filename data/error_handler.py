"""
Система обработки ошибок и fallback данных
"""
import logging
import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, List, Callable, Union
from datetime import datetime, timedelta
import json
import traceback
from functools import wraps
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Уровни серьезности ошибок"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Категории ошибок"""
    DATABASE = "database"
    NETWORK = "network"
    DATA_PROCESSING = "data_processing"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    SYSTEM = "system"


class ErrorHandler:
    """Класс для обработки ошибок и предоставления fallback данных"""
    
    def __init__(self):
        self.error_log = []
        self.fallback_data_cache = {}
        self.error_counts = {}
        self.max_error_log_size = 1000
    
    def log_error(
        self,
        error: Exception,
        context: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """Логирует ошибку с контекстом"""
        error_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.error_log)}"
        
        error_entry = {
            'id': error_id,
            'timestamp': datetime.now(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'severity': severity.value,
            'category': category.value,
            'traceback': traceback.format_exc(),
            'additional_info': additional_info or {}
        }
        
        # Добавляем в лог
        self.error_log.append(error_entry)
        
        # Ограничиваем размер лога
        if len(self.error_log) > self.max_error_log_size:
            self.error_log = self.error_log[-self.max_error_log_size:]
        
        # Обновляем счетчики
        error_key = f"{category.value}_{type(error).__name__}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Логируем в зависимости от серьезности
        if severity == ErrorSeverity.CRITICAL:
            logger.critical(f"[{error_id}] {context}: {error}")
        elif severity == ErrorSeverity.HIGH:
            logger.error(f"[{error_id}] {context}: {error}")
        elif severity == ErrorSeverity.MEDIUM:
            logger.warning(f"[{error_id}] {context}: {error}")
        else:
            logger.info(f"[{error_id}] {context}: {error}")
        
        return error_id
    
    def get_fallback_contracts_data(self) -> pd.DataFrame:
        """Возвращает fallback данные для договоров"""
        if 'contracts' in self.fallback_data_cache:
            return self.fallback_data_cache['contracts']
        
        # Генерируем базовые тестовые данные
        import random
        from datetime import datetime, timedelta
        
        contract_types = ['construction', 'supply', 'services', 'maintenance']
        statuses = ['active', 'completed', 'suspended', 'cancelled']
        contractors = [
            'ООО "СтройТех"', 'АО "ГазСтрой"', 'ООО "ЭнергоМонтаж"',
            'ООО "ТехСервис"', 'АО "ПромСтрой"', 'ООО "ГазТранс"'
        ]
        
        data = []
        for i in range(30):  # Меньше данных для fallback
            start_date = datetime.now() - timedelta(days=random.randint(30, 365))
            end_date = start_date + timedelta(days=random.randint(90, 365))
            
            data.append({
                'contract_id': i + 1,
                'contract_number': f"FB-{2024}-{i+1:03d}",
                'contract_name': f"Fallback договор №{i+1}",
                'contract_type': random.choice(contract_types),
                'status': random.choice(statuses),
                'total_amount': random.randint(1000000, 50000000),
                'start_date': start_date,
                'end_date': end_date,
                'contractor_name': random.choice(contractors),
                'created_at': start_date,
                'updated_at': datetime.now()
            })
        
        df = pd.DataFrame(data)
        df['start_date'] = pd.to_datetime(df['start_date'])
        df['end_date'] = pd.to_datetime(df['end_date'])
        
        # Кешируем данные
        self.fallback_data_cache['contracts'] = df
        
        return df
    
    def get_fallback_summary_data(self) -> Dict[str, Any]:
        """Возвращает fallback данные для сводки"""
        if 'summary' in self.fallback_data_cache:
            return self.fallback_data_cache['summary']
        
        summary = {
            'total_contracts': 30,
            'active_contracts': 15,
            'completed_contracts': 10,
            'suspended_contracts': 3,
            'cancelled_contracts': 2,
            'overdue_contracts': 2,
            'total_amount': 750000000,  # 750 млн
            'average_amount': 25000000,  # 25 млн
            'average_duration': 180,  # 180 дней
            'overdue_rate': 6.7  # 6.7%
        }
        
        # Кешируем данные
        self.fallback_data_cache['summary'] = summary
        
        return summary
    
    def show_error_notification(
        self,
        error_id: str,
        user_message: str,
        show_details: bool = False,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM
    ) -> None:
        """Показывает уведомление об ошибке пользователю"""
        
        # Выбираем тип уведомления в зависимости от серьезности
        if severity == ErrorSeverity.CRITICAL:
            st.error(f"🚨 Критическая ошибка: {user_message}")
        elif severity == ErrorSeverity.HIGH:
            st.error(f"❌ Ошибка: {user_message}")
        elif severity == ErrorSeverity.MEDIUM:
            st.warning(f"⚠️ Предупреждение: {user_message}")
        else:
            st.info(f"ℹ️ Информация: {user_message}")
        
        # Показываем детали если нужно
        if show_details:
            error_entry = next(
                (e for e in self.error_log if e['id'] == error_id), 
                None
            )
            
            if error_entry:
                with st.expander("Детали ошибки"):
                    st.code(f"""
Время: {error_entry['timestamp']}
Тип: {error_entry['error_type']}
Контекст: {error_entry['context']}
Категория: {error_entry['category']}
ID ошибки: {error_id}
                    """)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Возвращает статистику ошибок"""
        if not self.error_log:
            return {
                'total_errors': 0,
                'errors_by_category': {},
                'errors_by_severity': {},
                'recent_errors': []
            }
        
        # Группируем по категориям
        errors_by_category = {}
        errors_by_severity = {}
        
        for error in self.error_log:
            category = error['category']
            severity = error['severity']
            
            errors_by_category[category] = errors_by_category.get(category, 0) + 1
            errors_by_severity[severity] = errors_by_severity.get(severity, 0) + 1
        
        # Последние ошибки
        recent_errors = sorted(
            self.error_log, 
            key=lambda x: x['timestamp'], 
            reverse=True
        )[:10]
        
        return {
            'total_errors': len(self.error_log),
            'errors_by_category': errors_by_category,
            'errors_by_severity': errors_by_severity,
            'error_counts': self.error_counts,
            'recent_errors': recent_errors
        }
    
    def clear_error_log(self) -> int:
        """Очищает лог ошибок"""
        count = len(self.error_log)
        self.error_log.clear()
        self.error_counts.clear()
        logger.info(f"Очищено {count} записей из лога ошибок")
        return count


# Глобальный экземпляр обработчика ошибок
error_handler = ErrorHandler()


def handle_errors(
    fallback_value: Any = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.SYSTEM,
    show_notification: bool = True,
    context: Optional[str] = None
):
    """
    Декоратор для обработки ошибок в функциях
    
    Args:
        fallback_value: Значение, возвращаемое при ошибке
        severity: Уровень серьезности ошибки
        category: Категория ошибки
        show_notification: Показывать ли уведомление пользователю
        context: Дополнительный контекст для ошибки
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Определяем контекст
                error_context = context or f"Функция {func.__name__}"
                
                # Логируем ошибку
                error_id = error_handler.log_error(
                    error=e,
                    context=error_context,
                    severity=severity,
                    category=category,
                    additional_info={
                        'function': func.__name__,
                        'args': str(args)[:200],  # Ограничиваем длину
                        'kwargs': str(kwargs)[:200]
                    }
                )
                
                # Показываем уведомление
                if show_notification:
                    user_message = f"Произошла ошибка в {func.__name__}"
                    if severity in [ErrorSeverity.LOW, ErrorSeverity.MEDIUM]:
                        user_message += ". Используются резервные данные."
                    
                    error_handler.show_error_notification(
                        error_id=error_id,
                        user_message=user_message,
                        severity=severity
                    )
                
                # Возвращаем fallback значение
                if fallback_value is not None:
                    return fallback_value
                
                # Для некоторых категорий возвращаем специальные fallback данные
                if category == ErrorCategory.DATABASE:
                    if 'contracts' in func.__name__.lower():
                        return error_handler.get_fallback_contracts_data()
                    elif 'summary' in func.__name__.lower():
                        return error_handler.get_fallback_summary_data()
                
                return None
        
        return wrapper
    return decorator
