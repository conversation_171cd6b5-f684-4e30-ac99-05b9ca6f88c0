"""
Модуль для загрузки данных из базы данных
"""
import pandas as pd
import streamlit as st
from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any, List, Tuple
import logging

from data.database import db_connection
from data.models import Contract, ContractProgress, DataProcessor
from data.cache import cached_data, CacheTTL, CacheLevel, data_cache
from data.error_handler import handle_errors, ErrorSeverity, ErrorCategory, error_handler
from data.query_optimizer import query_optimizer
from config.constants import SQL_QUERIES
from config.settings import DASHBOARD_CONFIG

logger = logging.getLogger(__name__)


class DataLoader:
    """Класс для загрузки данных из базы данных"""
    
    def __init__(self):
        self.db = db_connection
        self.processor = DataProcessor()
    
    @cached_data(ttl=CacheTTL.CONTRACTS_LIST, level=CacheLevel.MEMORY, key_prefix="contracts_")
    @handle_errors(
        fallback_value=None,
        severity=ErrorSeverity.MEDIUM,
        category=ErrorCategory.DATABASE,
        context="Загрузка обзора договоров"
    )
    def load_contracts_overview(self) -> Optional[pd.DataFrame]:
        """Загружает общий обзор договоров с кешированием и обработкой ошибок"""
        # Если база данных недоступна, возвращаем fallback данные
        if not self.db.test_connection():
            logger.warning("База данных недоступна, используем fallback данные")
            return error_handler.get_fallback_contracts_data()

        # Используем оптимизированный запрос
        query, params = query_optimizer.get_optimized_contracts_overview_query()

        with query_optimizer.measure_query_time("load_contracts_overview"):
            df = self.db.execute_query(query, params)

        if isinstance(df, pd.DataFrame) and not df.empty:
            # Преобразование типов данных
            df['total_amount'] = pd.to_numeric(df['total_amount'], errors='coerce')
            df['start_date'] = pd.to_datetime(df['start_date'])
            df['end_date'] = pd.to_datetime(df['end_date'])

            logger.info(f"Загружено {len(df)} договоров из БД")
            return df
        else:
            logger.warning("Получен пустой результат из БД, используем fallback данные")
            return error_handler.get_fallback_contracts_data()
    
    @cached_data(ttl=CacheTTL.MONTHLY_PROGRESS, level=CacheLevel.MEMORY, key_prefix="progress_")
    def load_monthly_progress(self, start_date: date, end_date: date) -> Optional[pd.DataFrame]:
        """Загружает данные о месячном прогрессе с кешированием"""
        try:
            if not self.db.test_connection():
                logger.warning("База данных недоступна для загрузки прогресса")
                return None

            query = SQL_QUERIES['monthly_progress']
            df = self.db.execute_query(query, (start_date, end_date))

            if df is not None and not df.empty:
                # Преобразование типов данных
                df['month'] = pd.to_datetime(df['month'])
                df['completed_amount'] = pd.to_numeric(df['completed_amount'], errors='coerce')
                df['planned_amount'] = pd.to_numeric(df['planned_amount'], errors='coerce')
                df['contracts_count'] = pd.to_numeric(df['contracts_count'], errors='coerce')

                # Вычисление процента выполнения
                df['completion_rate'] = (
                    df['completed_amount'] / df['planned_amount'] * 100
                ).fillna(0)

                logger.info(f"Загружено {len(df)} месячных отчетов")
                return df
            else:
                logger.warning("Нет данных о прогрессе за указанный период")
                return None

        except Exception as e:
            logger.error(f"Ошибка загрузки месячного прогресса: {e}")
            st.error(f"Ошибка загрузки данных о прогрессе: {e}")
            return None
    
    @st.cache_data(ttl=DASHBOARD_CONFIG['refresh_interval'])
    def load_contract_details(_self, contract_id: int) -> Optional[pd.DataFrame]:
        """Загружает детальную информацию о договоре"""
        try:
            query = SQL_QUERIES['contract_details']
            df = _self.db.execute_query(query, (contract_id,))
            
            if df is not None and not df.empty:
                df['total_amount'] = pd.to_numeric(df['total_amount'], errors='coerce')
                df['completed_amount'] = pd.to_numeric(df['completed_amount'], errors='coerce')
                df['completion_percentage'] = pd.to_numeric(df['completion_percentage'], errors='coerce')
                
                logger.info(f"Загружены детали договора {contract_id}")
            
            return df
        except Exception as e:
            logger.error(f"Ошибка загрузки деталей договора {contract_id}: {e}")
            st.error(f"Ошибка загрузки деталей договора: {e}")
            return None
    
    def get_contracts_by_status(self, status: str) -> Optional[List[Contract]]:
        """Получает договоры по статусу"""
        try:
            query = f"""
            SELECT * FROM contracts 
            WHERE status = %s
            ORDER BY start_date DESC
            """
            df = self.db.execute_query(query, (status,))
            
            if df is not None and not df.empty:
                return self.processor.process_contracts_data(df)
            return []
        except Exception as e:
            logger.error(f"Ошибка загрузки договоров по статусу {status}: {e}")
            return None
    
    def get_contracts_by_type(self, contract_type: str) -> Optional[List[Contract]]:
        """Получает договоры по типу"""
        try:
            query = f"""
            SELECT * FROM contracts 
            WHERE contract_type = %s
            ORDER BY total_amount DESC
            """
            df = self.db.execute_query(query, (contract_type,))
            
            if df is not None and not df.empty:
                return self.processor.process_contracts_data(df)
            return []
        except Exception as e:
            logger.error(f"Ошибка загрузки договоров по типу {contract_type}: {e}")
            return None
    
    def get_overdue_contracts(self) -> Optional[List[Contract]]:
        """Получает просроченные договоры"""
        try:
            today = date.today()
            query = f"""
            SELECT * FROM contracts 
            WHERE end_date < %s AND status = 'active'
            ORDER BY end_date ASC
            """
            df = self.db.execute_query(query, (today,))
            
            if isinstance(df, pd.DataFrame) and not df.empty:
                return self.processor.process_contracts_data(df)
            return []
        except Exception as e:
            logger.error(f"Ошибка загрузки просроченных договоров: {e}")
            return None
    
    def get_top_contracts_by_amount(self, limit: int = 10) -> Optional[List[Contract]]:
        """Получает топ договоров по сумме"""
        try:
            query = f"""
            SELECT * FROM contracts 
            ORDER BY total_amount DESC
            LIMIT %s
            """
            df = self.db.execute_query(query, (limit,))
            
            if df is not None and not df.empty:
                return self.processor.process_contracts_data(df)
            return []
        except Exception as e:
            logger.error(f"Ошибка загрузки топ договоров: {e}")
            return None

    def load_summary_data(self) -> Dict[str, Any]:
        """Загружает сводные данные для дашборда (алиас для get_dashboard_summary)"""
        return self.get_dashboard_summary()

    def get_contract_statuses(self) -> List[str]:
        """Получает список статусов договоров"""
        try:
            if not self.db.test_connection():
                return ['active', 'completed', 'suspended', 'cancelled']

            query = "SELECT DISTINCT status FROM contracts WHERE status IS NOT NULL ORDER BY status"
            result_df = self.db.execute_query(query)

            if result_df is not None and not result_df.empty:
                return result_df['status'].tolist()

            return ['active', 'completed', 'suspended', 'cancelled']

        except Exception as e:
            logger.error(f"Ошибка получения статусов договоров: {e}")
            return ['active', 'completed', 'suspended', 'cancelled']

    def get_contract_types(self) -> List[str]:
        """Получает список типов договоров"""
        try:
            if not self.db.test_connection():
                return ['construction', 'supply', 'services', 'maintenance']

            query = "SELECT DISTINCT contract_type FROM contracts WHERE contract_type IS NOT NULL ORDER BY contract_type"
            result_df = self.db.execute_query(query)

            if result_df is not None and not result_df.empty:
                return result_df['contract_type'].tolist()

            return ['construction', 'supply', 'services', 'maintenance']

        except Exception as e:
            logger.error(f"Ошибка получения типов договоров: {e}")
            return ['construction', 'supply', 'services', 'maintenance']

    def get_departments(self) -> List[str]:
        """Получает список департаментов (fallback данные, так как колонка department отсутствует в схеме)"""
        try:
            # Поскольку в схеме БД нет колонки department, возвращаем предустановленный список
            # В будущем можно добавить колонку department в таблицу contracts или создать отдельную таблицу departments
            logger.info("Возвращаем предустановленный список департаментов (колонка department отсутствует в схеме)")
            return ['IT', 'Закупки', 'Финансы', 'Юридический', 'Производство', 'Логистика', 'Строительство', 'Снабжение']

        except Exception as e:
            logger.error(f"Ошибка получения департаментов: {e}")
            return ['IT', 'Закупки', 'Финансы', 'Юридический', 'Производство', 'Логистика']

    @cached_data(ttl=CacheTTL.DASHBOARD_SUMMARY, level=CacheLevel.MEMORY, key_prefix="summary_")
    @handle_errors(
        fallback_value=None,
        severity=ErrorSeverity.MEDIUM,
        category=ErrorCategory.DATABASE,
        context="Получение сводки дашборда"
    )
    def get_dashboard_summary(self) -> Dict[str, Any]:
        """Получает сводную информацию для дашборда с кешированием и обработкой ошибок"""
        # Если база данных недоступна, возвращаем fallback данные
        if not self.db.test_connection():
            logger.warning("База данных недоступна, используем fallback данные для сводки")
            return error_handler.get_fallback_summary_data()

        # Используем оптимизированный запрос
        summary_query = query_optimizer.get_optimized_dashboard_summary_query()

        try:
            with query_optimizer.measure_query_time("get_dashboard_summary"):
                summary_df = self.db.execute_query(summary_query)

                if summary_df is not None and not summary_df.empty:
                    summary = summary_df.iloc[0].to_dict()

                    # Добавляем процент просроченных
                    if summary['total_contracts'] > 0:
                        summary['overdue_rate'] = (
                            summary['overdue_contracts'] / summary['total_contracts'] * 100
                        )
                    else:
                        summary['overdue_rate'] = 0

                    return summary

                return {}
        except Exception as e:
            logger.error(f"Ошибка получения сводной информации: {e}")
            return self._generate_test_summary()
    
    def get_date_range_options(self) -> List[Tuple[str, int]]:
        """Возвращает опции для выбора диапазона дат"""
        return [
            ("Последние 3 месяца", 3),
            ("Последние 6 месяцев", 6),
            ("Последний год", 12),
            ("Последние 2 года", 24),
        ]

    def _generate_test_data(self) -> pd.DataFrame:
        """Генерирует тестовые данные для демонстрации"""
        import random
        from datetime import datetime, timedelta

        # Тестовые данные
        contract_types = ['construction', 'supply', 'services', 'maintenance']
        statuses = ['active', 'completed', 'suspended', 'cancelled']
        contractors = [
            'ООО "СтройИнвест"', 'АО "ТехСервис"', 'ООО "МегаСтрой"',
            'ЗАО "ПромТех"', 'ООО "ГлавСтрой"', 'АО "СпецМонтаж"'
        ]

        data = []
        for i in range(50):  # Генерируем 50 тестовых договоров
            start_date = datetime.now() - timedelta(days=random.randint(30, 730))
            end_date = start_date + timedelta(days=random.randint(90, 365))

            data.append({
                'contract_id': i + 1,
                'contract_number': f"Д-{2024}-{i+1:03d}",
                'contract_name': f"Договор на {random.choice(['строительство', 'поставку', 'услуги', 'обслуживание'])} №{i+1}",
                'contract_type': random.choice(contract_types),
                'status': random.choice(statuses),
                'total_amount': random.randint(1000000, 100000000),
                'start_date': start_date,
                'end_date': end_date,
                'contractor_name': random.choice(contractors),
                'created_at': start_date,
                'updated_at': datetime.now()
            })

        df = pd.DataFrame(data)
        df['start_date'] = pd.to_datetime(df['start_date'])
        df['end_date'] = pd.to_datetime(df['end_date'])

        return df

    def _generate_test_summary(self) -> Dict[str, Any]:
        """Генерирует тестовую сводную информацию"""
        return {
            'total_contracts': 50,
            'active_contracts': 25,
            'completed_contracts': 20,
            'overdue_contracts': 3,
            'total_amount': 1500000000,  # 1.5 млрд
            'average_amount': 30000000,  # 30 млн
            'overdue_rate': 6.0  # 6%
        }

    def invalidate_cache(self, pattern: Optional[str] = None) -> int:
        """Инвалидирует кеш данных"""
        invalidated_count = data_cache.invalidate_cache(pattern, CacheLevel.MEMORY)
        logger.info(f"Инвалидировано {invalidated_count} записей кеша")
        return invalidated_count

    def get_cache_stats(self) -> Dict[str, Any]:
        """Возвращает статистику кеша"""
        return data_cache.get_cache_stats()

    def cleanup_cache(self) -> int:
        """Очищает устаревший кеш"""
        return data_cache.cleanup_expired_cache()

    def get_date_range_options(self) -> List[Tuple[str, int]]:
        """Возвращает опции для выбора периода"""
        return [
            ("Последний месяц", 1),
            ("Последние 3 месяца", 3),
            ("Последний год", 12),
            ("Последние 2 года", 24),
            ("Все время", 60)
        ]

    def get_performance_stats(self) -> Dict[str, Any]:
        """Возвращает статистику производительности загрузчика данных"""
        return {
            'cache_stats': self.get_cache_stats(),
            'query_stats': query_optimizer.get_query_statistics(),
            'db_stats': self.db.get_query_performance_stats(),
            'error_stats': error_handler.get_error_statistics()
        }

    def run_health_check(self) -> Dict[str, Any]:
        """Выполняет проверку здоровья загрузчика данных"""
        from data.health_monitor import health_monitor
        return health_monitor.run_full_health_check()

    def optimize_database(self) -> Dict[str, Any]:
        """Выполняет оптимизацию базы данных"""
        try:
            # Создаем индексы
            created_indexes = query_optimizer.create_database_indexes()

            # Очищаем устаревший кеш
            cleaned_cache = self.cleanup_cache()

            # Очищаем лог ошибок
            cleared_errors = error_handler.clear_error_log()

            logger.info("Оптимизация базы данных завершена")

            return {
                'success': True,
                'created_indexes': created_indexes,
                'cleaned_cache_entries': cleaned_cache,
                'cleared_errors': cleared_errors,
                'timestamp': datetime.now()
            }

        except Exception as e:
            logger.error(f"Ошибка оптимизации базы данных: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }


# Глобальный экземпляр загрузчика данных
data_loader = DataLoader()
