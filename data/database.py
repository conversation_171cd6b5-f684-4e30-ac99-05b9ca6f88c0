"""
Модуль для работы с базой данных PostgreSQL
"""
import pandas as pd
import psycopg2
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError, OperationalError, TimeoutError
from sqlalchemy.pool import QueuePool
import streamlit as st
from typing import Optional, Dict, Any, List, Tuple
import logging
import time
from contextlib import contextmanager

from config.settings import DATABASE_CONFIG

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseConnection:
    """Класс для управления подключением к базе данных"""

    def __init__(self):
        self.engine = None
        self.connection_string = self._build_connection_string()
        self.query_stats = {
            'total_queries': 0,
            'failed_queries': 0,
            'slow_queries': 0,
            'total_time': 0
        }
        self.slow_query_threshold = 1.0  # секунды
    
    def _build_connection_string(self) -> str:
        """Создает строку подключения к базе данных"""
        return (
            f"postgresql://{DATABASE_CONFIG['user']}:"
            f"{DATABASE_CONFIG['password']}@"
            f"{DATABASE_CONFIG['host']}:"
            f"{DATABASE_CONFIG['port']}/"
            f"{DATABASE_CONFIG['database']}"
        )
    
    @st.cache_resource
    def get_engine(_self):
        """Получает движок SQLAlchemy с кешированием и оптимизированными настройками"""
        try:
            if _self.engine is None:
                _self.engine = create_engine(
                    _self.connection_string,
                    poolclass=QueuePool,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    pool_recycle=3600,
                    pool_timeout=30,
                    echo=False,  # Отключаем логирование SQL в продакшене
                    connect_args={
                        "connect_timeout": 10,
                        "application_name": "dashboard_app"
                    }
                )
                logger.info("Создано подключение к базе данных")
            return _self.engine
        except SQLAlchemyError as e:
            logger.error(f"Ошибка создания подключения к БД: {e}")
            st.error(f"Не удалось подключиться к базе данных: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Тестирует подключение к базе данных"""
        try:
            engine = self.get_engine()
            if engine is None:
                return False
            
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Ошибка тестирования подключения: {e}")
            return False
    
    def execute_query(
        self,
        query: str,
        params: Optional[tuple] = None,
        max_retries: int = 3,
        timeout: Optional[int] = 30
    ) -> Optional[pd.DataFrame]:
        """Выполняет SQL запрос с retry механизмом и мониторингом производительности"""
        start_time = time.time()
        self.query_stats['total_queries'] += 1

        for attempt in range(max_retries):
            try:
                engine = self.get_engine()
                if engine is None:
                    self.query_stats['failed_queries'] += 1
                    return None

                with engine.connect() as conn:
                    # Устанавливаем таймаут для запроса
                    if timeout:
                        conn.execute(text(f"SET statement_timeout = {timeout * 1000}"))

                    # Выполняем запрос с prepared statement
                    if params:
                        result = pd.read_sql(text(query), conn, params=params)
                    else:
                        result = pd.read_sql(text(query), conn)

                # Записываем статистику
                execution_time = time.time() - start_time
                self.query_stats['total_time'] += execution_time

                if execution_time > self.slow_query_threshold:
                    self.query_stats['slow_queries'] += 1
                    logger.warning(f"Медленный запрос ({execution_time:.2f}s): {query[:100]}...")

                logger.debug(f"Запрос выполнен за {execution_time:.2f}s. Получено {len(result)} строк")
                return result

            except (OperationalError, TimeoutError) as e:
                logger.warning(f"Попытка {attempt + 1}/{max_retries} не удалась: {e}")
                if attempt < max_retries - 1:
                    # Экспоненциальная задержка
                    wait_time = 2 ** attempt
                    logger.info(f"Ожидание {wait_time}s перед повторной попыткой...")
                    time.sleep(wait_time)
                    continue
                else:
                    self.query_stats['failed_queries'] += 1
                    logger.error(f"Все попытки исчерпаны. Ошибка выполнения запроса: {e}")
                    st.error(f"Ошибка подключения к базе данных: {e}")
                    return None

            except SQLAlchemyError as e:
                self.query_stats['failed_queries'] += 1
                logger.error(f"Ошибка выполнения запроса: {e}")
                st.error(f"Ошибка выполнения запроса к базе данных: {e}")
                return None

            except Exception as e:
                self.query_stats['failed_queries'] += 1
                logger.error(f"Неожиданная ошибка: {e}")
                st.error(f"Произошла неожиданная ошибка: {e}")
                return None

        return None
    
    def get_table_info(self, table_name: str) -> Optional[pd.DataFrame]:
        """Получает информацию о структуре таблицы"""
        query = """
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns 
        WHERE table_name = %s
        ORDER BY ordinal_position
        """
        return self.execute_query(query, (table_name,))
    
    def get_table_list(self) -> Optional[List[str]]:
        """Получает список всех таблиц в базе данных"""
        query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
        """
        result = self.execute_query(query)
        if result is not None:
            return result['table_name'].tolist()
        return None

    def execute_paginated_query(
        self,
        query: str,
        params: Optional[tuple] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[Optional[pd.DataFrame], int]:
        """Выполняет запрос с пагинацией и возвращает данные + общее количество"""
        try:
            # Сначала получаем общее количество записей
            count_query = f"SELECT COUNT(*) FROM ({query}) as count_subquery"
            count_result = self.execute_query(count_query, params)

            if count_result is None or count_result.empty:
                return None, 0

            total_count = int(count_result.iloc[0, 0])

            # Затем получаем данные для текущей страницы
            offset = (page - 1) * page_size
            paginated_query = f"{query} LIMIT {page_size} OFFSET {offset}"

            data = self.execute_query(paginated_query, params)

            return data, total_count

        except Exception as e:
            logger.error(f"Ошибка выполнения пагинированного запроса: {e}")
            return None, 0

    def execute_batch_query(self, queries: List[Tuple[str, Optional[tuple]]]) -> List[Optional[pd.DataFrame]]:
        """Выполняет несколько запросов в одной транзакции"""
        results = []

        try:
            engine = self.get_engine()
            if engine is None:
                return [None] * len(queries)

            with engine.begin() as conn:  # Автоматическая транзакция
                for query, params in queries:
                    try:
                        if params:
                            result = pd.read_sql(text(query), conn, params=params)
                        else:
                            result = pd.read_sql(text(query), conn)
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Ошибка в batch запросе: {e}")
                        results.append(None)

            logger.info(f"Выполнено {len(queries)} запросов в batch")
            return results

        except Exception as e:
            logger.error(f"Ошибка выполнения batch запросов: {e}")
            return [None] * len(queries)

    def get_database_stats(self) -> Dict[str, Any]:
        """Возвращает статистику базы данных"""
        try:
            stats_query = """
            SELECT
                schemaname,
                tablename,
                n_tup_ins as inserts,
                n_tup_upd as updates,
                n_tup_del as deletes,
                n_live_tup as live_tuples,
                n_dead_tup as dead_tuples,
                last_vacuum,
                last_autovacuum,
                last_analyze,
                last_autoanalyze
            FROM pg_stat_user_tables
            WHERE schemaname = 'public'
            ORDER BY n_live_tup DESC
            """

            return self.execute_query(stats_query)

        except Exception as e:
            logger.error(f"Ошибка получения статистики БД: {e}")
            return None

    def get_query_performance_stats(self) -> Dict[str, Any]:
        """Возвращает статистику производительности запросов"""
        success_rate = (
            (self.query_stats['total_queries'] - self.query_stats['failed_queries'])
            / self.query_stats['total_queries'] * 100
            if self.query_stats['total_queries'] > 0 else 0
        )

        avg_query_time = (
            self.query_stats['total_time'] / self.query_stats['total_queries']
            if self.query_stats['total_queries'] > 0 else 0
        )

        return {
            **self.query_stats,
            'success_rate': success_rate,
            'avg_query_time': avg_query_time,
            'slow_query_rate': (
                self.query_stats['slow_queries'] / self.query_stats['total_queries'] * 100
                if self.query_stats['total_queries'] > 0 else 0
            )
        }

    @contextmanager
    def get_connection(self):
        """Контекстный менеджер для получения подключения"""
        engine = self.get_engine()
        if engine is None:
            yield None
            return

        conn = None
        try:
            conn = engine.connect()
            yield conn
        except Exception as e:
            logger.error(f"Ошибка подключения: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()


# Глобальный экземпляр подключения к БД
db_connection = DatabaseConnection()
