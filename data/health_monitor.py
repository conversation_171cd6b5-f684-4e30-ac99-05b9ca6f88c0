"""
Система мониторинга здоровья приложения и базы данных
"""
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import psutil
import streamlit as st

from data.database import db_connection
from data.error_handler import error_handler, ErrorSeverity, ErrorCategory

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Статусы здоровья компонентов"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class HealthMonitor:
    """Класс для мониторинга здоровья системы"""
    
    def __init__(self):
        self.checks_history = []
        self.max_history_size = 100
        self.alert_thresholds = {
            'db_response_time': 2.0,  # секунды
            'memory_usage': 80.0,     # процент
            'cpu_usage': 80.0,        # процент
            'error_rate': 10.0        # процент
        }
    
    def check_database_health(self) -> Dict[str, Any]:
        """Проверяет здоровье базы данных"""
        start_time = time.time()
        
        try:
            # Проверяем подключение
            is_connected = db_connection.test_connection()
            
            if not is_connected:
                return {
                    'status': HealthStatus.CRITICAL.value,
                    'response_time': None,
                    'error': 'Нет подключения к базе данных',
                    'details': {}
                }
            
            # Измеряем время отклика
            test_query = "SELECT 1 as test"
            result = db_connection.execute_query(test_query)
            response_time = time.time() - start_time
            
            if result is None:
                return {
                    'status': HealthStatus.CRITICAL.value,
                    'response_time': response_time,
                    'error': 'Ошибка выполнения тестового запроса',
                    'details': {}
                }
            
            # Получаем статистику БД
            db_stats = db_connection.get_query_performance_stats()
            
            # Определяем статус на основе времени отклика
            if response_time > self.alert_thresholds['db_response_time']:
                status = HealthStatus.WARNING.value
            else:
                status = HealthStatus.HEALTHY.value
            
            return {
                'status': status,
                'response_time': response_time,
                'error': None,
                'details': {
                    'total_queries': db_stats.get('total_queries', 0),
                    'success_rate': db_stats.get('success_rate', 0),
                    'avg_query_time': db_stats.get('avg_query_time', 0),
                    'slow_query_rate': db_stats.get('slow_query_rate', 0)
                }
            }
            
        except Exception as e:
            error_handler.log_error(
                error=e,
                context="Database health check",
                severity=ErrorSeverity.HIGH,
                category=ErrorCategory.DATABASE
            )
            
            return {
                'status': HealthStatus.CRITICAL.value,
                'response_time': time.time() - start_time,
                'error': str(e),
                'details': {}
            }
    
    def check_system_health(self) -> Dict[str, Any]:
        """Проверяет здоровье системы"""
        try:
            # Получаем системные метрики
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Определяем статус
            status = HealthStatus.HEALTHY.value
            
            if (cpu_percent > self.alert_thresholds['cpu_usage'] or 
                memory.percent > self.alert_thresholds['memory_usage']):
                status = HealthStatus.WARNING.value
            
            if cpu_percent > 95 or memory.percent > 95:
                status = HealthStatus.CRITICAL.value
            
            return {
                'status': status,
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_free': disk.free,
                'details': {
                    'cpu_count': psutil.cpu_count(),
                    'memory_total': memory.total,
                    'disk_total': disk.total
                }
            }
            
        except Exception as e:
            error_handler.log_error(
                error=e,
                context="System health check",
                severity=ErrorSeverity.MEDIUM,
                category=ErrorCategory.SYSTEM
            )
            
            return {
                'status': HealthStatus.UNKNOWN.value,
                'error': str(e)
            }
    
    def check_application_health(self) -> Dict[str, Any]:
        """Проверяет здоровье приложения"""
        try:
            # Получаем статистику ошибок
            error_stats = error_handler.get_error_statistics()
            
            # Вычисляем частоту ошибок за последний час
            recent_errors = [
                e for e in error_stats['recent_errors']
                if e['timestamp'] > datetime.now() - timedelta(hours=1)
            ]
            
            error_rate = len(recent_errors)
            
            # Определяем статус
            status = HealthStatus.HEALTHY.value
            
            if error_rate > self.alert_thresholds['error_rate']:
                status = HealthStatus.WARNING.value
            
            if error_rate > 50:  # Более 50 ошибок в час
                status = HealthStatus.CRITICAL.value
            
            return {
                'status': status,
                'total_errors': error_stats['total_errors'],
                'recent_errors_count': len(recent_errors),
                'error_rate_per_hour': error_rate,
                'errors_by_category': error_stats['errors_by_category'],
                'errors_by_severity': error_stats['errors_by_severity']
            }
            
        except Exception as e:
            logger.error(f"Ошибка проверки здоровья приложения: {e}")
            return {
                'status': HealthStatus.UNKNOWN.value,
                'error': str(e)
            }
    
    def run_full_health_check(self) -> Dict[str, Any]:
        """Выполняет полную проверку здоровья системы"""
        start_time = datetime.now()
        
        # Выполняем все проверки
        db_health = self.check_database_health()
        system_health = self.check_system_health()
        app_health = self.check_application_health()
        
        # Определяем общий статус
        statuses = [db_health['status'], system_health['status'], app_health['status']]
        
        if HealthStatus.CRITICAL.value in statuses:
            overall_status = HealthStatus.CRITICAL.value
        elif HealthStatus.WARNING.value in statuses:
            overall_status = HealthStatus.WARNING.value
        elif HealthStatus.UNKNOWN.value in statuses:
            overall_status = HealthStatus.UNKNOWN.value
        else:
            overall_status = HealthStatus.HEALTHY.value
        
        health_report = {
            'timestamp': start_time,
            'overall_status': overall_status,
            'database': db_health,
            'system': system_health,
            'application': app_health,
            'check_duration': (datetime.now() - start_time).total_seconds()
        }
        
        # Сохраняем в историю
        self.checks_history.append(health_report)
        
        # Ограничиваем размер истории
        if len(self.checks_history) > self.max_history_size:
            self.checks_history = self.checks_history[-self.max_history_size:]
        
        logger.info(f"Health check completed. Overall status: {overall_status}")
        
        return health_report
    
    def get_health_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Возвращает тренды здоровья за указанный период"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_checks = [
            check for check in self.checks_history
            if check['timestamp'] > cutoff_time
        ]
        
        if not recent_checks:
            return {
                'period_hours': hours,
                'checks_count': 0,
                'trends': {}
            }
        
        # Анализируем тренды
        db_response_times = [
            check['database']['response_time'] 
            for check in recent_checks 
            if check['database']['response_time'] is not None
        ]
        
        cpu_usages = [
            check['system']['cpu_usage'] 
            for check in recent_checks 
            if 'cpu_usage' in check['system']
        ]
        
        memory_usages = [
            check['system']['memory_usage'] 
            for check in recent_checks 
            if 'memory_usage' in check['system']
        ]
        
        return {
            'period_hours': hours,
            'checks_count': len(recent_checks),
            'trends': {
                'db_response_time': {
                    'avg': sum(db_response_times) / len(db_response_times) if db_response_times else 0,
                    'min': min(db_response_times) if db_response_times else 0,
                    'max': max(db_response_times) if db_response_times else 0
                },
                'cpu_usage': {
                    'avg': sum(cpu_usages) / len(cpu_usages) if cpu_usages else 0,
                    'min': min(cpu_usages) if cpu_usages else 0,
                    'max': max(cpu_usages) if cpu_usages else 0
                },
                'memory_usage': {
                    'avg': sum(memory_usages) / len(memory_usages) if memory_usages else 0,
                    'min': min(memory_usages) if memory_usages else 0,
                    'max': max(memory_usages) if memory_usages else 0
                }
            }
        }
    
    def render_health_dashboard(self) -> None:
        """Отрисовывает дашборд здоровья системы"""
        st.subheader("🏥 Мониторинг здоровья системы")
        
        # Выполняем проверку
        health_report = self.run_full_health_check()
        
        # Показываем общий статус
        status_colors = {
            HealthStatus.HEALTHY.value: "🟢",
            HealthStatus.WARNING.value: "🟡",
            HealthStatus.CRITICAL.value: "🔴",
            HealthStatus.UNKNOWN.value: "⚪"
        }
        
        overall_status = health_report['overall_status']
        st.markdown(f"**Общий статус:** {status_colors[overall_status]} {overall_status.upper()}")
        
        # Детали по компонентам
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("### База данных")
            db_status = health_report['database']['status']
            st.markdown(f"Статус: {status_colors[db_status]} {db_status}")
            
            if health_report['database']['response_time']:
                st.metric(
                    "Время отклика", 
                    f"{health_report['database']['response_time']:.3f}s"
                )
        
        with col2:
            st.markdown("### Система")
            sys_status = health_report['system']['status']
            st.markdown(f"Статус: {status_colors[sys_status]} {sys_status}")
            
            if 'cpu_usage' in health_report['system']:
                st.metric("CPU", f"{health_report['system']['cpu_usage']:.1f}%")
                st.metric("Память", f"{health_report['system']['memory_usage']:.1f}%")
        
        with col3:
            st.markdown("### Приложение")
            app_status = health_report['application']['status']
            st.markdown(f"Статус: {status_colors[app_status]} {app_status}")
            
            st.metric(
                "Ошибки (час)", 
                health_report['application']['recent_errors_count']
            )


# Глобальный экземпляр монитора
health_monitor = HealthMonitor()
