"""
Оптимизированная система работы с данными
"""

# Основные модули
from data.database import db_connection, DatabaseConnection
from data.loader import data_loader, DataLoader
from data.models import Contract, ContractProgress, DataProcessor

# Система кеширования
from data.cache import (
    data_cache,
    DataCache,
    CacheLevel,
    CacheTTL,
    cached_data,
    invalidate_data_cache
)

# Оптимизация запросов
from data.query_optimizer import query_optimizer, QueryOptimizer

# Обработка ошибок
from data.error_handler import (
    error_handler,
    Error<PERSON><PERSON>ler,
    ErrorSeverity,
    ErrorCategory,
    handle_errors
)

# Мониторинг здоровья
from data.health_monitor import health_monitor, HealthMonitor, HealthStatus

# Экспорт основных элементов
__all__ = [
    # Основные классы
    'DatabaseConnection',
    'DataLoader',
    'Contract',
    'ContractProgress',
    'DataProcessor',

    # Глобальные экземпляры
    'db_connection',
    'data_loader',

    # Кеширование
    'data_cache',
    'DataCache',
    'CacheLevel',
    'CacheTTL',
    'cached_data',
    'invalidate_data_cache',

    # Оптимизация
    'query_optimizer',
    'QueryOptimizer',

    # Обработка ошибок
    'error_handler',
    'ErrorHandler',
    'ErrorSeverity',
    'ErrorCategory',
    'handle_errors',

    # Мониторинг
    'health_monitor',
    'HealthMonitor',
    'HealthStatus'
]
