"""
Модели данных для работы с договорами
"""
from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List, Dict, Any
import pandas as pd


@dataclass
class Contract:
    """Модель договора"""
    contract_id: int
    contract_number: str
    contract_name: str
    contract_type: str
    status: str
    total_amount: float
    start_date: date
    end_date: date
    contractor_name: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @property
    def duration_days(self) -> int:
        """Продолжительность договора в днях"""
        return (self.end_date - self.start_date).days
    
    @property
    def is_active(self) -> bool:
        """Проверяет, активен ли договор"""
        return self.status == 'active'
    
    @property
    def is_overdue(self) -> bool:
        """Проверяет, просрочен ли договор"""
        return self.end_date < date.today() and self.status == 'active'


@dataclass
class ContractProgress:
    """Модель прогресса выполнения договора"""
    progress_id: int
    contract_id: int
    report_date: date
    amount_planned: float
    amount_completed: float
    completion_percentage: float
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    
    @property
    def is_on_schedule(self) -> bool:
        """Проверяет, выполняется ли договор по графику"""
        return self.amount_completed >= self.amount_planned


@dataclass
class MonthlyReport:
    """Модель месячного отчета"""
    month: date
    total_contracts: int
    active_contracts: int
    completed_contracts: int
    total_planned: float
    total_completed: float
    completion_rate: float
    
    @property
    def month_name(self) -> str:
        """Возвращает название месяца на русском"""
        months = {
            1: 'Январь', 2: 'Февраль', 3: 'Март', 4: 'Апрель',
            5: 'Май', 6: 'Июнь', 7: 'Июль', 8: 'Август',
            9: 'Сентябрь', 10: 'Октябрь', 11: 'Ноябрь', 12: 'Декабрь'
        }
        return f"{months[self.month.month]} {self.month.year}"


class DataProcessor:
    """Класс для обработки и трансформации данных"""
    
    @staticmethod
    def process_contracts_data(df: pd.DataFrame) -> List[Contract]:
        """Преобразует DataFrame в список объектов Contract"""
        contracts = []
        for _, row in df.iterrows():
            contract = Contract(
                contract_id=row['contract_id'],
                contract_number=row['contract_number'],
                contract_name=row['contract_name'],
                contract_type=row['contract_type'],
                status=row['status'],
                total_amount=float(row['total_amount']),
                start_date=pd.to_datetime(row['start_date']).date(),
                end_date=pd.to_datetime(row['end_date']).date(),
                contractor_name=row['contractor_name'],
                created_at=pd.to_datetime(row.get('created_at')) if 'created_at' in row else None,
                updated_at=pd.to_datetime(row.get('updated_at')) if 'updated_at' in row else None
            )
            contracts.append(contract)
        return contracts
    
    @staticmethod
    def process_progress_data(df: pd.DataFrame) -> List[ContractProgress]:
        """Преобразует DataFrame в список объектов ContractProgress"""
        progress_list = []
        for _, row in df.iterrows():
            progress = ContractProgress(
                progress_id=row['progress_id'],
                contract_id=row['contract_id'],
                report_date=pd.to_datetime(row['report_date']).date(),
                amount_planned=float(row['amount_planned']),
                amount_completed=float(row['amount_completed']),
                completion_percentage=float(row['completion_percentage']),
                notes=row.get('notes'),
                created_at=pd.to_datetime(row.get('created_at')) if 'created_at' in row else None
            )
            progress_list.append(progress)
        return progress_list
    
    @staticmethod
    def calculate_monthly_metrics(df: pd.DataFrame) -> pd.DataFrame:
        """Вычисляет месячные метрики"""
        if df.empty:
            return pd.DataFrame()
        
        # Группировка по месяцам
        monthly_data = df.groupby(df['report_date'].dt.to_period('M')).agg({
            'contract_id': 'nunique',
            'amount_planned': 'sum',
            'amount_completed': 'sum'
        }).reset_index()
        
        # Вычисление процента выполнения
        monthly_data['completion_rate'] = (
            monthly_data['amount_completed'] / monthly_data['amount_planned'] * 100
        ).fillna(0)
        
        # Переименование колонок
        monthly_data.columns = [
            'month', 'contracts_count', 'planned_amount', 
            'completed_amount', 'completion_rate'
        ]
        
        return monthly_data
    
    @staticmethod
    def filter_data_by_date_range(df: pd.DataFrame, start_date: date, end_date: date, 
                                 date_column: str = 'report_date') -> pd.DataFrame:
        """Фильтрует данные по диапазону дат"""
        if df.empty:
            return df
        
        mask = (df[date_column] >= start_date) & (df[date_column] <= end_date)
        return df[mask]
    
    @staticmethod
    def get_summary_statistics(contracts: List[Contract]) -> Dict[str, Any]:
        """Вычисляет сводную статистику по договорам"""
        if not contracts:
            return {}
        
        total_contracts = len(contracts)
        active_contracts = sum(1 for c in contracts if c.is_active)
        overdue_contracts = sum(1 for c in contracts if c.is_overdue)
        total_amount = sum(c.total_amount for c in contracts)
        
        return {
            'total_contracts': total_contracts,
            'active_contracts': active_contracts,
            'overdue_contracts': overdue_contracts,
            'completed_contracts': total_contracts - active_contracts,
            'total_amount': total_amount,
            'average_amount': total_amount / total_contracts if total_contracts > 0 else 0,
            'overdue_rate': overdue_contracts / total_contracts * 100 if total_contracts > 0 else 0
        }
