"""
Система кеширования данных для дашборда
"""
import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, List, Callable, Union
from datetime import datetime, timedelta
import logging
import hashlib
import json
from functools import wraps
from enum import Enum

logger = logging.getLogger(__name__)


class CacheLevel(Enum):
    """Уровни кеширования"""
    MEMORY = "memory"          # В памяти приложения
    SESSION = "session"        # В сессии пользователя
    PERSISTENT = "persistent"  # Постоянное хранение


class CacheTTL(Enum):
    """Время жизни кеша для разных типов данных"""
    DASHBOARD_SUMMARY = 300    # 5 минут
    CONTRACTS_LIST = 600       # 10 минут
    CONTRACT_DETAILS = 900     # 15 минут
    MONTHLY_PROGRESS = 1800    # 30 минут
    STATIC_DATA = 3600         # 1 час
    REPORTS = 7200             # 2 часа


class DataCache:
    """Класс для управления кешем данных"""
    
    def __init__(self):
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'invalidations': 0,
            'total_requests': 0
        }
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Генерирует ключ кеша на основе имени функции и параметров"""
        # Создаем уникальный ключ из имени функции и параметров
        key_data = {
            'function': func_name,
            'args': args,
            'kwargs': {k: v for k, v in kwargs.items() if k != '_self'}
        }
        
        # Сериализуем в JSON и создаем хеш
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any], ttl: int) -> bool:
        """Проверяет, действителен ли кеш"""
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = cache_entry['timestamp']
        current_time = datetime.now()
        
        return (current_time - cache_time).total_seconds() < ttl
    
    def get_from_cache(
        self, 
        cache_key: str, 
        ttl: int, 
        level: CacheLevel = CacheLevel.MEMORY
    ) -> Optional[Any]:
        """Получает данные из кеша"""
        self.cache_stats['total_requests'] += 1
        
        try:
            if level == CacheLevel.MEMORY:
                if cache_key in self.memory_cache:
                    cache_entry = self.memory_cache[cache_key]
                    if self._is_cache_valid(cache_entry, ttl):
                        self.cache_stats['hits'] += 1
                        logger.debug(f"Cache hit for key: {cache_key}")
                        return cache_entry['data']
                    else:
                        # Удаляем устаревший кеш
                        del self.memory_cache[cache_key]
            
            elif level == CacheLevel.SESSION:
                if 'cache' not in st.session_state:
                    st.session_state.cache = {}
                
                if cache_key in st.session_state.cache:
                    cache_entry = st.session_state.cache[cache_key]
                    if self._is_cache_valid(cache_entry, ttl):
                        self.cache_stats['hits'] += 1
                        logger.debug(f"Session cache hit for key: {cache_key}")
                        return cache_entry['data']
                    else:
                        del st.session_state.cache[cache_key]
            
            self.cache_stats['misses'] += 1
            logger.debug(f"Cache miss for key: {cache_key}")
            return None
            
        except Exception as e:
            logger.error(f"Ошибка получения данных из кеша: {e}")
            return None
    
    def set_to_cache(
        self, 
        cache_key: str, 
        data: Any, 
        level: CacheLevel = CacheLevel.MEMORY
    ) -> None:
        """Сохраняет данные в кеш"""
        try:
            cache_entry = {
                'data': data,
                'timestamp': datetime.now(),
                'size': self._estimate_size(data)
            }
            
            if level == CacheLevel.MEMORY:
                self.memory_cache[cache_key] = cache_entry
                logger.debug(f"Data cached in memory for key: {cache_key}")
            
            elif level == CacheLevel.SESSION:
                if 'cache' not in st.session_state:
                    st.session_state.cache = {}
                
                st.session_state.cache[cache_key] = cache_entry
                logger.debug(f"Data cached in session for key: {cache_key}")
                
        except Exception as e:
            logger.error(f"Ошибка сохранения данных в кеш: {e}")
    
    def invalidate_cache(
        self, 
        pattern: Optional[str] = None, 
        level: CacheLevel = CacheLevel.MEMORY
    ) -> int:
        """Инвалидирует кеш по паттерну или полностью"""
        invalidated_count = 0
        
        try:
            if level == CacheLevel.MEMORY:
                if pattern is None:
                    invalidated_count = len(self.memory_cache)
                    self.memory_cache.clear()
                else:
                    keys_to_remove = [
                        key for key in self.memory_cache.keys() 
                        if pattern in key
                    ]
                    for key in keys_to_remove:
                        del self.memory_cache[key]
                        invalidated_count += 1
            
            elif level == CacheLevel.SESSION:
                if 'cache' in st.session_state:
                    if pattern is None:
                        invalidated_count = len(st.session_state.cache)
                        st.session_state.cache.clear()
                    else:
                        keys_to_remove = [
                            key for key in st.session_state.cache.keys() 
                            if pattern in key
                        ]
                        for key in keys_to_remove:
                            del st.session_state.cache[key]
                            invalidated_count += 1
            
            self.cache_stats['invalidations'] += invalidated_count
            logger.info(f"Invalidated {invalidated_count} cache entries")
            
        except Exception as e:
            logger.error(f"Ошибка инвалидации кеша: {e}")
        
        return invalidated_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Возвращает статистику кеша"""
        total_requests = self.cache_stats['total_requests']
        hit_rate = (
            self.cache_stats['hits'] / total_requests * 100 
            if total_requests > 0 else 0
        )
        
        return {
            **self.cache_stats,
            'hit_rate': hit_rate,
            'memory_cache_size': len(self.memory_cache),
            'session_cache_size': len(getattr(st.session_state, 'cache', {}))
        }
    
    def _estimate_size(self, data: Any) -> int:
        """Оценивает размер данных в байтах"""
        try:
            if isinstance(data, pd.DataFrame):
                return data.memory_usage(deep=True).sum()
            elif isinstance(data, (dict, list)):
                return len(str(data))
            else:
                return len(str(data))
        except:
            return 0
    
    def cleanup_expired_cache(self) -> int:
        """Очищает устаревший кеш"""
        cleaned_count = 0
        current_time = datetime.now()
        
        try:
            # Очистка memory cache
            expired_keys = []
            for key, entry in self.memory_cache.items():
                if 'timestamp' in entry:
                    age = (current_time - entry['timestamp']).total_seconds()
                    # Удаляем записи старше 1 часа
                    if age > 3600:
                        expired_keys.append(key)
            
            for key in expired_keys:
                del self.memory_cache[key]
                cleaned_count += 1
            
            # Очистка session cache
            if 'cache' in st.session_state:
                expired_keys = []
                for key, entry in st.session_state.cache.items():
                    if 'timestamp' in entry:
                        age = (current_time - entry['timestamp']).total_seconds()
                        if age > 3600:
                            expired_keys.append(key)
                
                for key in expired_keys:
                    del st.session_state.cache[key]
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired cache entries")
                
        except Exception as e:
            logger.error(f"Ошибка очистки устаревшего кеша: {e}")
        
        return cleaned_count


# Глобальный экземпляр кеша
data_cache = DataCache()


def cached_data(
    ttl: Union[int, CacheTTL] = CacheTTL.DASHBOARD_SUMMARY,
    level: CacheLevel = CacheLevel.MEMORY,
    key_prefix: str = ""
):
    """
    Декоратор для кеширования данных
    
    Args:
        ttl: Время жизни кеша в секундах или CacheTTL enum
        level: Уровень кеширования
        key_prefix: Префикс для ключа кеша
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Получаем TTL
            cache_ttl = ttl.value if isinstance(ttl, CacheTTL) else ttl
            
            # Генерируем ключ кеша
            cache_key = data_cache._generate_cache_key(
                f"{key_prefix}{func.__name__}", args, kwargs
            )
            
            # Пытаемся получить из кеша
            cached_result = data_cache.get_from_cache(cache_key, cache_ttl, level)
            if cached_result is not None:
                return cached_result
            
            # Выполняем функцию
            result = func(*args, **kwargs)
            
            # Сохраняем в кеш
            if result is not None:
                data_cache.set_to_cache(cache_key, result, level)
            
            return result
        
        return wrapper
    return decorator


def invalidate_data_cache(pattern: Optional[str] = None) -> int:
    """Инвалидирует кеш данных"""
    return data_cache.invalidate_cache(pattern)
