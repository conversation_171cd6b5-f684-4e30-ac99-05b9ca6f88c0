# 📊 Дашборд освоения договоров

Интерактивный дашборд для мониторинга и анализа динамики освоения договоров, построенный на Streamlit и PostgreSQL.

## 🚀 Возможности

- **📈 Интерактивные графики** - визуализация динамики освоения по месяцам
- **📋 Детальные таблицы** - просмотр информации о договорах с фильтрацией
- **🔍 Фильтры и поиск** - быстрый поиск по статусам, типам и названиям
- **📊 Аналитические метрики** - ключевые показатели эффективности
- **⚠️ Мониторинг просрочек** - отслеживание просроченных договоров
- **💾 Экспорт данных** - выгрузка отчетов в CSV/Excel

## 🏗️ Архитектура проекта

```
gaz_gaz/
├── app.py                 # Главное приложение Streamlit
├── run.py                 # Скрипт запуска с проверками
├── components/            # UI компоненты
│   ├── __init__.py
│   ├── charts.py         # Графики и диаграммы
│   └── tables.py         # Таблицы и отчеты
├── data/                  # Работа с данными
│   ├── __init__.py
│   ├── database.py       # Подключение к БД
│   ├── loader.py         # Загрузка данных
│   └── models.py         # Модели данных
├── config/                # Конфигурация
│   ├── __init__.py
│   ├── settings.py       # Настройки приложения
│   └── constants.py      # Константы
├── utils/                 # Утилиты
│   ├── __init__.py
│   └── helpers.py        # Вспомогательные функции
├── sample_data/           # Примеры данных
│   └── create_sample_db.sql
├── static/                # Статические файлы
│   ├── css/
│   └── images/
├── tests/                 # Тесты
│   ├── __init__.py
│   ├── test_helpers.py
│   └── test_models.py
├── docker/                # Docker конфигурация
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── docker-compose.prod.yml
│   ├── Makefile
│   ├── README.md
│   ├── start.sh
│   └── healthcheck.py
├── .env.example          # Пример конфигурации
├── .gitignore            # Игнорируемые файлы
├── Makefile              # Команды для разработки
├── pyproject.toml        # Конфигурация Poetry
└── README.md             # Документация
```

## 🛠️ Установка и настройка

### 1. Быстрый старт

```bash
# Клонирование репозитория
git clone <repository-url>
cd gaz_gaz

# Первоначальная настройка (создает .env и устанавливает зависимости)
make setup

# Или вручную:
# Установка зависимостей через Poetry
poetry install

# Создание файла конфигурации
cp .env.example .env
```

### 2. Настройка базы данных PostgreSQL

1. Создайте базу данных PostgreSQL
2. Выполните SQL скрипт для создания таблиц:

```bash
psql -U your_username -d your_database -f sample_data/create_sample_db.sql
```

### 3. Настройка переменных окружения

Скопируйте `.env.example` в `.env` и заполните настройки:

```bash
cp .env.example .env
```

Отредактируйте `.env`:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=contracts_db
DB_USER=your_username
DB_PASSWORD=your_password

# Application Configuration
APP_TITLE=Дашборд освоения договоров
APP_ICON=📊
DEBUG=True
```

## 🚀 Запуск приложения

### Локальный запуск

```bash
# Быстрый запуск через Makefile
make run

# Запуск в режиме разработки (с автоперезагрузкой)
make run-dev

# Или через Poetry
poetry run streamlit run app.py

# Или через скрипт запуска с проверками
python run.py

# Или активировать окружение и запустить
poetry shell
streamlit run app.py
```

### Docker запуск

```bash
# Быстрый старт (разработка)
./docker/start.sh dev

# С pgAdmin для управления БД
./docker/start.sh dev --with-pgadmin

# Продакшн запуск
./docker/start.sh prod

# Или через Makefile
cd docker
make dev        # разработка
make dev-tools  # с pgAdmin
make prod       # продакшн
```

Приложение будет доступно по адресу: http://localhost:8501

📋 **Подробная документация по Docker:** [docker/README.md](docker/README.md)

## 📊 Структура базы данных

### Таблица `contracts` (договоры)
- `contract_id` - ID договора (PK)
- `contract_number` - Номер договора
- `contract_name` - Название договора
- `contract_type` - Тип договора (construction, supply, services, maintenance)
- `status` - Статус (active, completed, suspended, cancelled)
- `total_amount` - Общая сумма договора
- `start_date` - Дата начала
- `end_date` - Дата окончания
- `contractor_name` - Название подрядчика

### Таблица `contract_progress` (прогресс выполнения)
- `progress_id` - ID записи прогресса (PK)
- `contract_id` - ID договора (FK)
- `report_date` - Дата отчета
- `amount_planned` - Запланированная сумма
- `amount_completed` - Выполненная сумма
- `completion_percentage` - Процент выполнения (вычисляемое поле)
- `notes` - Примечания

## 🎯 Основные функции

### 📈 Графики и визуализация
- Динамика освоения средств по месяцам
- Распределение договоров по статусам
- Топ договоров по сумме
- Временная шкала выполнения договоров

### 📋 Таблицы и отчеты
- Общий обзор договоров с фильтрацией
- Детальная информация о прогрессе
- Список просроченных договоров
- Сводные метрики и статистика

### 🔧 Фильтры и настройки
- Фильтрация по периоду времени
- Фильтрация по статусу и типу договора
- Поиск по названию договора
- Настройка отображения данных

## 🧪 Тестирование

```bash
# Запуск тестов через Makefile
make test

# Запуск с покрытием
make test-cov

# Или через Poetry
poetry run pytest tests/

# Запуск с покрытием через Poetry
poetry run pytest --cov=. --cov-report=html tests/
```

## 🔧 Команды разработки

Проект использует Makefile для упрощения команд разработки:

```bash
# Показать все доступные команды
make help

# Установка и настройка
make setup              # Первоначальная настройка проекта
make install            # Установить зависимости
make dev                # Установить зависимости для разработки

# Запуск
make run                # Запустить приложение
make run-dev            # Запустить в режиме разработки

# Тестирование и проверка кода
make test               # Запустить тесты
make test-cov           # Запустить тесты с покрытием
make lint               # Проверить код линтерами
make format             # Форматировать код
make format-check       # Проверить форматирование
make check              # Полная проверка (форматирование + линтеры + тесты)

# Docker
make docker-dev         # Запустить через Docker (разработка)
make docker-prod        # Запустить через Docker (продакшн)
make docker-tools       # Запустить с pgAdmin

# Утилиты
make clean              # Очистить временные файлы
make update             # Обновить зависимости
make info               # Показать информацию о проекте
make shell              # Активировать виртуальное окружение
```

## 📝 Разработка

### Добавление новых компонентов

1. **Новый график**: добавьте метод в `components/charts.py`
2. **Новая таблица**: добавьте метод в `components/tables.py`
3. **Новый источник данных**: расширьте `data/loader.py`

### Структура кода

- Все компоненты UI находятся в папке `components/`
- Логика работы с данными в папке `data/`
- Конфигурация и константы в папке `config/`
- Вспомогательные функции в папке `utils/`

## 🔧 Конфигурация

Основные настройки находятся в `config/settings.py`:

- `DATABASE_CONFIG` - настройки подключения к БД
- `APP_CONFIG` - настройки приложения
- `DASHBOARD_CONFIG` - настройки дашборда
- `COLOR_SCHEME` - цветовая схема
- `DATA_FORMATS` - форматы отображения данных

## 📚 Зависимости

- **streamlit** - веб-фреймворк для создания дашбордов
- **pandas** - обработка и анализ данных
- **plotly** - интерактивные графики
- **psycopg2-binary** - драйвер PostgreSQL
- **sqlalchemy** - ORM для работы с БД
- **python-dotenv** - загрузка переменных окружения

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции (`git checkout -b feature/new-feature`)
3. Внесите изменения и добавьте тесты
4. Отправьте pull request

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. См. файл `LICENSE` для подробностей.

## 🆘 Поддержка

Если у вас возникли вопросы или проблемы:

1. Проверьте раздел [Issues](../../issues)
2. Создайте новый issue с подробным описанием проблемы
3. Приложите логи и скриншоты при необходимости

## 🔄 Обновления

Следите за обновлениями проекта:
- Новые функции и улучшения
- Исправления ошибок
- Обновления зависимостей

---

**Автор**: igornet0  
**Версия**: 0.1.0  
**Дата создания**: 2025-07-25
