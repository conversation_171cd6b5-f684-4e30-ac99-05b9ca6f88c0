"""
Вспомогательные функции для дашборда
"""
import pandas as pd
import streamlit as st
from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any, List, Tuple
import logging

logger = logging.getLogger(__name__)


def format_currency(amount: float, currency: str = "₽") -> str:
    """Форматирует сумму в валюте"""
    if pd.isna(amount):
        return "—"
    return f"{amount:,.2f} {currency}"


def format_percentage(value: float, decimals: int = 1) -> str:
    """Форматирует процентное значение"""
    if pd.isna(value):
        return "—"
    return f"{value:.{decimals}f}%"


def format_date(date_value: Any, format_str: str = "%d.%m.%Y") -> str:
    """Форматирует дату"""
    if pd.isna(date_value):
        return "—"
    
    if isinstance(date_value, str):
        try:
            date_value = pd.to_datetime(date_value)
        except:
            return date_value
    
    if isinstance(date_value, (datetime, pd.Timestamp)):
        return date_value.strftime(format_str)
    
    return str(date_value)


def calculate_date_range(months_back: int) -> Tuple[date, date]:
    """Вычисляет диапазон дат"""
    end_date = date.today()
    start_date = end_date - timedelta(days=months_back * 30)
    return start_date, end_date


def get_status_color(status: str) -> str:
    """Возвращает цвет для статуса"""
    colors = {
        'active': '#28a745',
        'completed': '#6c757d',
        'suspended': '#ffc107',
        'cancelled': '#dc3545',
        'overdue': '#dc3545'
    }
    return colors.get(status.lower(), '#6c757d')


def create_status_badge(status: str) -> str:
    """Создает HTML badge для статуса"""
    color = get_status_color(status)
    return f"""
    <span style="
        background-color: {color};
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
    ">{status}</span>
    """


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Безопасное деление с обработкой деления на ноль"""
    if denominator == 0 or pd.isna(denominator) or pd.isna(numerator):
        return default
    return numerator / denominator


def calculate_completion_rate(completed: float, total: float) -> float:
    """Вычисляет процент выполнения"""
    return safe_divide(completed, total) * 100


def get_month_name_ru(month_num: int) -> str:
    """Возвращает название месяца на русском языке"""
    months = {
        1: 'Январь', 2: 'Февраль', 3: 'Март', 4: 'Апрель',
        5: 'Май', 6: 'Июнь', 7: 'Июль', 8: 'Август',
        9: 'Сентябрь', 10: 'Октябрь', 11: 'Ноябрь', 12: 'Декабрь'
    }
    return months.get(month_num, f'Месяц {month_num}')


def format_month_year(date_value: Any) -> str:
    """Форматирует дату в формате 'Месяц ГГГГ'"""
    if pd.isna(date_value):
        return "—"
    
    if isinstance(date_value, str):
        try:
            date_value = pd.to_datetime(date_value)
        except:
            return date_value
    
    if isinstance(date_value, (datetime, pd.Timestamp)):
        month_name = get_month_name_ru(date_value.month)
        return f"{month_name} {date_value.year}"
    
    return str(date_value)


def validate_dataframe(df: pd.DataFrame, required_columns: List[str]) -> bool:
    """Проверяет наличие обязательных колонок в DataFrame"""
    if df is None or df.empty:
        return False
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.warning(f"Отсутствуют колонки: {missing_columns}")
        return False
    
    return True


def clean_numeric_column(df: pd.DataFrame, column: str) -> pd.DataFrame:
    """Очищает числовую колонку от некорректных значений"""
    if column not in df.columns:
        return df
    
    df[column] = pd.to_numeric(df[column], errors='coerce')
    df[column] = df[column].fillna(0)
    return df


def add_calculated_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Добавляет вычисляемые колонки"""
    if df.empty:
        return df
    
    df_calc = df.copy()
    
    # Добавляем процент выполнения если есть нужные колонки
    if 'completed_amount' in df_calc.columns and 'total_amount' in df_calc.columns:
        df_calc['completion_percentage'] = df_calc.apply(
            lambda row: calculate_completion_rate(
                row['completed_amount'], 
                row['total_amount']
            ), axis=1
        )
    
    # Добавляем статус просрочки
    if 'end_date' in df_calc.columns and 'status' in df_calc.columns:
        today = date.today()
        df_calc['is_overdue'] = (
            (pd.to_datetime(df_calc['end_date']).dt.date < today) & 
            (df_calc['status'] == 'active')
        )
    
    return df_calc


@st.cache_data
def load_sample_data() -> pd.DataFrame:
    """Загружает примерные данные для демонстрации"""
    sample_data = {
        'contract_id': range(1, 11),
        'contract_number': [f'Д-2024-{i:03d}' for i in range(1, 11)],
        'contract_name': [
            'Строительство жилого комплекса',
            'Поставка строительных материалов',
            'Ремонт дорожного покрытия',
            'Установка систем отопления',
            'Благоустройство территории',
            'Строительство школы',
            'Поставка медицинского оборудования',
            'Ремонт фасада здания',
            'Установка лифтов',
            'Строительство моста'
        ],
        'contract_type': ['construction', 'supply', 'construction', 'services', 
                         'construction', 'construction', 'supply', 'services', 
                         'services', 'construction'],
        'status': ['active', 'active', 'completed', 'active', 'active',
                  'active', 'completed', 'active', 'suspended', 'active'],
        'total_amount': [50000000, 15000000, 25000000, 8000000, 12000000,
                        75000000, 30000000, 5000000, 18000000, 45000000],
        'start_date': pd.date_range('2024-01-01', periods=10, freq='30D'),
        'end_date': pd.date_range('2024-12-01', periods=10, freq='45D'),
        'contractor_name': [
            'ООО "СтройИнвест"',
            'ЗАО "МатериалСнаб"',
            'ООО "ДорСтрой"',
            'ИП Иванов И.И.',
            'ООО "ЭкоСтрой"',
            'ЗАО "ОбразованиеСтрой"',
            'ООО "МедТехника"',
            'ИП Петров П.П.',
            'ООО "ЛифтСервис"',
            'ЗАО "МостСтрой"'
        ]
    }
    
    return pd.DataFrame(sample_data)


def show_info_message(message: str, icon: str = "ℹ️") -> None:
    """Показывает информационное сообщение"""
    st.info(f"{icon} {message}")


def show_success_message(message: str, icon: str = "✅") -> None:
    """Показывает сообщение об успехе"""
    st.success(f"{icon} {message}")


def show_warning_message(message: str, icon: str = "⚠️") -> None:
    """Показывает предупреждение"""
    st.warning(f"{icon} {message}")


def show_error_message(message: str, icon: str = "❌") -> None:
    """Показывает сообщение об ошибке"""
    st.error(f"{icon} {message}")


def create_download_link(df: pd.DataFrame, filename: str, 
                        file_format: str = 'csv') -> None:
    """Создает ссылку для скачивания данных"""
    if df.empty:
        show_warning_message("Нет данных для скачивания")
        return
    
    if file_format.lower() == 'csv':
        csv = df.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label=f"📥 Скачать {filename}.csv",
            data=csv,
            file_name=f"{filename}.csv",
            mime="text/csv"
        )
    elif file_format.lower() == 'excel':
        # Для Excel нужно использовать BytesIO
        from io import BytesIO
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Данные')
        
        st.download_button(
            label=f"📥 Скачать {filename}.xlsx",
            data=output.getvalue(),
            file_name=f"{filename}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
