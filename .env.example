# Конфигурация дашборда освоения договоров
# Скопируйте этот файл в .env и настройте значения

# =============================================================================
# НАСТРОЙКИ БАЗЫ ДАННЫХ
# =============================================================================

# Хост базы данных PostgreSQL
DB_HOST=localhost

# Порт базы данных (обычно 5432)
DB_PORT=5432

# Имя базы данных
DB_NAME=contracts_db

# Пользователь базы данных
DB_USER=postgres

# Пароль базы данных (ОБЯЗАТЕЛЬНО установите в продакшене!)
DB_PASSWORD=your_secure_password_here

# Настройки пула соединений
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10

# =============================================================================
# НАСТРОЙКИ ПРИЛОЖЕНИЯ
# =============================================================================

# Название приложения
APP_TITLE=Дашборд освоения договоров

# Иконка приложения (эмодзи)
APP_ICON=📊

# Макет страницы (wide или centered)
APP_LAYOUT=wide

# Состояние боковой панели (expanded, collapsed, auto)
APP_SIDEBAR_STATE=expanded

# Тема интерфейса (light, dark, auto)
APP_THEME=light

# Размер страницы для таблиц
APP_PAGE_SIZE=50

# Высота графиков в пикселях
APP_CHART_HEIGHT=400

# Включить анимации (true/false)
APP_ANIMATIONS=true

# =============================================================================
# НАСТРОЙКИ КЕШИРОВАНИЯ
# =============================================================================

# Включить кеширование (true/false)
CACHE_ENABLED=true

# TTL по умолчанию в секундах
CACHE_DEFAULT_TTL=300

# TTL для сводки дашборда
CACHE_DASHBOARD_TTL=300

# TTL для списка договоров
CACHE_CONTRACTS_TTL=600

# TTL для месячного прогресса
CACHE_PROGRESS_TTL=1800

# TTL для статических данных
CACHE_STATIC_TTL=3600

# Максимальный размер кеша
CACHE_MAX_SIZE=1000

# Интервал очистки кеша в секундах
CACHE_CLEANUP_INTERVAL=3600

# =============================================================================
# НАСТРОЙКИ ДАШБОРДА
# =============================================================================

# Диапазон дат по умолчанию в месяцах
DEFAULT_DATE_RANGE=12

# Интервал обновления в секундах
REFRESH_INTERVAL=300

# Максимум договоров для отображения
MAX_CONTRACTS_DISPLAY=1000

# Включить обновления в реальном времени (true/false)
ENABLE_REAL_TIME=false

# Показывать отладочную информацию (true/false)
SHOW_DEBUG_INFO=false

# Доступные форматы экспорта (через запятую)
EXPORT_FORMATS=csv,xlsx,json

# =============================================================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# =============================================================================

# Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Включить запись в файл (true/false)
LOG_FILE_ENABLED=true

# Путь к файлу логов
LOG_FILE_PATH=logs/app.log

# Включить вывод в консоль (true/false)
LOG_CONSOLE_ENABLED=true

# =============================================================================
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# =============================================================================

# Секретный ключ (ОБЯЗАТЕЛЬНО установите в продакшене!)
SECRET_KEY=your_very_secure_secret_key_here_32_chars_minimum

# Таймаут сессии в секундах
SESSION_TIMEOUT=3600

# Максимум попыток входа
MAX_LOGIN_ATTEMPTS=5

# Разрешенные хосты (через запятую)
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# ОБЩИЕ НАСТРОЙКИ
# =============================================================================

# Режим отладки (true/false) - НИКОГДА не включайте в продакшене!
DEBUG=false

# Окружение (development, staging, production)
ENVIRONMENT=development

# Версия приложения
APP_VERSION=1.0.0
