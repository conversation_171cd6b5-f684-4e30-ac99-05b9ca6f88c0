"""
Fallback конфигурация без pydantic
Используется когда pydantic недоступна
"""
import os
from typing import Dict, Any


class FallbackConfig:
    """Простая конфигурация без pydantic"""
    
    def __init__(self):
        # Настройки базы данных
        self.database = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', '5432')),
            'name': os.getenv('DB_NAME', 'contracts_db'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', ''),
            'url': None
        }
        
        # Формируем URL базы данных
        self.database['url'] = (
            f"postgresql://{self.database['user']}:{self.database['password']}"
            f"@{self.database['host']}:{self.database['port']}/{self.database['name']}"
        )
        
        # Настройки приложения
        self.app = {
            'title': '📊 Дашборд освоения договоров',
            'description': 'Система мониторинга и анализа договоров',
            'version': '2.0.0',
            'debug': os.getenv('DEBUG', 'False').lower() == 'true'
        }
        
        # Настройки UI
        self.ui = {
            'title': self.app['title'],
            'theme': os.getenv('UI_THEME', 'light'),
            'animation_enabled': os.getenv('UI_ANIMATIONS', 'True').lower() == 'true',
            'charts_enabled': os.getenv('UI_CHARTS', 'True').lower() == 'true',
            'auto_refresh': os.getenv('UI_AUTO_REFRESH', 'False').lower() == 'true',
            'refresh_interval': int(os.getenv('UI_REFRESH_INTERVAL', '300'))
        }
        
        # Настройки кеширования
        self.cache = {
            'enabled': os.getenv('CACHE_ENABLED', 'True').lower() == 'true',
            'ttl': int(os.getenv('CACHE_TTL', '300')),
            'max_size': int(os.getenv('CACHE_MAX_SIZE', '1000'))
        }
        
        # Настройки логирования
        self.logging = {
            'level': os.getenv('LOG_LEVEL', 'INFO'),
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_enabled': os.getenv('LOG_FILE_ENABLED', 'True').lower() == 'true',
            'file_path': os.getenv('LOG_FILE_PATH', 'logs/app.log')
        }
        
        # Цвета для совместимости
        self.colors = {
            'primary': '#3b82f6',
            'secondary': '#2563eb',
            'success': '#1e40af',
            'warning': '#60a5fa',
            'danger': '#93c5fd',
            'info': '#1d4ed8',
            'background': '#f8fafc',
            'text_primary': '#1f2937',
            'text_secondary': '#6b7280',
            'border': '#3b82f6',
            'card_bg': '#ffffff'
        }


# Создаем глобальный экземпляр
fallback_config = FallbackConfig()

# Экспортируем для совместимости
DATABASE_CONFIG = fallback_config.database
APP_CONFIG = fallback_config.app
UI_CONFIG = fallback_config.ui
CACHE_CONFIG = fallback_config.cache
LOGGING_CONFIG = fallback_config.logging
