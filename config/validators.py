"""
Валидаторы конфигурации приложения
"""
import os
import re
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
from pydantic import ValidationError
import psycopg2
from urllib.parse import urlparse

from config.app_config import AppConfig
from config.component_configs import ComponentsConfig

logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """Исключение для ошибок валидации конфигурации"""
    
    def __init__(self, message: str, errors: List[Dict[str, Any]] = None):
        self.message = message
        self.errors = errors or []
        super().__init__(self.message)


class ConfigValidator:
    """Валидатор конфигурации приложения"""
    
    def __init__(self):
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_database_connection(self, db_config: Dict[str, Any]) -> bool:
        """Валидирует подключение к базе данных"""
        try:
            # Проверяем обязательные параметры
            required_params = ['host', 'port', 'database', 'user']
            missing_params = [param for param in required_params if not db_config.get(param)]
            
            if missing_params:
                self.validation_errors.append({
                    'type': 'database_config',
                    'field': 'required_params',
                    'message': f"Отсутствуют обязательные параметры БД: {', '.join(missing_params)}",
                    'suggestion': "Проверьте переменные окружения DB_HOST, DB_PORT, DB_NAME, DB_USER"
                })
                return False
            
            # Проверяем формат хоста
            host = db_config['host']
            if not self._is_valid_hostname(host):
                self.validation_errors.append({
                    'type': 'database_config',
                    'field': 'host',
                    'message': f"Неверный формат хоста БД: {host}",
                    'suggestion': "Используйте IP-адрес или доменное имя"
                })
                return False
            
            # Проверяем порт
            port = db_config['port']
            if not isinstance(port, int) or not (1 <= port <= 65535):
                self.validation_errors.append({
                    'type': 'database_config',
                    'field': 'port',
                    'message': f"Неверный порт БД: {port}",
                    'suggestion': "Порт должен быть числом от 1 до 65535"
                })
                return False
            
            # Проверяем имя базы данных
            database = db_config['database']
            if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', database):
                self.validation_errors.append({
                    'type': 'database_config',
                    'field': 'database',
                    'message': f"Неверное имя базы данных: {database}",
                    'suggestion': "Имя должно начинаться с буквы и содержать только буквы, цифры и подчеркивания"
                })
                return False
            
            # Предупреждение о пустом пароле
            if not db_config.get('password'):
                self.validation_warnings.append({
                    'type': 'database_config',
                    'field': 'password',
                    'message': "Пароль базы данных не установлен",
                    'suggestion': "Установите переменную окружения DB_PASSWORD для безопасности"
                })
            
            # Пытаемся подключиться к БД
            try:
                conn_string = f"postgresql://{db_config['user']}:{db_config['password']}@{host}:{port}/{database}"
                conn = psycopg2.connect(
                    host=host,
                    port=port,
                    database=database,
                    user=db_config['user'],
                    password=db_config['password'],
                    connect_timeout=5
                )
                conn.close()
                logger.info("Подключение к базе данных успешно проверено")
                return True
                
            except psycopg2.OperationalError as e:
                self.validation_warnings.append({
                    'type': 'database_connection',
                    'field': 'connection',
                    'message': f"Не удалось подключиться к БД: {str(e)}",
                    'suggestion': "Проверьте, что PostgreSQL запущен и доступен по указанным параметрам"
                })
                return True  # Не критическая ошибка, приложение может работать с тестовыми данными
                
        except Exception as e:
            self.validation_errors.append({
                'type': 'database_validation',
                'field': 'general',
                'message': f"Ошибка валидации БД: {str(e)}",
                'suggestion': "Проверьте корректность всех параметров базы данных"
            })
            return False
    
    def validate_file_paths(self, config: AppConfig) -> bool:
        """Валидирует пути к файлам"""
        valid = True
        
        # Проверяем путь к логам
        log_path = Path(config.logging.file_path)
        log_dir = log_path.parent
        
        if not log_dir.exists():
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"Создана директория для логов: {log_dir}")
            except Exception as e:
                self.validation_errors.append({
                    'type': 'file_system',
                    'field': 'log_path',
                    'message': f"Не удалось создать директорию для логов: {log_dir}",
                    'suggestion': f"Проверьте права доступа или создайте директорию вручную: mkdir -p {log_dir}"
                })
                valid = False
        
        # Проверяем права на запись в директорию логов
        if log_dir.exists() and not os.access(log_dir, os.W_OK):
            self.validation_errors.append({
                'type': 'file_system',
                'field': 'log_permissions',
                'message': f"Нет прав на запись в директорию логов: {log_dir}",
                'suggestion': f"Установите права на запись: chmod 755 {log_dir}"
            })
            valid = False
        
        return valid
    
    def validate_environment_variables(self) -> bool:
        """Валидирует переменные окружения"""
        valid = True
        
        # Критически важные переменные
        critical_vars = {
            'DB_HOST': 'Хост базы данных',
            'DB_NAME': 'Имя базы данных',
            'DB_USER': 'Пользователь базы данных'
        }
        
        for var, description in critical_vars.items():
            if not os.getenv(var):
                self.validation_errors.append({
                    'type': 'environment',
                    'field': var,
                    'message': f"Не установлена критически важная переменная окружения: {var}",
                    'suggestion': f"Установите {var} в файле .env или переменных окружения ({description})"
                })
                valid = False
        
        # Рекомендуемые переменные
        recommended_vars = {
            'DB_PASSWORD': 'Пароль базы данных',
            'APP_TITLE': 'Название приложения',
            'LOG_LEVEL': 'Уровень логирования'
        }
        
        for var, description in recommended_vars.items():
            if not os.getenv(var):
                self.validation_warnings.append({
                    'type': 'environment',
                    'field': var,
                    'message': f"Рекомендуется установить переменную окружения: {var}",
                    'suggestion': f"Установите {var} для настройки: {description}"
                })
        
        return valid
    
    def validate_numeric_ranges(self, config: AppConfig) -> bool:
        """Валидирует числовые диапазоны"""
        valid = True
        
        # Проверяем размеры пула соединений
        if config.database.pool_size > config.database.max_overflow:
            self.validation_warnings.append({
                'type': 'database_config',
                'field': 'pool_settings',
                'message': f"Размер пула ({config.database.pool_size}) больше максимального переполнения ({config.database.max_overflow})",
                'suggestion': "Рекомендуется установить max_overflow >= pool_size"
            })
        
        # Проверяем TTL кеша
        if config.cache.dashboard_summary_ttl > config.cache.contracts_list_ttl:
            self.validation_warnings.append({
                'type': 'cache_config',
                'field': 'ttl_settings',
                'message': "TTL сводки дашборда больше TTL списка договоров",
                'suggestion': "Обычно сводка обновляется чаще, чем детальные данные"
            })
        
        # Проверяем производительность
        if config.performance.max_workers > 10:
            self.validation_warnings.append({
                'type': 'performance_config',
                'field': 'max_workers',
                'message': f"Большое количество рабочих процессов: {config.performance.max_workers}",
                'suggestion': "Рекомендуется не более 2-4 процессов на ядро CPU"
            })
        
        return valid
    
    def validate_security_settings(self, config: AppConfig) -> bool:
        """Валидирует настройки безопасности"""
        valid = True
        
        # Проверяем секретный ключ в продакшене
        if config.environment == 'production':
            if not config.security.secret_key:
                self.validation_errors.append({
                    'type': 'security',
                    'field': 'secret_key',
                    'message': "Секретный ключ обязателен в продакшене",
                    'suggestion': "Установите переменную окружения SECRET_KEY с криптографически стойким ключом"
                })
                valid = False
            
            if config.debug:
                self.validation_errors.append({
                    'type': 'security',
                    'field': 'debug_mode',
                    'message': "Режим отладки включен в продакшене",
                    'suggestion': "Установите DEBUG=false в продакшене"
                })
                valid = False
        
        # Проверяем разрешенные хосты
        if 'localhost' in config.security.allowed_hosts and config.environment == 'production':
            self.validation_warnings.append({
                'type': 'security',
                'field': 'allowed_hosts',
                'message': "localhost разрешен в продакшене",
                'suggestion': "Удалите localhost из ALLOWED_HOSTS в продакшене"
            })
        
        return valid
    
    def _is_valid_hostname(self, hostname: str) -> bool:
        """Проверяет валидность имени хоста"""
        if not hostname:
            return False
        
        # IP адрес
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        if re.match(ip_pattern, hostname):
            return True
        
        # Доменное имя
        if hostname in ['localhost', '127.0.0.1']:
            return True
        
        domain_pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        return bool(re.match(domain_pattern, hostname))
    
    def validate_full_config(self, config: AppConfig) -> Tuple[bool, List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Выполняет полную валидацию конфигурации"""
        self.validation_errors.clear()
        self.validation_warnings.clear()
        
        logger.info("Начинаем валидацию конфигурации...")
        
        # Валидируем переменные окружения
        env_valid = self.validate_environment_variables()
        
        # Валидируем подключение к БД
        db_valid = self.validate_database_connection(config.database.dict())
        
        # Валидируем пути к файлам
        files_valid = self.validate_file_paths(config)
        
        # Валидируем числовые диапазоны
        ranges_valid = self.validate_numeric_ranges(config)
        
        # Валидируем настройки безопасности
        security_valid = self.validate_security_settings(config)
        
        # Общий результат
        is_valid = all([env_valid, db_valid, files_valid, ranges_valid, security_valid])
        
        if is_valid:
            logger.info("✅ Конфигурация прошла валидацию успешно")
        else:
            logger.error(f"❌ Найдено {len(self.validation_errors)} ошибок конфигурации")
        
        if self.validation_warnings:
            logger.warning(f"⚠️ Найдено {len(self.validation_warnings)} предупреждений")
        
        return is_valid, self.validation_errors, self.validation_warnings
    
    def print_validation_report(self, errors: List[Dict[str, Any]], warnings: List[Dict[str, Any]]) -> None:
        """Выводит отчет о валидации"""
        print("\n" + "="*60)
        print("📋 ОТЧЕТ О ВАЛИДАЦИИ КОНФИГУРАЦИИ")
        print("="*60)
        
        if errors:
            print(f"\n❌ ОШИБКИ ({len(errors)}):")
            print("-" * 40)
            for i, error in enumerate(errors, 1):
                print(f"{i}. {error['message']}")
                print(f"   Тип: {error['type']}")
                print(f"   Поле: {error['field']}")
                print(f"   💡 Решение: {error['suggestion']}")
                print()
        
        if warnings:
            print(f"\n⚠️ ПРЕДУПРЕЖДЕНИЯ ({len(warnings)}):")
            print("-" * 40)
            for i, warning in enumerate(warnings, 1):
                print(f"{i}. {warning['message']}")
                print(f"   Тип: {warning['type']}")
                print(f"   Поле: {warning['field']}")
                print(f"   💡 Рекомендация: {warning['suggestion']}")
                print()
        
        if not errors and not warnings:
            print("\n✅ Все проверки пройдены успешно!")
        
        print("="*60)


def validate_config_on_startup() -> AppConfig:
    """Валидирует конфигурацию при запуске приложения"""
    try:
        # Загружаем конфигурацию
        config = AppConfig.from_env()
        
        # Создаем валидатор
        validator = ConfigValidator()
        
        # Выполняем валидацию
        is_valid, errors, warnings = validator.validate_full_config(config)
        
        # Выводим отчет
        validator.print_validation_report(errors, warnings)
        
        # Если есть критические ошибки, останавливаем приложение
        if not is_valid:
            raise ConfigValidationError(
                f"Конфигурация содержит {len(errors)} критических ошибок",
                errors
            )
        
        return config
        
    except ValidationError as e:
        logger.error(f"Ошибка валидации Pydantic: {e}")
        raise ConfigValidationError(f"Ошибка структуры конфигурации: {e}")
    
    except Exception as e:
        logger.error(f"Неожиданная ошибка валидации: {e}")
        raise ConfigValidationError(f"Неожиданная ошибка: {e}")


# Глобальный экземпляр валидатора
config_validator = ConfigValidator()
