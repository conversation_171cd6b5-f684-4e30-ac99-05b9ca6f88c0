#!/usr/bin/env python3
"""
Скрипт для проверки и валидации конфигурации приложения
"""
import sys
import os
import argparse
import logging
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.validators import validate_config_on_startup, ConfigValidationError
from config.migration import config_migrator
from config.app_config import AppConfig

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_config(args):
    """Проверяет конфигурацию"""
    try:
        print("🔍 Проверка конфигурации приложения...")
        print("=" * 50)
        
        # Валидируем конфигурацию
        config = validate_config_on_startup()
        
        if args.verbose:
            print("\n📊 ДЕТАЛИ КОНФИГУРАЦИИ:")
            print("-" * 30)
            print(f"Окружение: {config.environment}")
            print(f"Режим отладки: {config.debug}")
            print(f"Версия: {config.version}")
            print(f"База данных: {config.database.host}:{config.database.port}/{config.database.database}")
            print(f"Пул соединений: {config.database.pool_size} (max overflow: {config.database.max_overflow})")
            print(f"Кеширование: {'включено' if config.cache.enabled else 'отключено'}")
            print(f"TTL кеша: {config.cache.default_ttl}s")
            print(f"Уровень логирования: {config.logging.level}")
        
        print("\n✅ Конфигурация корректна и готова к использованию!")
        return True
        
    except ConfigValidationError as e:
        print(f"\n❌ Ошибка конфигурации: {e.message}")
        if args.verbose and e.errors:
            print("\nДетали ошибок:")
            for error in e.errors:
                print(f"- {error['message']}")
        return False
        
    except Exception as e:
        print(f"\n💥 Неожиданная ошибка: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return False


def generate_template(args):
    """Генерирует шаблон .env файла"""
    try:
        print("📝 Генерация шаблона .env файла...")
        
        output_file = args.output or ".env.template"
        config_migrator.generate_env_template(output_file)
        
        print(f"✅ Шаблон создан: {output_file}")
        print("\n💡 Следующие шаги:")
        print(f"1. Скопируйте {output_file} в .env")
        print("2. Отредактируйте значения в .env файле")
        print("3. Запустите проверку: python config/check_config.py --check")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка генерации шаблона: {e}")
        return False


def backup_config(args):
    """Создает резервную копию конфигурации"""
    try:
        print("💾 Создание резервной копии конфигурации...")
        
        backup_file = config_migrator.backup_current_config(args.backup_dir or "config/backups")
        
        print(f"✅ Резервная копия создана: {backup_file}")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка создания резервной копии: {e}")
        return False


def migrate_config(args):
    """Мигрирует конфигурацию из старого формата"""
    try:
        print("🔄 Миграция конфигурации из старого формата...")
        
        # Создаем резервную копию перед миграцией
        if not args.no_backup:
            print("📦 Создание резервной копии перед миграцией...")
            config_migrator.backup_current_config()
        
        # Выполняем миграцию
        legacy_path = args.legacy_path or "config/settings.py"
        new_config = config_migrator.migrate_from_legacy(legacy_path)
        
        # Выводим сводку
        config_migrator.print_migration_summary()
        
        print("\n✅ Миграция завершена успешно!")
        print("\n💡 Рекомендации:")
        print("1. Проверьте новую конфигурацию: python config/check_config.py --check")
        print("2. Обновите переменные окружения в .env файле")
        print("3. Протестируйте приложение")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка миграции: {e}")
        return False


def show_config(args):
    """Показывает текущую конфигурацию"""
    try:
        print("📋 ТЕКУЩАЯ КОНФИГУРАЦИЯ")
        print("=" * 50)
        
        config = AppConfig.from_env()
        
        # Основная информация
        print(f"\n🏷️  ОБЩИЕ НАСТРОЙКИ:")
        print(f"   Окружение: {config.environment}")
        print(f"   Отладка: {config.debug}")
        print(f"   Версия: {config.version}")
        
        # База данных
        print(f"\n🗄️  БАЗА ДАННЫХ:")
        print(f"   Хост: {config.database.host}")
        print(f"   Порт: {config.database.port}")
        print(f"   БД: {config.database.database}")
        print(f"   Пользователь: {config.database.user}")
        print(f"   Пароль: {'установлен' if config.database.password else 'НЕ УСТАНОВЛЕН'}")
        print(f"   Пул: {config.database.pool_size} (overflow: {config.database.max_overflow})")
        
        # UI
        print(f"\n🎨 ИНТЕРФЕЙС:")
        print(f"   Заголовок: {config.ui.title}")
        print(f"   Иконка: {config.ui.icon}")
        print(f"   Макет: {config.ui.layout}")
        print(f"   Боковая панель: {config.ui.initial_sidebar_state}")
        print(f"   Тема: {config.ui.theme}")
        
        # Кеширование
        print(f"\n🚀 КЕШИРОВАНИЕ:")
        print(f"   Включено: {config.cache.enabled}")
        print(f"   TTL по умолчанию: {config.cache.default_ttl}s")
        print(f"   TTL сводки: {config.cache.dashboard_summary_ttl}s")
        print(f"   TTL договоров: {config.cache.contracts_list_ttl}s")
        print(f"   Максимальный размер: {config.cache.max_cache_size}")
        
        # Дашборд
        print(f"\n📊 ДАШБОРД:")
        print(f"   Диапазон дат: {config.dashboard.default_date_range} мес.")
        print(f"   Интервал обновления: {config.dashboard.refresh_interval}s")
        print(f"   Максимум договоров: {config.dashboard.max_contracts_display}")
        print(f"   Реальное время: {config.dashboard.enable_real_time}")
        
        # Логирование
        print(f"\n📝 ЛОГИРОВАНИЕ:")
        print(f"   Уровень: {config.logging.level}")
        print(f"   Файл: {config.logging.file_enabled} ({config.logging.file_path})")
        print(f"   Консоль: {config.logging.console_enabled}")
        
        # Безопасность
        print(f"\n🔒 БЕЗОПАСНОСТЬ:")
        print(f"   Секретный ключ: {'установлен' if config.security.secret_key else 'НЕ УСТАНОВЛЕН'}")
        print(f"   Таймаут сессии: {config.security.session_timeout}s")
        print(f"   Разрешенные хосты: {', '.join(config.security.allowed_hosts)}")
        
        if not args.hide_sensitive:
            print(f"\n⚠️  ЧУВСТВИТЕЛЬНЫЕ ДАННЫЕ (скрыты по умолчанию):")
            print(f"   Используйте --show-sensitive для отображения")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка получения конфигурации: {e}")
        return False


def main():
    """Главная функция"""
    parser = argparse.ArgumentParser(
        description="Утилита для работы с конфигурацией дашборда",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  python config/check_config.py --check                    # Проверить конфигурацию
  python config/check_config.py --generate                 # Создать шаблон .env
  python config/check_config.py --show                     # Показать конфигурацию
  python config/check_config.py --backup                   # Создать резервную копию
  python config/check_config.py --migrate                  # Мигрировать из старого формата
        """
    )
    
    # Основные команды
    parser.add_argument('--check', action='store_true', help='Проверить конфигурацию')
    parser.add_argument('--generate', action='store_true', help='Создать шаблон .env файла')
    parser.add_argument('--show', action='store_true', help='Показать текущую конфигурацию')
    parser.add_argument('--backup', action='store_true', help='Создать резервную копию')
    parser.add_argument('--migrate', action='store_true', help='Мигрировать из старого формата')
    
    # Опции
    parser.add_argument('--verbose', '-v', action='store_true', help='Подробный вывод')
    parser.add_argument('--output', '-o', help='Файл для вывода (для --generate)')
    parser.add_argument('--backup-dir', help='Директория для резервных копий')
    parser.add_argument('--legacy-path', help='Путь к старому файлу настроек')
    parser.add_argument('--no-backup', action='store_true', help='Не создавать резервную копию при миграции')
    parser.add_argument('--hide-sensitive', action='store_true', default=True, help='Скрыть чувствительные данные')
    parser.add_argument('--show-sensitive', action='store_true', help='Показать чувствительные данные')
    
    args = parser.parse_args()
    
    # Если показать чувствительные данные, отключаем скрытие
    if args.show_sensitive:
        args.hide_sensitive = False
    
    # Если не указана команда, показываем справку
    if not any([args.check, args.generate, args.show, args.backup, args.migrate]):
        parser.print_help()
        return 1
    
    success = True
    
    # Выполняем команды
    if args.check:
        success &= check_config(args)
    
    if args.generate:
        success &= generate_template(args)
    
    if args.show:
        success &= show_config(args)
    
    if args.backup:
        success &= backup_config(args)
    
    if args.migrate:
        success &= migrate_config(args)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
