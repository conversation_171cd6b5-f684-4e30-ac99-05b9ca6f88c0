"""
Константы для дашборда освоения договоров
"""

# Статусы договоров
CONTRACT_STATUSES = {
    'active': 'Активный',
    'completed': 'Завершен',
    'suspended': 'Приостановлен',
    'cancelled': 'Отменен',
}

# Типы договоров
CONTRACT_TYPES = {
    'construction': 'Строительство',
    'supply': 'Поставка',
    'services': 'Услуги',
    'maintenance': 'Обслуживание',
}

# Периоды отчетности
REPORTING_PERIODS = {
    'month': 'Месяц',
    'quarter': 'Квартал',
    'year': 'Год',
}

# Метрики для отображения
METRICS = {
    'total_contracts': 'Всего договоров',
    'total_amount': 'Общая сумма',
    'completed_amount': 'Освоено средств',
    'completion_rate': 'Процент освоения',
    'active_contracts': 'Активные договоры',
    'overdue_contracts': 'Просроченные договоры',
}

# SQL запросы
SQL_QUERIES = {
    'contracts_overview': """
        SELECT 
            contract_id,
            contract_number,
            contract_name,
            contract_type,
            status,
            total_amount,
            start_date,
            end_date,
            contractor_name
        FROM contracts 
        WHERE status = 'active'
    """,
    
    'monthly_progress': """
        SELECT 
            DATE_TRUNC('month', report_date) as month,
            SUM(amount_completed) as completed_amount,
            SUM(amount_planned) as planned_amount,
            COUNT(DISTINCT contract_id) as contracts_count
        FROM contract_progress 
        WHERE report_date >= %s AND report_date <= %s
        GROUP BY DATE_TRUNC('month', report_date)
        ORDER BY month
    """,
    
    'contract_details': """
        SELECT 
            c.contract_id,
            c.contract_number,
            c.contract_name,
            c.total_amount,
            COALESCE(SUM(cp.amount_completed), 0) as completed_amount,
            COALESCE(SUM(cp.amount_completed) / NULLIF(c.total_amount, 0) * 100, 0) as completion_percentage
        FROM contracts c
        LEFT JOIN contract_progress cp ON c.contract_id = cp.contract_id
        WHERE c.contract_id = %s
        GROUP BY c.contract_id, c.contract_number, c.contract_name, c.total_amount
    """,
}

# Названия колонок для отображения
COLUMN_NAMES = {
    'contract_id': 'ID договора',
    'contract_number': 'Номер договора',
    'contract_name': 'Название договора',
    'contract_type': 'Тип договора',
    'status': 'Статус',
    'total_amount': 'Общая сумма',
    'completed_amount': 'Освоено',
    'completion_rate': 'Процент освоения',
    'start_date': 'Дата начала',
    'end_date': 'Дата окончания',
    'contractor_name': 'Подрядчик',
    'month': 'Месяц',
    'planned_amount': 'Запланировано',
    'contracts_count': 'Количество договоров',
}
