#!/usr/bin/env python3
"""
Тест новой системы конфигурации
"""
import os
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_basic_config():
    """Тестирует базовую загрузку конфигурации"""
    try:
        print("🧪 Тестирование базовой конфигурации...")
        
        from config.app_config import AppConfig, app_config
        
        print(f"✅ Конфигурация загружена: {app_config.ui.title}")
        print(f"✅ Окружение: {app_config.environment}")
        print(f"✅ База данных: {app_config.database.host}:{app_config.database.port}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка загрузки конфигурации: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_legacy_compatibility():
    """Тестирует обратную совместимость"""
    try:
        print("\n🔄 Тестирование обратной совместимости...")
        
        from config import DATABASE_CONFIG, APP_CONFIG, DASHBOARD_CONFIG, COLOR_SCHEME
        
        print(f"✅ DATABASE_CONFIG: {DATABASE_CONFIG.get('host', 'не найден')}")
        print(f"✅ APP_CONFIG: {APP_CONFIG.get('title', 'не найден')}")
        print(f"✅ DASHBOARD_CONFIG: {DASHBOARD_CONFIG.get('default_date_range', 'не найден')}")
        print(f"✅ COLOR_SCHEME: {COLOR_SCHEME.get('primary', 'не найден')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка обратной совместимости: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_component_configs():
    """Тестирует конфигурации компонентов"""
    try:
        print("\n🎨 Тестирование конфигураций компонентов...")
        
        from config.component_configs import components_config
        
        print(f"✅ Графики: высота {components_config.charts.height}px")
        print(f"✅ Таблицы: размер страницы {components_config.tables.default_page_size}")
        print(f"✅ Формы: колонки {components_config.forms.default_columns}")
        print(f"✅ Экспорт: форматы {components_config.export.formats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка конфигураций компонентов: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_validation():
    """Тестирует валидацию конфигурации"""
    try:
        print("\n🔍 Тестирование валидации...")
        
        from config.validators import ConfigValidator
        from config.app_config import app_config
        
        validator = ConfigValidator()
        
        # Тестируем валидацию переменных окружения
        env_valid = validator.validate_environment_variables()
        print(f"✅ Переменные окружения: {'валидны' if env_valid else 'есть проблемы'}")
        
        # Тестируем валидацию числовых диапазонов
        ranges_valid = validator.validate_numeric_ranges(app_config)
        print(f"✅ Числовые диапазоны: {'валидны' if ranges_valid else 'есть предупреждения'}")
        
        # Показываем предупреждения если есть
        if validator.validation_warnings:
            print(f"⚠️ Предупреждений: {len(validator.validation_warnings)}")
            for warning in validator.validation_warnings[:3]:  # Показываем первые 3
                print(f"   - {warning['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка валидации: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_env_loading():
    """Тестирует загрузку из переменных окружения"""
    try:
        print("\n🌍 Тестирование загрузки из переменных окружения...")
        
        # Устанавливаем тестовые переменные
        os.environ['APP_TITLE'] = 'Тестовый дашборд'
        os.environ['DB_HOST'] = 'test-host'
        os.environ['CACHE_ENABLED'] = 'false'
        
        from config.app_config import AppConfig
        
        # Создаем новую конфигурацию
        test_config = AppConfig.from_env()
        
        print(f"✅ Заголовок из env: {test_config.ui.title}")
        print(f"✅ Хост БД из env: {test_config.database.host}")
        print(f"✅ Кеш из env: {test_config.cache.enabled}")
        
        # Очищаем тестовые переменные
        del os.environ['APP_TITLE']
        del os.environ['DB_HOST']
        del os.environ['CACHE_ENABLED']
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка загрузки из env: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Главная функция тестирования"""
    print("🚀 ТЕСТИРОВАНИЕ НОВОЙ СИСТЕМЫ КОНФИГУРАЦИИ")
    print("=" * 60)
    
    tests = [
        test_basic_config,
        test_legacy_compatibility,
        test_component_configs,
        test_validation,
        test_env_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"💥 Неожиданная ошибка в тесте {test.__name__}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 РЕЗУЛЬТАТЫ: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 Все тесты пройдены успешно!")
        return True
    else:
        print(f"⚠️ Не пройдено {total - passed} тестов")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
