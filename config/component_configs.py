"""
Специализированные конфигурации для компонентов системы
"""
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class ChartConfig(BaseModel):
    """Конфигурация графиков"""
    default_type: str = Field(default="bar", pattern="^(bar|line|pie|area|scatter)$", description="Тип графика по умолчанию")
    width: Optional[int] = Field(default=None, ge=200, le=2000, description="Ширина графика")
    height: int = Field(default=400, ge=200, le=1000, description="Высота графика")
    show_legend: bool = Field(default=True, description="Показывать легенду")
    show_grid: bool = Field(default=True, description="Показывать сетку")
    interactive: bool = Field(default=True, description="Интерактивные графики")
    animation_duration: int = Field(default=500, ge=0, le=3000, description="Длительность анимации в мс")
    color_palette: List[str] = Field(
        default=[
            "#3b82f6", "#2563eb", "#1e40af", "#60a5fa", 
            "#93c5fd", "#1d4ed8", "#2dd4bf", "#06b6d4"
        ],
        description="Цветовая палитра"
    )
    responsive: bool = Field(default=True, description="Адаптивность")
    export_enabled: bool = Field(default=True, description="Возможность экспорта")
    
    @field_validator('color_palette')
    @classmethod
    def validate_colors(cls, v):
        if len(v) < 3:
            raise ValueError("Цветовая палитра должна содержать минимум 3 цвета")
        for color in v:
            if not color.startswith('#') or len(color) != 7:
                raise ValueError(f"Неверный формат цвета: {color}")
        return v


class TableConfig(BaseModel):
    """Конфигурация таблиц"""
    default_page_size: int = Field(default=20, ge=5, le=200, description="Размер страницы по умолчанию")
    sortable: bool = Field(default=True, description="Возможность сортировки")
    filterable: bool = Field(default=True, description="Возможность фильтрации")
    searchable: bool = Field(default=True, description="Возможность поиска")
    show_index: bool = Field(default=False, description="Показывать индекс")
    show_pagination: bool = Field(default=True, description="Показывать пагинацию")
    striped_rows: bool = Field(default=True, description="Чередующиеся строки")
    hover_effect: bool = Field(default=True, description="Эффект при наведении")
    compact_mode: bool = Field(default=False, description="Компактный режим")
    export_formats: List[str] = Field(
        default=["csv", "xlsx"],
        description="Доступные форматы экспорта"
    )
    max_column_width: int = Field(default=300, ge=50, le=1000, description="Максимальная ширина колонки")
    
    @field_validator('export_formats')
    @classmethod
    def validate_export_formats(cls, v):
        allowed = {"csv", "xlsx", "json", "pdf"}
        invalid = set(v) - allowed
        if invalid:
            raise ValueError(f"Недопустимые форматы: {invalid}")
        return v


class FormConfig(BaseModel):
    """Конфигурация форм"""
    default_columns: int = Field(default=2, ge=1, le=6, description="Количество колонок по умолчанию")
    show_labels: bool = Field(default=True, description="Показывать метки полей")
    show_help_text: bool = Field(default=True, description="Показывать справочный текст")
    show_validation_errors: bool = Field(default=True, description="Показывать ошибки валидации")
    auto_save: bool = Field(default=False, description="Автосохранение")
    auto_save_interval: int = Field(default=30, ge=5, le=300, description="Интервал автосохранения в секундах")
    required_field_marker: str = Field(default="*", description="Маркер обязательных полей")
    date_format: str = Field(default="%d.%m.%Y", description="Формат даты")
    datetime_format: str = Field(default="%d.%m.%Y %H:%M", description="Формат даты и времени")
    number_format: str = Field(default="{:,.2f}", description="Формат чисел")


class FilterConfig(BaseModel):
    """Конфигурация фильтров"""
    show_clear_button: bool = Field(default=True, description="Показывать кнопку очистки")
    show_apply_button: bool = Field(default=True, description="Показывать кнопку применения")
    auto_apply: bool = Field(default=False, description="Автоматическое применение фильтров")
    auto_apply_delay: int = Field(default=500, ge=100, le=2000, description="Задержка автоприменения в мс")
    remember_filters: bool = Field(default=True, description="Запоминать фильтры")
    max_filter_options: int = Field(default=1000, ge=10, le=10000, description="Максимум опций в фильтре")
    search_threshold: int = Field(default=10, ge=5, le=100, description="Порог для включения поиска в фильтре")
    case_sensitive_search: bool = Field(default=False, description="Регистрозависимый поиск")


class NotificationConfig(BaseModel):
    """Конфигурация уведомлений"""
    show_success: bool = Field(default=True, description="Показывать уведомления об успехе")
    show_warnings: bool = Field(default=True, description="Показывать предупреждения")
    show_errors: bool = Field(default=True, description="Показывать ошибки")
    show_info: bool = Field(default=True, description="Показывать информационные сообщения")
    auto_dismiss_success: bool = Field(default=True, description="Автоскрытие успешных уведомлений")
    auto_dismiss_delay: int = Field(default=3000, ge=1000, le=10000, description="Задержка автоскрытия в мс")
    position: str = Field(
        default="top-right",
        pattern="^(top-left|top-right|bottom-left|bottom-right|top-center|bottom-center)$",
        description="Позиция уведомлений"
    )
    max_notifications: int = Field(default=5, ge=1, le=20, description="Максимум одновременных уведомлений")


class ExportConfig(BaseModel):
    """Конфигурация экспорта данных"""
    enabled: bool = Field(default=True, description="Включить экспорт")
    formats: List[str] = Field(
        default=["csv", "xlsx", "json"],
        description="Доступные форматы экспорта"
    )
    max_rows: int = Field(default=10000, ge=100, le=1000000, description="Максимум строк для экспорта")
    include_metadata: bool = Field(default=True, description="Включать метаданные")
    compress_large_files: bool = Field(default=True, description="Сжимать большие файлы")
    compression_threshold: int = Field(default=1048576, ge=102400, description="Порог сжатия в байтах")
    filename_template: str = Field(
        default="export_{timestamp}_{type}.{ext}",
        description="Шаблон имени файла"
    )
    
    @field_validator('formats')
    @classmethod
    def validate_formats(cls, v):
        allowed = {"csv", "xlsx", "json", "pdf", "xml"}
        invalid = set(v) - allowed
        if invalid:
            raise ValueError(f"Недопустимые форматы: {invalid}")
        return v


class ValidationConfig(BaseModel):
    """Конфигурация валидации"""
    strict_mode: bool = Field(default=False, description="Строгий режим валидации")
    show_field_errors: bool = Field(default=True, description="Показывать ошибки полей")
    show_form_errors: bool = Field(default=True, description="Показывать ошибки формы")
    validate_on_change: bool = Field(default=True, description="Валидация при изменении")
    validate_on_blur: bool = Field(default=True, description="Валидация при потере фокуса")
    debounce_delay: int = Field(default=300, ge=100, le=1000, description="Задержка валидации в мс")
    max_error_messages: int = Field(default=5, ge=1, le=20, description="Максимум сообщений об ошибках")


class AccessibilityConfig(BaseModel):
    """Конфигурация доступности"""
    high_contrast: bool = Field(default=False, description="Высокий контраст")
    large_text: bool = Field(default=False, description="Крупный текст")
    keyboard_navigation: bool = Field(default=True, description="Навигация с клавиатуры")
    screen_reader_support: bool = Field(default=True, description="Поддержка скринридеров")
    focus_indicators: bool = Field(default=True, description="Индикаторы фокуса")
    skip_links: bool = Field(default=True, description="Ссылки для пропуска")
    aria_labels: bool = Field(default=True, description="ARIA метки")


class ComponentsConfig(BaseModel):
    """Общая конфигурация компонентов"""
    charts: ChartConfig = Field(default_factory=ChartConfig)
    tables: TableConfig = Field(default_factory=TableConfig)
    forms: FormConfig = Field(default_factory=FormConfig)
    filters: FilterConfig = Field(default_factory=FilterConfig)
    notifications: NotificationConfig = Field(default_factory=NotificationConfig)
    export: ExportConfig = Field(default_factory=ExportConfig)
    validation: ValidationConfig = Field(default_factory=ValidationConfig)
    accessibility: AccessibilityConfig = Field(default_factory=AccessibilityConfig)
    
    class Config:
        validate_assignment = True
        extra = "forbid"
    
    def get_chart_config(self) -> Dict[str, Any]:
        """Возвращает конфигурацию графиков"""
        return self.charts.dict()
    
    def get_table_config(self) -> Dict[str, Any]:
        """Возвращает конфигурацию таблиц"""
        return self.tables.dict()
    
    def get_form_config(self) -> Dict[str, Any]:
        """Возвращает конфигурацию форм"""
        return self.forms.dict()
    
    def update_chart_config(self, **kwargs) -> None:
        """Обновляет конфигурацию графиков"""
        for key, value in kwargs.items():
            if hasattr(self.charts, key):
                setattr(self.charts, key, value)
    
    def update_table_config(self, **kwargs) -> None:
        """Обновляет конфигурацию таблиц"""
        for key, value in kwargs.items():
            if hasattr(self.tables, key):
                setattr(self.tables, key, value)
    
    def to_legacy_format(self) -> Dict[str, Any]:
        """Преобразует в старый формат для обратной совместимости"""
        return {
            'CHART_CONFIG': self.charts.dict(),
            'TABLE_CONFIG': self.tables.dict(),
            'FORM_CONFIG': self.forms.dict(),
            'FILTER_CONFIG': self.filters.dict(),
            'EXPORT_CONFIG': self.export.dict()
        }


# Глобальный экземпляр конфигурации компонентов
components_config = ComponentsConfig()
