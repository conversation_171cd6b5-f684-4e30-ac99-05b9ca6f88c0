"""
Централизованная система конфигурации приложения
"""

# Основная конфигурация с fallback поддержкой
try:
    from config.app_config import (
        AppConfig,
        DatabaseConfig,
        UIConfig,
        CacheConfig,
        DashboardConfig,
        ColorSchemeConfig,
        LoggingConfig,
        SecurityConfig,
        PerformanceConfig,
        app_config
    )
    print("✅ Загружена основная конфигурация с pydantic")

except ImportError as e:
    print(f"⚠️ pydantic недоступна ({e}), используем fallback конфигурацию")
    from config.fallback_config import fallback_config as app_config

    # Создаем заглушки для классов
    AppConfig = type('AppConfig', (), {})
    DatabaseConfig = type('DatabaseConfig', (), {})
    UIConfig = type('UIConfig', (), {})
    CacheConfig = type('CacheConfig', (), {})
    DashboardConfig = type('DashboardConfig', (), {})
    ColorSchemeConfig = type('ColorSchemeConfig', (), {})
    LoggingConfig = type('LoggingConfig', (), {})
    SecurityConfig = type('SecurityConfig', (), {})
    PerformanceConfig = type('PerformanceConfig', (), {})

# Конфигурации компонентов с fallback
try:
    from config.component_configs import (
        ComponentsConfig,
        ChartConfig,
        TableConfig,
        FormConfig,
        FilterConfig,
        NotificationConfig,
        ExportConfig,
        ValidationConfig,
        AccessibilityConfig,
        components_config
    )
except ImportError:
    # Создаем заглушки для компонентов
    ComponentsConfig = type('ComponentsConfig', (), {})
    ChartConfig = type('ChartConfig', (), {})
    TableConfig = type('TableConfig', (), {})
    FormConfig = type('FormConfig', (), {})
    FilterConfig = type('FilterConfig', (), {})
    NotificationConfig = type('NotificationConfig', (), {})
    ExportConfig = type('ExportConfig', (), {})
    ValidationConfig = type('ValidationConfig', (), {})
    AccessibilityConfig = type('AccessibilityConfig', (), {})
    components_config = type('components_config', (), {})()

# Валидация конфигурации с fallback
try:
    from config.validators import (
        ConfigValidator,
        ConfigValidationError,
        validate_config_on_startup,
        config_validator
    )
except ImportError:
    # Создаем заглушки для валидации
    ConfigValidator = type('ConfigValidator', (), {})
    ConfigValidationError = Exception
    validate_config_on_startup = lambda: None
    config_validator = type('config_validator', (), {})()

# Обратная совместимость - импорт старых настроек
try:
    # Получаем конфигурацию в старом формате
    if hasattr(app_config, 'get_legacy_config'):
        _legacy_config = app_config.get_legacy_config()
        DATABASE_CONFIG = _legacy_config['DATABASE_CONFIG']
        APP_CONFIG = _legacy_config['APP_CONFIG']
        DASHBOARD_CONFIG = _legacy_config['DASHBOARD_CONFIG']
        COLOR_SCHEME = _legacy_config['COLOR_SCHEME']
    else:
        # Используем fallback конфигурацию
        DATABASE_CONFIG = app_config.database
        APP_CONFIG = app_config.app
        DASHBOARD_CONFIG = {
            'refresh_interval': app_config.ui['refresh_interval'],
            'max_records_per_page': 50,
            'default_theme': app_config.ui['theme'],
            'enable_animations': app_config.ui['animation_enabled'],
            'show_debug_info': app_config.app['debug']
        }
        COLOR_SCHEME = app_config.colors

    # Дополнительные настройки из старых файлов
    try:
        from config.constants import (
            CONTRACT_STATUSES,
            CONTRACT_TYPES,
            REPORTING_PERIODS,
            METRICS,
            SQL_QUERIES,
            COLUMN_NAMES
        )
    except ImportError:
        # Fallback константы
        CONTRACT_STATUSES = ['active', 'completed', 'overdue', 'suspended']
        CONTRACT_TYPES = ['Поставка', 'Услуги', 'Работы', 'Аренда']
        REPORTING_PERIODS = ['month', 'quarter', 'year']
        METRICS = ['total_contracts', 'active_contracts', 'total_amount']
        SQL_QUERIES = {}
        COLUMN_NAMES = {}

    # Форматы данных
    DATA_FORMATS = {
        'currency': '{:,.2f} ₽',
        'percentage': '{:.1%}',
        'date': '%d.%m.%Y',
        'datetime': '%d.%m.%Y %H:%M',
    }

except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Не удалось загрузить конфигурацию для обратной совместимости: {e}")

    # Fallback значения
    DATABASE_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'name': 'contracts_db',
        'user': 'postgres',
        'password': '',
        'url': 'postgresql://postgres:@localhost:5432/contracts_db'
    }
    APP_CONFIG = {
        'title': '📊 Дашборд освоения договоров',
        'version': '2.0.0',
        'debug': False
    }
    DASHBOARD_CONFIG = {
        'refresh_interval': 300,
        'max_records_per_page': 50,
        'default_theme': 'light'
    }
    COLOR_SCHEME = {
        'primary': '#3b82f6',
        'secondary': '#2563eb'
    }
    DATA_FORMATS = {
        'currency': '{:,.2f} ₽',
        'percentage': '{:.1%}',
        'date': '%d.%m.%Y',
        'datetime': '%d.%m.%Y %H:%M',
    }

    # Fallback константы
    CONTRACT_STATUSES = ['active', 'completed', 'overdue', 'suspended']
    CONTRACT_TYPES = ['Поставка', 'Услуги', 'Работы', 'Аренда']
    REPORTING_PERIODS = ['month', 'quarter', 'year']
    METRICS = ['total_contracts', 'active_contracts', 'total_amount']
    SQL_QUERIES = {}
    COLUMN_NAMES = {}

# Экспорт всех элементов
__all__ = [
    # Основные классы конфигурации
    'AppConfig',
    'DatabaseConfig',
    'UIConfig',
    'CacheConfig',
    'DashboardConfig',
    'ColorSchemeConfig',
    'LoggingConfig',
    'SecurityConfig',
    'PerformanceConfig',

    # Конфигурации компонентов
    'ComponentsConfig',
    'ChartConfig',
    'TableConfig',
    'FormConfig',
    'FilterConfig',
    'NotificationConfig',
    'ExportConfig',
    'ValidationConfig',
    'AccessibilityConfig',

    # Валидация
    'ConfigValidator',
    'ConfigValidationError',
    'validate_config_on_startup',
    'config_validator',

    # Глобальные экземпляры
    'app_config',
    'components_config',

    # Обратная совместимость
    'DATABASE_CONFIG',
    'APP_CONFIG',
    'DASHBOARD_CONFIG',
    'COLOR_SCHEME',
    'DATA_FORMATS',

    # Константы (если доступны)
    'CONTRACT_STATUSES',
    'CONTRACT_TYPES',
    'REPORTING_PERIODS',
    'METRICS',
    'SQL_QUERIES',
    'COLUMN_NAMES'
]
