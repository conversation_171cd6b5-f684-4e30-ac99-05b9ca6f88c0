"""
Настройки приложения для дашборда освоения договоров
УСТАРЕЛО: Используйте config.app_config для новых проектов
"""
import os
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

# Загружаем переменные окружения
load_dotenv()

# Пытаемся использовать новую систему конфигурации
try:
    from config.app_config import app_config

    # Получаем конфигурацию в старом формате для обратной совместимости
    _legacy_config = app_config.get_legacy_config()

    DATABASE_CONFIG = _legacy_config['DATABASE_CONFIG']
    APP_CONFIG = _legacy_config['APP_CONFIG']
    DASHBOARD_CONFIG = _legacy_config['DASHBOARD_CONFIG']
    COLOR_SCHEME = _legacy_config['COLOR_SCHEME']

    logger.info("✅ Используется новая система конфигурации")

except ImportError as e:
    logger.warning(f"⚠️ Новая система конфигурации недоступна, используем старую: {e}")

    # Fallback к старой системе конфигурации
    DATABASE_CONFIG = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 5432)),
        'database': os.getenv('DB_NAME', 'contracts_db'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', ''),
    }

    APP_CONFIG = {
        'title': os.getenv('APP_TITLE', 'Дашборд освоения договоров'),
        'icon': os.getenv('APP_ICON', '📊'),
        'layout': 'wide',
        'initial_sidebar_state': 'expanded',
    }

    DASHBOARD_CONFIG = {
        'default_date_range': int(os.getenv('DEFAULT_DATE_RANGE', 12)),
        'refresh_interval': int(os.getenv('REFRESH_INTERVAL', 300)),
        'page_size': 50,
        'chart_height': 400,
    }

    COLOR_SCHEME = {
        'primary': '#3b82f6',      # Основной синий
        'secondary': '#2563eb',    # Средний синий
        'success': '#1e40af',      # Темно-синий для успеха
        'warning': '#60a5fa',      # Светло-синий для предупреждений
        'danger': '#93c5fd',       # Очень светло-синий для опасности
        'info': '#1d4ed8',         # Самый темный синий для информации
        'background': '#f8fafc',   # Светлый фон
        'text_primary': '#1f2937', # Темный текст
        'text_secondary': '#6b7280', # Серый текст
        'border': '#3b82f6',       # Синяя граница
        'card_bg': '#ffffff',      # Белый фон карточек
    }

except Exception as e:
    logger.error(f"❌ Ошибка загрузки конфигурации: {e}")
    raise

# Форматы данных (остаются неизменными)
DATA_FORMATS = {
    'currency': '{:,.2f} ₽',
    'percentage': '{:.1%}',
    'date': '%d.%m.%Y',
    'datetime': '%d.%m.%Y %H:%M',
}
