"""
Централизованная система конфигурации приложения
"""
import os
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum

logger = logging.getLogger(__name__)


class LogLevel(str, Enum):
    """Уровни логирования"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabaseConfig(BaseModel):
    """Конфигурация базы данных"""
    host: str = Field(default="localhost", description="Хост базы данных")
    port: int = Field(default=5432, ge=1, le=65535, description="Порт базы данных")
    database: str = Field(default="contracts_db", min_length=1, description="Имя базы данных")
    user: str = Field(default="postgres", min_length=1, description="Пользователь БД")
    password: str = Field(default="", description="Пароль БД")
    pool_size: int = Field(default=10, ge=1, le=50, description="Размер пула соединений")
    max_overflow: int = Field(default=20, ge=0, le=100, description="Максимальное переполнение пула")
    pool_timeout: int = Field(default=30, ge=1, le=300, description="Таймаут пула в секундах")
    pool_recycle: int = Field(default=3600, ge=300, description="Время переиспользования соединения")
    connect_timeout: int = Field(default=10, ge=1, le=60, description="Таймаут подключения")
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        if not v:
            logger.warning("Пароль базы данных не установлен")
        return v
    
    def get_connection_string(self) -> str:
        """Возвращает строку подключения к БД"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"


class UIConfig(BaseModel):
    """Конфигурация пользовательского интерфейса"""
    title: str = Field(default="Дашборд освоения договоров", description="Заголовок приложения")
    icon: str = Field(default="📊", description="Иконка приложения")
    layout: str = Field(default="wide", pattern="^(centered|wide)$", description="Макет страницы")
    initial_sidebar_state: str = Field(
        default="expanded",
        pattern="^(auto|expanded|collapsed)$",
        description="Начальное состояние боковой панели"
    )
    theme: str = Field(default="light", pattern="^(light|dark|auto)$", description="Тема интерфейса")
    page_size: int = Field(default=50, ge=10, le=1000, description="Размер страницы для таблиц")
    chart_height: int = Field(default=400, ge=200, le=1000, description="Высота графиков")
    animation_enabled: bool = Field(default=True, description="Включить анимации")
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if len(v) > 100:
            raise ValueError("Заголовок не может быть длиннее 100 символов")
        return v


class CacheConfig(BaseModel):
    """Конфигурация кеширования"""
    enabled: bool = Field(default=True, description="Включить кеширование")
    default_ttl: int = Field(default=300, ge=60, le=86400, description="TTL по умолчанию в секундах")
    dashboard_summary_ttl: int = Field(default=300, ge=60, le=3600, description="TTL для сводки дашборда")
    contracts_list_ttl: int = Field(default=600, ge=60, le=3600, description="TTL для списка договоров")
    monthly_progress_ttl: int = Field(default=1800, ge=300, le=7200, description="TTL для месячного прогресса")
    static_data_ttl: int = Field(default=3600, ge=600, le=86400, description="TTL для статических данных")
    max_cache_size: int = Field(default=1000, ge=100, le=10000, description="Максимальный размер кеша")
    cleanup_interval: int = Field(default=3600, ge=300, le=86400, description="Интервал очистки кеша")


class DashboardConfig(BaseModel):
    """Конфигурация дашборда"""
    default_date_range: int = Field(default=12, ge=1, le=60, description="Диапазон дат по умолчанию в месяцах")
    refresh_interval: int = Field(default=300, ge=30, le=3600, description="Интервал обновления в секундах")
    max_contracts_display: int = Field(default=1000, ge=10, le=10000, description="Максимум договоров для отображения")
    enable_real_time: bool = Field(default=False, description="Включить обновления в реальном времени")
    show_debug_info: bool = Field(default=False, description="Показывать отладочную информацию")
    export_formats: List[str] = Field(
        default=["csv", "xlsx", "json"], 
        description="Доступные форматы экспорта"
    )
    
    @field_validator('export_formats')
    @classmethod
    def validate_export_formats(cls, v):
        allowed_formats = {"csv", "xlsx", "json", "pdf"}
        invalid_formats = set(v) - allowed_formats
        if invalid_formats:
            raise ValueError(f"Недопустимые форматы экспорта: {invalid_formats}")
        return v


class ColorSchemeConfig(BaseModel):
    """Конфигурация цветовой схемы"""
    primary: str = Field(default="#3b82f6", pattern="^#[0-9a-fA-F]{6}$", description="Основной цвет")
    secondary: str = Field(default="#2563eb", pattern="^#[0-9a-fA-F]{6}$", description="Вторичный цвет")
    success: str = Field(default="#1e40af", pattern="^#[0-9a-fA-F]{6}$", description="Цвет успеха")
    warning: str = Field(default="#60a5fa", pattern="^#[0-9a-fA-F]{6}$", description="Цвет предупреждения")
    danger: str = Field(default="#93c5fd", pattern="^#[0-9a-fA-F]{6}$", description="Цвет опасности")
    info: str = Field(default="#1d4ed8", pattern="^#[0-9a-fA-F]{6}$", description="Информационный цвет")
    background: str = Field(default="#f8fafc", pattern="^#[0-9a-fA-F]{6}$", description="Цвет фона")
    text_primary: str = Field(default="#1f2937", pattern="^#[0-9a-fA-F]{6}$", description="Основной цвет текста")
    text_secondary: str = Field(default="#6b7280", pattern="^#[0-9a-fA-F]{6}$", description="Вторичный цвет текста")
    border: str = Field(default="#3b82f6", pattern="^#[0-9a-fA-F]{6}$", description="Цвет границ")
    card_bg: str = Field(default="#ffffff", pattern="^#[0-9a-fA-F]{6}$", description="Фон карточек")


class LoggingConfig(BaseModel):
    """Конфигурация логирования"""
    level: LogLevel = Field(default=LogLevel.INFO, description="Уровень логирования")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Формат логов"
    )
    file_enabled: bool = Field(default=True, description="Включить запись в файл")
    file_path: str = Field(default="logs/app.log", description="Путь к файлу логов")
    file_max_size: int = Field(default=10485760, ge=1048576, description="Максимальный размер файла логов в байтах")
    file_backup_count: int = Field(default=5, ge=1, le=20, description="Количество резервных файлов логов")
    console_enabled: bool = Field(default=True, description="Включить вывод в консоль")


class SecurityConfig(BaseModel):
    """Конфигурация безопасности"""
    secret_key: Optional[str] = Field(default=None, description="Секретный ключ приложения")
    session_timeout: int = Field(default=3600, ge=300, le=86400, description="Таймаут сессии в секундах")
    max_login_attempts: int = Field(default=5, ge=1, le=20, description="Максимум попыток входа")
    password_min_length: int = Field(default=8, ge=6, le=50, description="Минимальная длина пароля")
    enable_csrf_protection: bool = Field(default=True, description="Включить CSRF защиту")
    allowed_hosts: List[str] = Field(default=["localhost", "127.0.0.1"], description="Разрешенные хосты")
    
    @field_validator('secret_key')
    @classmethod
    def validate_secret_key(cls, v):
        if v and len(v) < 32:
            raise ValueError("Секретный ключ должен быть не менее 32 символов")
        return v


class PerformanceConfig(BaseModel):
    """Конфигурация производительности"""
    max_workers: int = Field(default=4, ge=1, le=20, description="Максимум рабочих процессов")
    request_timeout: int = Field(default=30, ge=5, le=300, description="Таймаут запроса в секундах")
    slow_query_threshold: float = Field(default=1.0, ge=0.1, le=10.0, description="Порог медленного запроса в секундах")
    memory_limit_mb: int = Field(default=512, ge=128, le=4096, description="Лимит памяти в МБ")
    enable_profiling: bool = Field(default=False, description="Включить профилирование")
    compression_enabled: bool = Field(default=True, description="Включить сжатие ответов")


class AppConfig(BaseModel):
    """Главная конфигурация приложения"""
    # Подконфигурации
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    ui: UIConfig = Field(default_factory=UIConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    dashboard: DashboardConfig = Field(default_factory=DashboardConfig)
    colors: ColorSchemeConfig = Field(default_factory=ColorSchemeConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    
    # Общие настройки
    debug: bool = Field(default=False, description="Режим отладки")
    environment: str = Field(default="development", pattern="^(development|staging|production)$", description="Окружение")
    version: str = Field(default="1.0.0", description="Версия приложения")
    
    class Config:
        env_prefix = ""
        case_sensitive = False
        validate_assignment = True
        extra = "forbid"
    
    @model_validator(mode='before')
    @classmethod
    def validate_config(cls, values):
        """Валидация всей конфигурации"""
        # Проверяем совместимость настроек
        if values.get('debug') and values.get('environment') == 'production':
            logger.warning("Режим отладки включен в продакшене")

        return values
    
    @classmethod
    def from_env(cls, env_file: Optional[str] = None) -> 'AppConfig':
        """Создает конфигурацию из переменных окружения"""
        # Загружаем переменные окружения
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()
        
        # Создаем конфигурацию из переменных окружения
        config_data = {}
        
        # Настройки базы данных
        config_data['database'] = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'contracts_db'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', ''),
            'pool_size': int(os.getenv('DB_POOL_SIZE', 10)),
            'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', 20)),
            'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', 30)),
            'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', 3600)),
            'connect_timeout': int(os.getenv('DB_CONNECT_TIMEOUT', 10)),
        }
        
        # Настройки UI
        config_data['ui'] = {
            'title': os.getenv('APP_TITLE', 'Дашборд освоения договоров'),
            'icon': os.getenv('APP_ICON', '📊'),
            'layout': os.getenv('APP_LAYOUT', 'wide'),
            'initial_sidebar_state': os.getenv('APP_SIDEBAR_STATE', 'expanded'),
            'theme': os.getenv('APP_THEME', 'light'),
            'page_size': int(os.getenv('APP_PAGE_SIZE', 50)),
            'chart_height': int(os.getenv('APP_CHART_HEIGHT', 400)),
            'animation_enabled': os.getenv('APP_ANIMATIONS', 'true').lower() == 'true',
        }
        
        # Настройки кеширования
        config_data['cache'] = {
            'enabled': os.getenv('CACHE_ENABLED', 'true').lower() == 'true',
            'default_ttl': int(os.getenv('CACHE_DEFAULT_TTL', 300)),
            'dashboard_summary_ttl': int(os.getenv('CACHE_DASHBOARD_TTL', 300)),
            'contracts_list_ttl': int(os.getenv('CACHE_CONTRACTS_TTL', 600)),
            'monthly_progress_ttl': int(os.getenv('CACHE_PROGRESS_TTL', 1800)),
            'static_data_ttl': int(os.getenv('CACHE_STATIC_TTL', 3600)),
            'max_cache_size': int(os.getenv('CACHE_MAX_SIZE', 1000)),
            'cleanup_interval': int(os.getenv('CACHE_CLEANUP_INTERVAL', 3600)),
        }
        
        # Настройки дашборда
        config_data['dashboard'] = {
            'default_date_range': int(os.getenv('DEFAULT_DATE_RANGE', 12)),
            'refresh_interval': int(os.getenv('REFRESH_INTERVAL', 300)),
            'max_contracts_display': int(os.getenv('MAX_CONTRACTS_DISPLAY', 1000)),
            'enable_real_time': os.getenv('ENABLE_REAL_TIME', 'false').lower() == 'true',
            'show_debug_info': os.getenv('SHOW_DEBUG_INFO', 'false').lower() == 'true',
            'export_formats': os.getenv('EXPORT_FORMATS', 'csv,xlsx,json').split(','),
        }
        
        # Настройки логирования
        config_data['logging'] = {
            'level': os.getenv('LOG_LEVEL', 'INFO'),
            'file_enabled': os.getenv('LOG_FILE_ENABLED', 'true').lower() == 'true',
            'file_path': os.getenv('LOG_FILE_PATH', 'logs/app.log'),
            'console_enabled': os.getenv('LOG_CONSOLE_ENABLED', 'true').lower() == 'true',
        }
        
        # Общие настройки
        config_data['debug'] = os.getenv('DEBUG', 'false').lower() == 'true'
        config_data['environment'] = os.getenv('ENVIRONMENT', 'development')
        config_data['version'] = os.getenv('APP_VERSION', '1.0.0')
        
        return cls(**config_data)
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразует конфигурацию в словарь"""
        return self.dict()
    
    def get_legacy_config(self) -> Dict[str, Any]:
        """Возвращает конфигурацию в старом формате для обратной совместимости"""
        return {
            'DATABASE_CONFIG': {
                'host': self.database.host,
                'port': self.database.port,
                'database': self.database.database,
                'user': self.database.user,
                'password': self.database.password,
            },
            'APP_CONFIG': {
                'title': self.ui.title,
                'icon': self.ui.icon,
                'layout': self.ui.layout,
                'initial_sidebar_state': self.ui.initial_sidebar_state,
            },
            'DASHBOARD_CONFIG': {
                'default_date_range': self.dashboard.default_date_range,
                'refresh_interval': self.dashboard.refresh_interval,
                'page_size': self.ui.page_size,
                'chart_height': self.ui.chart_height,
            },
            'COLOR_SCHEME': {
                'primary': self.colors.primary,
                'secondary': self.colors.secondary,
                'success': self.colors.success,
                'warning': self.colors.warning,
                'danger': self.colors.danger,
                'info': self.colors.info,
                'background': self.colors.background,
                'text_primary': self.colors.text_primary,
                'text_secondary': self.colors.text_secondary,
                'border': self.colors.border,
                'card_bg': self.colors.card_bg,
            }
        }


# Глобальный экземпляр конфигурации
app_config = AppConfig.from_env()
