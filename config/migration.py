"""
Утилиты для миграции конфигурации
"""
import os
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime

from config.app_config import AppConfig
from config.component_configs import ComponentsConfig

logger = logging.getLogger(__name__)


class ConfigMigrator:
    """Класс для миграции конфигурации между версиями"""
    
    def __init__(self):
        self.migration_log = []
    
    def migrate_from_legacy(self, legacy_settings_path: str = "config/settings.py") -> AppConfig:
        """Мигрирует конфигурацию из старого формата"""
        logger.info("Начинаем миграцию конфигурации из старого формата...")
        
        try:
            # Читаем старый файл настроек
            legacy_config = self._read_legacy_settings(legacy_settings_path)
            
            # Создаем новую конфигурацию
            new_config_data = self._convert_legacy_to_new(legacy_config)
            
            # Создаем объект конфигурации
            new_config = AppConfig(**new_config_data)
            
            logger.info("✅ Миграция конфигурации завершена успешно")
            return new_config
            
        except Exception as e:
            logger.error(f"❌ Ошибка миграции конфигурации: {e}")
            raise
    
    def _read_legacy_settings(self, settings_path: str) -> Dict[str, Any]:
        """Читает старый файл настроек"""
        legacy_config = {}
        
        try:
            # Импортируем старые настройки
            import importlib.util
            spec = importlib.util.spec_from_file_location("legacy_settings", settings_path)
            legacy_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(legacy_module)
            
            # Извлекаем конфигурационные словари
            config_vars = [
                'DATABASE_CONFIG', 'APP_CONFIG', 'DASHBOARD_CONFIG', 
                'COLOR_SCHEME', 'DATA_FORMATS'
            ]
            
            for var_name in config_vars:
                if hasattr(legacy_module, var_name):
                    legacy_config[var_name] = getattr(legacy_module, var_name)
                    self.migration_log.append(f"Импортирован {var_name}")
            
            return legacy_config
            
        except Exception as e:
            logger.warning(f"Не удалось прочитать старые настройки: {e}")
            return {}
    
    def _convert_legacy_to_new(self, legacy_config: Dict[str, Any]) -> Dict[str, Any]:
        """Конвертирует старую конфигурацию в новый формат"""
        new_config = {}
        
        # Конвертируем настройки базы данных
        if 'DATABASE_CONFIG' in legacy_config:
            db_config = legacy_config['DATABASE_CONFIG']
            new_config['database'] = {
                'host': db_config.get('host', 'localhost'),
                'port': db_config.get('port', 5432),
                'database': db_config.get('database', 'contracts_db'),
                'user': db_config.get('user', 'postgres'),
                'password': db_config.get('password', ''),
            }
            self.migration_log.append("Конвертированы настройки БД")
        
        # Конвертируем настройки UI
        if 'APP_CONFIG' in legacy_config:
            app_config = legacy_config['APP_CONFIG']
            new_config['ui'] = {
                'title': app_config.get('title', 'Дашборд освоения договоров'),
                'icon': app_config.get('icon', '📊'),
                'layout': app_config.get('layout', 'wide'),
                'initial_sidebar_state': app_config.get('initial_sidebar_state', 'expanded'),
            }
            self.migration_log.append("Конвертированы настройки UI")
        
        # Конвертируем настройки дашборда
        if 'DASHBOARD_CONFIG' in legacy_config:
            dash_config = legacy_config['DASHBOARD_CONFIG']
            new_config['dashboard'] = {
                'default_date_range': dash_config.get('default_date_range', 12),
                'refresh_interval': dash_config.get('refresh_interval', 300),
            }
            
            # UI настройки из дашборда
            if 'ui' not in new_config:
                new_config['ui'] = {}
            new_config['ui'].update({
                'page_size': dash_config.get('page_size', 50),
                'chart_height': dash_config.get('chart_height', 400),
            })
            self.migration_log.append("Конвертированы настройки дашборда")
        
        # Конвертируем цветовую схему
        if 'COLOR_SCHEME' in legacy_config:
            colors = legacy_config['COLOR_SCHEME']
            new_config['colors'] = {
                'primary': colors.get('primary', '#3b82f6'),
                'secondary': colors.get('secondary', '#2563eb'),
                'success': colors.get('success', '#1e40af'),
                'warning': colors.get('warning', '#60a5fa'),
                'danger': colors.get('danger', '#93c5fd'),
                'info': colors.get('info', '#1d4ed8'),
                'background': colors.get('background', '#f8fafc'),
                'text_primary': colors.get('text_primary', '#1f2937'),
                'text_secondary': colors.get('text_secondary', '#6b7280'),
                'border': colors.get('border', '#3b82f6'),
                'card_bg': colors.get('card_bg', '#ffffff'),
            }
            self.migration_log.append("Конвертирована цветовая схема")
        
        return new_config
    
    def backup_current_config(self, backup_dir: str = "config/backups") -> str:
        """Создает резервную копию текущей конфигурации"""
        try:
            # Создаем директорию для бэкапов
            backup_path = Path(backup_dir)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # Генерируем имя файла бэкапа
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_path / f"config_backup_{timestamp}.json"
            
            # Получаем текущую конфигурацию
            from config import app_config
            config_dict = app_config.to_dict()
            
            # Сохраняем в JSON
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ Создана резервная копия конфигурации: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"❌ Ошибка создания резервной копии: {e}")
            raise
    
    def restore_from_backup(self, backup_file: str) -> AppConfig:
        """Восстанавливает конфигурацию из резервной копии"""
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            # Создаем объект конфигурации
            restored_config = AppConfig(**config_dict)
            
            logger.info(f"✅ Конфигурация восстановлена из: {backup_file}")
            return restored_config
            
        except Exception as e:
            logger.error(f"❌ Ошибка восстановления конфигурации: {e}")
            raise
    
    def generate_env_template(self, output_file: str = ".env.template") -> None:
        """Генерирует шаблон файла .env с комментариями"""
        try:
            template_content = self._get_env_template()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            logger.info(f"✅ Создан шаблон .env файла: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ Ошибка создания шаблона .env: {e}")
            raise
    
    def _get_env_template(self) -> str:
        """Возвращает содержимое шаблона .env файла"""
        return """# Конфигурация дашборда освоения договоров
# Скопируйте этот файл в .env и настройте значения

# =============================================================================
# НАСТРОЙКИ БАЗЫ ДАННЫХ
# =============================================================================

# Хост базы данных PostgreSQL
DB_HOST=localhost

# Порт базы данных (обычно 5432)
DB_PORT=5432

# Имя базы данных
DB_NAME=contracts_db

# Пользователь базы данных
DB_USER=postgres

# Пароль базы данных (ОБЯЗАТЕЛЬНО установите в продакшене!)
DB_PASSWORD=your_secure_password_here

# Настройки пула соединений
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10

# =============================================================================
# НАСТРОЙКИ ПРИЛОЖЕНИЯ
# =============================================================================

# Название приложения
APP_TITLE=Дашборд освоения договоров

# Иконка приложения (эмодзи)
APP_ICON=📊

# Макет страницы (wide или centered)
APP_LAYOUT=wide

# Состояние боковой панели (expanded, collapsed, auto)
APP_SIDEBAR_STATE=expanded

# Тема интерфейса (light, dark, auto)
APP_THEME=light

# Размер страницы для таблиц
APP_PAGE_SIZE=50

# Высота графиков в пикселях
APP_CHART_HEIGHT=400

# Включить анимации (true/false)
APP_ANIMATIONS=true

# =============================================================================
# НАСТРОЙКИ КЕШИРОВАНИЯ
# =============================================================================

# Включить кеширование (true/false)
CACHE_ENABLED=true

# TTL по умолчанию в секундах
CACHE_DEFAULT_TTL=300

# TTL для сводки дашборда
CACHE_DASHBOARD_TTL=300

# TTL для списка договоров
CACHE_CONTRACTS_TTL=600

# TTL для месячного прогресса
CACHE_PROGRESS_TTL=1800

# TTL для статических данных
CACHE_STATIC_TTL=3600

# Максимальный размер кеша
CACHE_MAX_SIZE=1000

# Интервал очистки кеша в секундах
CACHE_CLEANUP_INTERVAL=3600

# =============================================================================
# НАСТРОЙКИ ДАШБОРДА
# =============================================================================

# Диапазон дат по умолчанию в месяцах
DEFAULT_DATE_RANGE=12

# Интервал обновления в секундах
REFRESH_INTERVAL=300

# Максимум договоров для отображения
MAX_CONTRACTS_DISPLAY=1000

# Включить обновления в реальном времени (true/false)
ENABLE_REAL_TIME=false

# Показывать отладочную информацию (true/false)
SHOW_DEBUG_INFO=false

# Доступные форматы экспорта (через запятую)
EXPORT_FORMATS=csv,xlsx,json

# =============================================================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# =============================================================================

# Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Включить запись в файл (true/false)
LOG_FILE_ENABLED=true

# Путь к файлу логов
LOG_FILE_PATH=logs/app.log

# Включить вывод в консоль (true/false)
LOG_CONSOLE_ENABLED=true

# =============================================================================
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# =============================================================================

# Секретный ключ (ОБЯЗАТЕЛЬНО установите в продакшене!)
SECRET_KEY=your_very_secure_secret_key_here_32_chars_minimum

# Таймаут сессии в секундах
SESSION_TIMEOUT=3600

# Максимум попыток входа
MAX_LOGIN_ATTEMPTS=5

# Разрешенные хосты (через запятую)
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# ОБЩИЕ НАСТРОЙКИ
# =============================================================================

# Режим отладки (true/false) - НИКОГДА не включайте в продакшене!
DEBUG=false

# Окружение (development, staging, production)
ENVIRONMENT=development

# Версия приложения
APP_VERSION=1.0.0
"""
    
    def get_migration_log(self) -> List[str]:
        """Возвращает лог миграции"""
        return self.migration_log.copy()
    
    def print_migration_summary(self) -> None:
        """Выводит сводку миграции"""
        print("\n" + "="*50)
        print("📋 СВОДКА МИГРАЦИИ КОНФИГУРАЦИИ")
        print("="*50)
        
        if self.migration_log:
            print(f"\n✅ Выполнено операций: {len(self.migration_log)}")
            for i, operation in enumerate(self.migration_log, 1):
                print(f"{i}. {operation}")
        else:
            print("\n❌ Операции миграции не выполнялись")
        
        print("\n" + "="*50)


# Глобальный экземпляр мигратора
config_migrator = ConfigMigrator()
