# 🔧 Система конфигурации дашборда

Централизованная система конфигурации с валидацией, типизацией и обратной совместимостью.

## 📋 Содержание

- [Быстрый старт](#быстрый-старт)
- [Структура конфигурации](#структура-конфигурации)
- [Валидация](#валидация)
- [Миграция](#миграция)
- [CLI утилиты](#cli-утилиты)
- [Обратная совместимость](#обратная-совместимость)

## 🚀 Быстрый старт

### 1. Создание конфигурации

```bash
# Создать шаблон .env файла
python config/check_config.py --generate

# Скопировать и настроить
cp .env.template .env
# Отредактируйте .env файл

# Проверить конфигурацию
python config/check_config.py --check
```

### 2. Использование в коде

```python
# Новый способ (рекомендуется)
from config import app_config

# Доступ к настройкам
print(app_config.ui.title)
print(app_config.database.host)
print(app_config.cache.enabled)

# Старый способ (обратная совместимость)
from config import DATABASE_CONFIG, APP_CONFIG
print(DATABASE_CONFIG['host'])
print(APP_CONFIG['title'])
```

## 🏗️ Структура конфигурации

### Основные разделы

```python
app_config = AppConfig(
    database=DatabaseConfig(...),      # Настройки БД
    ui=UIConfig(...),                  # Интерфейс
    cache=CacheConfig(...),            # Кеширование
    dashboard=DashboardConfig(...),    # Дашборд
    colors=ColorSchemeConfig(...),     # Цвета
    logging=LoggingConfig(...),        # Логирование
    security=SecurityConfig(...),      # Безопасность
    performance=PerformanceConfig(...) # Производительность
)
```

### Конфигурации компонентов

```python
from config import components_config

# Настройки графиков
chart_config = components_config.charts
print(chart_config.height)  # 400
print(chart_config.color_palette)  # ['#3b82f6', ...]

# Настройки таблиц
table_config = components_config.tables
print(table_config.default_page_size)  # 20
print(table_config.sortable)  # True
```

## ✅ Валидация

### Автоматическая валидация

Конфигурация автоматически валидируется при загрузке:

```python
from config.validators import validate_config_on_startup

try:
    config = validate_config_on_startup()
    print("✅ Конфигурация валидна")
except ConfigValidationError as e:
    print(f"❌ Ошибка: {e.message}")
    for error in e.errors:
        print(f"- {error['message']}")
```

### Типы проверок

- **Обязательные параметры**: DB_HOST, DB_NAME, DB_USER
- **Форматы данных**: email, URL, IP-адреса
- **Диапазоны значений**: порты (1-65535), TTL (60-86400)
- **Подключения**: тест подключения к БД
- **Безопасность**: секретные ключи в продакшене
- **Файловая система**: права доступа, существование директорий

### Уровни валидации

- **Ошибки** (критические): останавливают запуск
- **Предупреждения**: логируются, но не блокируют

## 🔄 Миграция

### Из старого формата

```bash
# Автоматическая миграция
python config/check_config.py --migrate

# С резервной копией
python config/check_config.py --migrate --backup-dir config/backups

# Без резервной копии
python config/check_config.py --migrate --no-backup
```

### Программная миграция

```python
from config.migration import config_migrator

# Миграция из старого файла
new_config = config_migrator.migrate_from_legacy("config/old_settings.py")

# Создание резервной копии
backup_file = config_migrator.backup_current_config()

# Восстановление из резервной копии
restored_config = config_migrator.restore_from_backup(backup_file)
```

## 🛠️ CLI утилиты

### Основные команды

```bash
# Проверка конфигурации
python config/check_config.py --check

# Показать текущую конфигурацию
python config/check_config.py --show

# Создать шаблон .env
python config/check_config.py --generate

# Создать резервную копию
python config/check_config.py --backup

# Мигрировать из старого формата
python config/check_config.py --migrate
```

### Дополнительные опции

```bash
# Подробный вывод
python config/check_config.py --check --verbose

# Показать чувствительные данные
python config/check_config.py --show --show-sensitive

# Указать файл вывода
python config/check_config.py --generate --output .env.production

# Указать директорию для резервных копий
python config/check_config.py --backup --backup-dir /path/to/backups
```

## 🔙 Обратная совместимость

### Старые импорты работают

```python
# Эти импорты продолжают работать
from config.settings import DATABASE_CONFIG, APP_CONFIG
from config.constants import CONTRACT_STATUSES, SQL_QUERIES

# Автоматически преобразуются из новой конфигурации
print(DATABASE_CONFIG['host'])  # из app_config.database.host
print(APP_CONFIG['title'])      # из app_config.ui.title
```

### Переменные окружения

Все старые переменные окружения поддерживаются:

```bash
# Старые переменные
DB_HOST=localhost
APP_TITLE=My Dashboard
DEFAULT_DATE_RANGE=12

# Новые переменные (расширенные)
DB_POOL_SIZE=10
CACHE_ENABLED=true
LOG_LEVEL=INFO
```

## 📊 Переменные окружения

### Критически важные

```bash
DB_HOST=localhost           # Хост БД
DB_NAME=contracts_db        # Имя БД
DB_USER=postgres           # Пользователь БД
```

### Рекомендуемые

```bash
DB_PASSWORD=secure_password # Пароль БД
SECRET_KEY=32_char_key     # Секретный ключ
LOG_LEVEL=INFO             # Уровень логирования
```

### Опциональные

```bash
# База данных
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# Кеширование
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=300
CACHE_MAX_SIZE=1000

# UI
APP_LAYOUT=wide
APP_THEME=light
APP_ANIMATIONS=true

# Дашборд
REFRESH_INTERVAL=300
MAX_CONTRACTS_DISPLAY=1000
EXPORT_FORMATS=csv,xlsx,json
```

## 🔍 Отладка

### Проверка конфигурации

```python
# Показать все настройки
from config import app_config
print(app_config.dict())

# Проверить валидацию
from config.validators import config_validator
is_valid, errors, warnings = config_validator.validate_full_config(app_config)

# Показать статистику
print(f"Ошибок: {len(errors)}")
print(f"Предупреждений: {len(warnings)}")
```

### Тестирование

```bash
# Запустить тесты конфигурации
python config/test_config.py

# Проверить обратную совместимость
python -c "from config import DATABASE_CONFIG; print(DATABASE_CONFIG)"
```

## 🚨 Частые проблемы

### 1. Ошибка импорта Pydantic

```bash
# Установить зависимости
pip install pydantic psutil

# Или через poetry
poetry install
```

### 2. Отсутствует .env файл

```bash
# Создать из шаблона
python config/check_config.py --generate
cp .env.template .env
```

### 3. Неверные переменные окружения

```bash
# Проверить конфигурацию
python config/check_config.py --check --verbose
```

### 4. Проблемы с БД

```bash
# Проверить подключение
python -c "from config import app_config; print(app_config.database.get_connection_string())"
```

## 📈 Производительность

### Кеширование конфигурации

Конфигурация загружается один раз при импорте и кешируется:

```python
# Первый импорт - загрузка и валидация
from config import app_config  # ~50ms

# Последующие импорты - из кеша
from config import app_config  # ~1ms
```

### Ленивая загрузка

Компоненты загружаются по требованию:

```python
# Загружается только при первом обращении
chart_config = components_config.charts  # Загрузка
table_config = components_config.tables  # Из кеша
```

## 🔐 Безопасность

### Чувствительные данные

```python
# Пароли и ключи не логируются
logger.info(f"DB host: {app_config.database.host}")  # ✅ OK
logger.info(f"DB password: {app_config.database.password}")  # ❌ Не делайте так!

# Используйте маскирование
password_masked = "*" * len(app_config.database.password) if app_config.database.password else "не установлен"
logger.info(f"DB password: {password_masked}")  # ✅ OK
```

### Валидация в продакшене

```python
# Автоматические проверки безопасности
if app_config.environment == 'production':
    assert app_config.security.secret_key, "SECRET_KEY обязателен в продакшене"
    assert not app_config.debug, "DEBUG должен быть False в продакшене"
    assert app_config.database.password, "DB_PASSWORD обязателен в продакшене"
```

---

## 📞 Поддержка

При возникновении проблем:

1. Запустите диагностику: `python config/check_config.py --check --verbose`
2. Проверьте логи: `tail -f logs/app.log`
3. Создайте issue с выводом диагностики
