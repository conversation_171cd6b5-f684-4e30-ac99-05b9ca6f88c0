"""
Тесты для моделей данных
"""
import pytest
import pandas as pd
from datetime import date, datetime
from data.models import Contract, ContractProgress, DataProcessor


class TestContract:
    """Тесты модели Contract"""
    
    def test_contract_creation(self):
        """Тест создания договора"""
        contract = Contract(
            contract_id=1,
            contract_number="Д-2024-001",
            contract_name="Тестовый договор",
            contract_type="construction",
            status="active",
            total_amount=1000000.0,
            start_date=date(2024, 1, 1),
            end_date=date(2024, 12, 31),
            contractor_name="ООО Тест"
        )
        
        assert contract.contract_id == 1
        assert contract.contract_number == "Д-2024-001"
        assert contract.is_active == True
        assert contract.duration_days == 365
    
    def test_contract_properties(self):
        """Тест свойств договора"""
        # Активный договор
        active_contract = Contract(
            contract_id=1, contract_number="Д-001", contract_name="Тест",
            contract_type="construction", status="active", total_amount=1000000.0,
            start_date=date(2024, 1, 1), end_date=date(2024, 12, 31),
            contractor_name="ООО Тест"
        )
        assert active_contract.is_active == True
        
        # Просроченный договор
        overdue_contract = Contract(
            contract_id=2, contract_number="Д-002", contract_name="Тест",
            contract_type="construction", status="active", total_amount=1000000.0,
            start_date=date(2023, 1, 1), end_date=date(2023, 12, 31),
            contractor_name="ООО Тест"
        )
        assert overdue_contract.is_overdue == True


class TestContractProgress:
    """Тесты модели ContractProgress"""
    
    def test_progress_creation(self):
        """Тест создания прогресса"""
        progress = ContractProgress(
            progress_id=1,
            contract_id=1,
            report_date=date(2024, 7, 25),
            amount_planned=100000.0,
            amount_completed=80000.0,
            completion_percentage=80.0
        )
        
        assert progress.progress_id == 1
        assert progress.contract_id == 1
        assert progress.is_on_schedule == False  # 80000 < 100000
    
    def test_progress_on_schedule(self):
        """Тест проверки выполнения по графику"""
        # Выполняется по графику
        on_schedule = ContractProgress(
            progress_id=1, contract_id=1, report_date=date(2024, 7, 25),
            amount_planned=100000.0, amount_completed=120000.0,
            completion_percentage=120.0
        )
        assert on_schedule.is_on_schedule == True
        
        # Отстает от графика
        behind_schedule = ContractProgress(
            progress_id=2, contract_id=1, report_date=date(2024, 7, 25),
            amount_planned=100000.0, amount_completed=80000.0,
            completion_percentage=80.0
        )
        assert behind_schedule.is_on_schedule == False


class TestDataProcessor:
    """Тесты процессора данных"""
    
    def test_process_contracts_data(self):
        """Тест обработки данных договоров"""
        df = pd.DataFrame({
            'contract_id': [1, 2],
            'contract_number': ['Д-001', 'Д-002'],
            'contract_name': ['Тест 1', 'Тест 2'],
            'contract_type': ['construction', 'supply'],
            'status': ['active', 'completed'],
            'total_amount': [1000000.0, 500000.0],
            'start_date': ['2024-01-01', '2024-02-01'],
            'end_date': ['2024-12-31', '2024-11-30'],
            'contractor_name': ['ООО Тест 1', 'ООО Тест 2']
        })
        
        processor = DataProcessor()
        contracts = processor.process_contracts_data(df)
        
        assert len(contracts) == 2
        assert isinstance(contracts[0], Contract)
        assert contracts[0].contract_id == 1
        assert contracts[0].start_date == date(2024, 1, 1)
    
    def test_calculate_monthly_metrics(self):
        """Тест вычисления месячных метрик"""
        df = pd.DataFrame({
            'contract_id': [1, 1, 2, 2],
            'report_date': pd.to_datetime([
                '2024-01-31', '2024-02-29', '2024-01-31', '2024-02-29'
            ]),
            'amount_planned': [100000, 150000, 80000, 120000],
            'amount_completed': [90000, 140000, 75000, 110000]
        })
        
        processor = DataProcessor()
        monthly_data = processor.calculate_monthly_metrics(df)
        
        assert len(monthly_data) == 2  # 2 месяца
        assert 'completion_rate' in monthly_data.columns
        
        # Проверяем первый месяц
        jan_data = monthly_data[monthly_data['month'] == '2024-01']
        assert len(jan_data) == 1
        assert jan_data.iloc[0]['contracts_count'] == 2
    
    def test_get_summary_statistics(self):
        """Тест получения сводной статистики"""
        contracts = [
            Contract(1, 'Д-001', 'Тест 1', 'construction', 'active', 1000000.0,
                    date(2024, 1, 1), date(2024, 12, 31), 'ООО Тест 1'),
            Contract(2, 'Д-002', 'Тест 2', 'supply', 'completed', 500000.0,
                    date(2024, 2, 1), date(2024, 11, 30), 'ООО Тест 2'),
            Contract(3, 'Д-003', 'Тест 3', 'construction', 'active', 800000.0,
                    date(2023, 1, 1), date(2023, 12, 31), 'ООО Тест 3')  # Просроченный
        ]
        
        processor = DataProcessor()
        stats = processor.get_summary_statistics(contracts)
        
        assert stats['total_contracts'] == 3
        assert stats['active_contracts'] == 2
        assert stats['overdue_contracts'] == 1
        assert stats['total_amount'] == 2300000.0
        assert stats['average_amount'] == 2300000.0 / 3
        assert stats['overdue_rate'] == 1/3 * 100


if __name__ == "__main__":
    pytest.main([__file__])
