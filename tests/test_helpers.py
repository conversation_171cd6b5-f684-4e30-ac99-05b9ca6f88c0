"""
Тесты для вспомогательных функций
"""
import pytest
import pandas as pd
from datetime import date, datetime
from utils.helpers import (
    format_currency, format_percentage, format_date,
    calculate_completion_rate, safe_divide, get_month_name_ru,
    validate_dataframe, clean_numeric_column
)


class TestFormatFunctions:
    """Тесты функций форматирования"""
    
    def test_format_currency(self):
        """Тест форматирования валюты"""
        assert format_currency(1000.50) == "1,000.50 ₽"
        assert format_currency(0) == "0.00 ₽"
        assert format_currency(pd.NA) == "—"
    
    def test_format_percentage(self):
        """Тест форматирования процентов"""
        assert format_percentage(75.5) == "75.5%"
        assert format_percentage(100.0, 0) == "100%"
        assert format_percentage(pd.NA) == "—"
    
    def test_format_date(self):
        """Тест форматирования дат"""
        test_date = datetime(2024, 7, 25)
        assert format_date(test_date) == "25.07.2024"
        assert format_date("2024-07-25") == "25.07.2024"
        assert format_date(pd.NA) == "—"


class TestCalculationFunctions:
    """Тесты вычислительных функций"""
    
    def test_safe_divide(self):
        """Тест безопасного деления"""
        assert safe_divide(10, 2) == 5.0
        assert safe_divide(10, 0) == 0.0
        assert safe_divide(10, 0, default=100) == 100
        assert safe_divide(pd.NA, 5) == 0.0
    
    def test_calculate_completion_rate(self):
        """Тест вычисления процента выполнения"""
        assert calculate_completion_rate(50, 100) == 50.0
        assert calculate_completion_rate(100, 100) == 100.0
        assert calculate_completion_rate(50, 0) == 0.0
    
    def test_get_month_name_ru(self):
        """Тест получения названия месяца на русском"""
        assert get_month_name_ru(1) == "Январь"
        assert get_month_name_ru(12) == "Декабрь"
        assert get_month_name_ru(13) == "Месяц 13"


class TestDataValidation:
    """Тесты валидации данных"""
    
    def test_validate_dataframe(self):
        """Тест валидации DataFrame"""
        df = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})
        assert validate_dataframe(df, ['a', 'b']) == True
        assert validate_dataframe(df, ['a', 'c']) == False
        assert validate_dataframe(pd.DataFrame(), ['a']) == False
        assert validate_dataframe(None, ['a']) == False
    
    def test_clean_numeric_column(self):
        """Тест очистки числовых колонок"""
        df = pd.DataFrame({'amount': ['100', '200.5', 'invalid', None]})
        cleaned_df = clean_numeric_column(df, 'amount')
        
        assert cleaned_df['amount'].iloc[0] == 100.0
        assert cleaned_df['amount'].iloc[1] == 200.5
        assert cleaned_df['amount'].iloc[2] == 0.0  # invalid -> 0
        assert cleaned_df['amount'].iloc[3] == 0.0  # None -> 0


if __name__ == "__main__":
    pytest.main([__file__])
