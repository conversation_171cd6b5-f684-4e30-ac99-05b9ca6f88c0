"""
Основная страница дашборда
"""
import streamlit as st
import pandas as pd
import logging
from typing import Dict, Any

from app.pages.base_page import BasePage
from data.loader import data_loader
from components.dashboard_components import dashboard_components
from components.charts import chart_components
from components.tables import table_components
from app.components.active_filters_panel import ActiveFiltersPanel

logger = logging.getLogger(__name__)


def check_database_connection():
    """Проверяет подключение к базе данных"""
    from data.database import db_connection
    
    if not db_connection.test_connection():
        st.error("❌ Нет подключения к базе данных")
        st.info("Проверьте настройки подключения в файле .env")
        st.stop()


def apply_dashboard_filters(df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
    """Применяет фильтры дашборда к DataFrame"""
    if not isinstance(df, pd.DataFrame) or df.empty:
        return df if isinstance(df, pd.DataFrame) else pd.DataFrame()
    
    filtered_df = df.copy()
    
    # Применяем фильтры (базовая реализация)
    if filters.get('status_filter') and filters['status_filter'] != 'Все':
        filtered_df = filtered_df[filtered_df['status'] == filters['status_filter']]
    
    if filters.get('type_filter') and filters['type_filter'] != 'Все':
        filtered_df = filtered_df[filtered_df['contract_type'] == filters['type_filter']]
    
    return filtered_df


def render_speedometer_section(summary: Dict[str, Any]):
    """Отрисовывает секцию со спидометром"""
    st.markdown("""
    <h2 style="
        color: #1e40af;
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0 0 1.5rem 0;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        background: linear-gradient(90deg, #1e40af, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    ">🎯 Спидометр освоения</h2>
    """, unsafe_allow_html=True)
    
    # Вычисляем данные для спидометра
    total_amount = summary.get('total_amount', 0)
    # Предполагаем, что освоено 70% от общей суммы (в реальности это должно браться из БД)
    completed_amount = total_amount * 0.7  # Заглушка
    
    dashboard_components.render_progress_speedometer(completed_amount, total_amount)


def render_quarterly_section(filtered_df: pd.DataFrame):
    """Отрисовывает секцию с квартальным графиком"""
    st.markdown("""
    <h2 style="
        color: #1e40af;
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0 0 1.5rem 0;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        background: linear-gradient(90deg, #1e40af, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    ">📊 Динамика по кварталам</h2>
    """, unsafe_allow_html=True)
    
    dashboard_components.render_quarterly_chart(filtered_df)


def render_analytics_section(filtered_df: pd.DataFrame):
    """Отрисовывает секцию дополнительной аналитики"""
    st.markdown("""
    <h2 style="
        color: #1e40af;
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0 0 1.5rem 0;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        background: linear-gradient(90deg, #1e40af, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    ">📈 Дополнительная аналитика</h2>
    """, unsafe_allow_html=True)
    
    # Графики распределения
    col3, col4 = st.columns(2)
    
    with col3:
        if len(filtered_df['status'].unique()) > 1:
            status_chart = chart_components.contracts_by_status_pie(
                filtered_df,
                "Распределение по статусам"
            )
            st.plotly_chart(status_chart, use_container_width=True)
    
    with col4:
        if len(filtered_df['contract_type'].unique()) > 1:
            type_chart = chart_components.contracts_by_type_bar(
                filtered_df,
                "Распределение по типам"
            )
            st.plotly_chart(type_chart, use_container_width=True)


def render_contracts_table_section(filtered_df: pd.DataFrame, contracts_df: pd.DataFrame):
    """Отрисовывает секцию с таблицей договоров"""
    st.markdown("""
    <h2 style="
        color: #1e40af;
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0 0 1.5rem 0;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        background: linear-gradient(90deg, #1e40af, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    ">📋 Список договоров</h2>
    """, unsafe_allow_html=True)
    
    # Показываем статистику по отфильтрованным данным
    col5, col6, col7, col8 = st.columns(4)
    
    with col5:
        st.metric("Найдено договоров", f"{len(filtered_df):,}")
    with col6:
        filtered_sum = filtered_df['total_amount'].sum()
        st.metric("Общая сумма", f"{filtered_sum:,.0f} ₽")
    with col7:
        active_filtered = len(filtered_df[filtered_df['status'] == 'active'])
        st.metric("Активных", f"{active_filtered:,}")
    with col8:
        percentage_found = (len(filtered_df) / len(contracts_df)) * 100 if len(contracts_df) > 0 else 0
        st.metric("Доля выборки", f"{percentage_found:.1f}%")
    
    # Отображаем таблицу
    table_components.contracts_table(
        filtered_df.head(20),  # Показываем первые 20 записей
        show_filters=False,
        page_size=20
    )


def render_new_dashboard():
    """Отрисовывает новый строгий и функциональный дашборд"""
    try:
        # Проверяем подключение к БД
        check_database_connection()

        # Отрисовываем строгий заголовок
        dashboard_components.render_header()

        # Загружаем данные
        contracts_df = data_loader.load_contracts_overview()
        summary = data_loader.get_dashboard_summary()

        if contracts_df is None or contracts_df.empty:
            st.error("Нет данных для отображения дашборда")
            return

        # 1. ФИЛЬТРЫ СВЕРХУ
        st.markdown("---")
        filters = dashboard_components.render_filter_panel(contracts_df)

        # Применяем фильтры к данным
        filtered_df = apply_dashboard_filters(contracts_df, filters)

        # 2. КЛЮЧЕВЫЕ ПОКАЗАТЕЛИ
        st.markdown("---")
        dashboard_components.render_key_metrics(summary)

        # 3. СПИДОМЕТР И ГРАФИК ПО КВАРТАЛАМ
        st.markdown("---")
        col1, col2 = st.columns([1, 1])

        with col1:
            render_speedometer_section(summary)

        with col2:
            render_quarterly_section(filtered_df)

        # 4. АНАЛИЗ РИСКОВ
        st.markdown("---")
        dashboard_components.render_risk_analysis(filtered_df)

        # 5. АНАЛИЗ ПО ПОДРЯДЧИКАМ
        st.markdown("---")
        dashboard_components.render_contractor_analysis(filtered_df)

        # 6. ДОПОЛНИТЕЛЬНАЯ АНАЛИТИКА (если включены графики)
        if filters.get('show_charts', True):
            st.markdown("---")
            render_analytics_section(filtered_df)

        # 7. ТАБЛИЦА ДОГОВОРОВ
        st.markdown("---")
        render_contracts_table_section(filtered_df, contracts_df)

    except Exception as e:
        logger.error(f"Ошибка в новом дашборде: {e}")
        st.error(f"Произошла ошибка: {e}")
        st.info("Обратитесь к администратору системы")


class DashboardPage(BasePage):
    """Класс для основной страницы дашборда"""

    def __init__(self):
        super().__init__("Дашборд освоения договоров", "📊")

    def render_content(self) -> None:
        """Отрисовывает содержимое дашборда"""
        # Загружаем данные
        data = self.load_data()

        if not data['success']:
            self.handle_error(data.get('error', 'Неизвестная ошибка'))
            return

        contracts_df = data['contracts_df']
        summary = data['summary']

        if contracts_df is None or contracts_df.empty:
            self.render_empty_state("Нет данных для отображения дашборда")
            return

        # 1. ФИЛЬТРЫ СВЕРХУ
        st.markdown("---")
        filters = dashboard_components.render_filter_panel(contracts_df)

        # 1.1. ПАНЕЛЬ АКТИВНЫХ ФИЛЬТРОВ (отображается автоматически при наличии активных фильтров)
        self._render_active_filters_panel(filters, contracts_df)

        # Применяем фильтры к данным
        filtered_df = self.apply_filters(contracts_df, filters)

        # 2. КЛЮЧЕВЫЕ ПОКАЗАТЕЛИ
        st.markdown("---")
        dashboard_components.render_key_metrics(summary)

        # 3. СПИДОМЕТР И ГРАФИК ПО КВАРТАЛАМ
        st.markdown("---")
        col1, col2 = st.columns([1, 1])

        with col1:
            render_speedometer_section(summary)

        with col2:
            render_quarterly_section(filtered_df)

        # 4. АНАЛИЗ РИСКОВ
        st.markdown("---")
        dashboard_components.render_risk_analysis(filtered_df)

        # 5. АНАЛИЗ ПО ПОДРЯДЧИКАМ
        st.markdown("---")
        dashboard_components.render_contractor_analysis(filtered_df)

        # 6. ДОПОЛНИТЕЛЬНАЯ АНАЛИТИКА (если включены графики)
        if filters.get('show_charts', True):
            st.markdown("---")
            render_analytics_section(filtered_df)

        # 7. ТАБЛИЦА ДОГОВОРОВ
        st.markdown("---")
        render_contracts_table_section(filtered_df, contracts_df)

    def _render_active_filters_panel(self, filters: Dict[str, Any], contracts_df: pd.DataFrame) -> None:
        """Отрисовывает панель активных фильтров"""
        try:
            # Преобразуем фильтры в формат, ожидаемый ActiveFiltersPanel
            active_filters = self._convert_filters_to_active_format(filters)

            # Получаем доступные статусы и типы
            available_statuses = sorted(list(contracts_df['status'].unique())) if not contracts_df.empty and 'status' in contracts_df.columns else ['active', 'completed', 'suspended', 'cancelled']
            available_types = sorted(list(contracts_df['contract_type'].unique())) if not contracts_df.empty and 'contract_type' in contracts_df.columns else ['construction', 'services', 'supply']

            # Создаем панель активных фильтров
            active_filters_panel = ActiveFiltersPanel()

            # Отрисовываем панель и получаем результат нажатия кнопки сброса
            reset_clicked = active_filters_panel.render(active_filters, available_statuses, available_types)

            # Обрабатываем сброс фильтров если была нажата кнопка
            if reset_clicked:
                logger.info("🔄 СБРОС ФИЛЬТРОВ: Кнопка сброса была нажата")
                self._reset_all_filters()
                st.rerun()

            # Применяем отступ для основного контента
            active_filters_panel.apply_main_content_padding()

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки панели активных фильтров: {e}")

    def _convert_filters_to_active_format(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Преобразует фильтры дашборда в формат для панели активных фильтров"""
        active_filters = {}

        logger.info(f"🔄 КОНВЕРТАЦИЯ ФИЛЬТРОВ: Входные фильтры: {filters}")

        # Преобразуем статус - поддерживаем как одиночные значения, так и списки
        status_filter = filters.get('status_filter')
        if status_filter and status_filter != 'Все':
            if isinstance(status_filter, list):
                active_filters['status'] = [s for s in status_filter if s != 'Все']
            else:
                active_filters['status'] = [status_filter]
        else:
            active_filters['status'] = []

        # Преобразуем тип договора - поддерживаем как одиночные значения, так и списки
        type_filter = filters.get('type_filter')
        if type_filter and type_filter != 'Все':
            if isinstance(type_filter, list):
                active_filters['contract_type'] = [t for t in type_filter if t != 'Все']
            else:
                active_filters['contract_type'] = [type_filter]
        else:
            active_filters['contract_type'] = []

        # Преобразуем диапазон сумм
        amount_filter = filters.get('amount_filter', 'Все суммы')
        active_filters['amount_filter'] = amount_filter
        if amount_filter and amount_filter != 'Все суммы':
            active_filters['amount_range'] = self._convert_amount_filter_to_range(amount_filter)
        else:
            active_filters['amount_range'] = (0, float('inf'))

        # Добавляем другие фильтры
        active_filters['start_date'] = filters.get('start_date')
        active_filters['end_date'] = filters.get('end_date')
        active_filters['show_charts'] = filters.get('show_charts', True)

        logger.info(f"🔄 КОНВЕРТАЦИЯ ФИЛЬТРОВ: Результат: {active_filters}")
        return active_filters

    def _convert_amount_filter_to_range(self, amount_filter: str) -> tuple:
        """Преобразует текстовый фильтр суммы в числовые значения"""
        if amount_filter == 'До 10 млн':
            return (0, 10_000_000)
        elif amount_filter == '10-50 млн':
            return (10_000_000, 50_000_000)
        elif amount_filter == '50-100 млн':
            return (50_000_000, 100_000_000)
        elif amount_filter == 'Свыше 100 млн':
            return (100_000_000, float('inf'))
        else:  # 'Все суммы'
            return (0, float('inf'))

    def _reset_all_filters(self) -> None:
        """Сбрасывает все фильтры к значениям по умолчанию"""
        try:
            # Очищаем все ключи фильтров из session_state
            filter_keys = [key for key in st.session_state.keys() if key.startswith('dashboard_')]
            for key in filter_keys:
                del st.session_state[key]

            logger.info("✅ Все фильтры дашборда сброшены")
        except Exception as e:
            logger.error(f"❌ Ошибка сброса фильтров: {e}")
