"""
Базовый класс для страниц дашборда
"""
import streamlit as st
import pandas as pd
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from data.loader import data_loader
from data.database import db_connection

logger = logging.getLogger(__name__)


class BasePage(ABC):
    """Базовый класс для всех страниц дашборда"""
    
    def __init__(self, title: str, icon: str = "📊"):
        self.title = title
        self.icon = icon
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def check_database_connection(self) -> bool:
        """Проверяет подключение к базе данных"""
        if not db_connection.test_connection():
            st.error("❌ Нет подключения к базе данных")
            st.info("Проверьте настройки подключения в файле .env")
            return False
        return True
    
    def render_page_header(self) -> None:
        """Отрисовывает заголовок страницы"""
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            margin: -1rem -1rem 2rem -1rem;
            text-align: center;
            border-bottom: 4px solid #2563eb;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            border-radius: 0 0 1rem 1rem;
        ">
            <h1 style="
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                color: #ffffff;
            ">{self.icon} {self.title}</h1>
        </div>
        """, unsafe_allow_html=True)
    
    def render_section_header(self, title: str, icon: str = "📊") -> None:
        """Отрисовывает заголовок секции"""
        st.markdown(f"""
        <h2 style="
            color: #1e40af;
            font-size: 1.6rem;
            font-weight: 700;
            margin: 2rem 0 1.5rem 0;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            background: linear-gradient(90deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        ">{icon} {title}</h2>
        """, unsafe_allow_html=True)
    
    def load_data(self) -> Dict[str, Any]:
        """Загружает данные для страницы"""
        try:
            contracts_df = data_loader.load_contracts_overview()
            summary = data_loader.get_dashboard_summary()
            
            return {
                'contracts_df': contracts_df,
                'summary': summary,
                'success': True
            }
        except Exception as e:
            self.logger.error(f"Ошибка загрузки данных: {e}")
            return {
                'contracts_df': None,
                'summary': None,
                'success': False,
                'error': str(e)
            }
    
    def handle_error(self, error: str) -> None:
        """Обрабатывает ошибки страницы"""
        self.logger.error(f"Ошибка на странице {self.title}: {error}")
        st.error(f"Произошла ошибка: {error}")
        st.info("Обратитесь к администратору системы")
    
    def render_loading_state(self) -> None:
        """Отрисовывает состояние загрузки"""
        with st.spinner("Загрузка данных..."):
            st.empty()
    
    def render_empty_state(self, message: str = "Нет данных для отображения") -> None:
        """Отрисовывает состояние отсутствия данных"""
        st.warning(message)
        st.info("Проверьте подключение к базе данных или обратитесь к администратору")
    
    def apply_filters(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """Применяет фильтры к DataFrame"""
        if df is None or df.empty:
            return df
        
        filtered_df = df.copy()
        
        # Базовая фильтрация по статусу
        if filters.get('status_filter') and filters['status_filter'] != 'Все':
            filtered_df = filtered_df[filtered_df['status'] == filters['status_filter']]
        
        # Базовая фильтрация по типу
        if filters.get('type_filter') and filters['type_filter'] != 'Все':
            filtered_df = filtered_df[filtered_df['contract_type'] == filters['type_filter']]
        
        # Поиск по тексту
        if filters.get('search_query') and filters['search_query'].strip():
            search_term = filters['search_query'].lower()
            mask = (
                filtered_df['contract_name'].str.lower().str.contains(search_term, na=False) |
                filtered_df['contract_number'].str.lower().str.contains(search_term, na=False)
            )
            filtered_df = filtered_df[mask]
        
        return filtered_df
    
    @abstractmethod
    def render_content(self) -> None:
        """Отрисовывает основное содержимое страницы"""
        pass
    
    def render(self) -> None:
        """Основной метод отрисовки страницы"""
        try:
            # Проверяем подключение к БД
            if not self.check_database_connection():
                return
            
            # Отрисовываем заголовок
            self.render_page_header()
            
            # Отрисовываем содержимое
            self.render_content()
            
        except Exception as e:
            self.handle_error(str(e))
