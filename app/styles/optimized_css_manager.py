"""
Оптимизированный менеджер CSS стилей
Заменяет старый css_manager.py с улучшенной производительностью и модульностью
"""
import streamlit as st
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union
from functools import lru_cache

# Импорт конфигурации с fallback
try:
    from config.app_config import app_config
except ImportError:
    # Создаем простую заглушку
    class SimpleConfig:
        class UI:
            animation_enabled = True
            theme = 'light'
        ui = UI()
    app_config = SimpleConfig()

logger = logging.getLogger(__name__)


class OptimizedCSSManager:
    """Оптимизированный менеджер CSS стилей"""
    
    def __init__(self):
        self.css_cache = {}
        self.loaded_files = set()
        self.base_path = Path("static/css")
        
    @lru_cache(maxsize=32)
    def load_css_file(self, file_path: str) -> str:
        """Загружает CSS файл с кешированием"""
        try:
            full_path = self.base_path / file_path
            
            if not full_path.exists():
                logger.warning(f"CSS файл не найден: {full_path}")
                return ""
            
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.debug(f"Загружен CSS файл: {file_path}")
            return content
            
        except Exception as e:
            logger.error(f"Ошибка загрузки CSS файла {file_path}: {e}")
            return ""
    
    def inject_modular_styles(self) -> None:
        """Внедряет модульную систему стилей"""
        if "modular_styles" in self.loaded_files:
            return

        # Загружаем главный файл модульной системы
        main_css = self.load_css_file("main.css")

        if main_css:
            st.markdown(f"<style>{main_css}</style>", unsafe_allow_html=True)
            self.loaded_files.add("modular_styles")
            logger.info("✅ Модульная система стилей загружена")
        else:
            # Fallback к консолидированным стилям
            self._inject_consolidated_fallback()

    def _inject_consolidated_fallback(self) -> None:
        """Fallback к консолидированным стилям"""
        consolidated_css = self.load_css_file("dashboard-consolidated.css")

        if consolidated_css:
            st.markdown(f"<style>{consolidated_css}</style>", unsafe_allow_html=True)
            self.loaded_files.add("consolidated_fallback")
            logger.warning("⚠️ Используются консолидированные стили как fallback")
        else:
            # Последний fallback к встроенным стилям
            self._inject_fallback_styles()
    
    def _inject_fallback_styles(self) -> None:
        """Fallback стили если консолидированный файл недоступен"""
        fallback_css = self._get_fallback_css()
        st.markdown(f"<style>{fallback_css}</style>", unsafe_allow_html=True)
        logger.warning("⚠️ Используются fallback стили")
    
    @lru_cache(maxsize=1)
    def _get_fallback_css(self) -> str:
        """Возвращает базовые fallback стили"""
        colors = app_config.colors
        
        return f"""
        /* Fallback стили */
        :root {{
            --color-primary: {colors.primary};
            --color-secondary: {colors.secondary};
            --color-success: {colors.success};
            --color-warning: {colors.warning};
            --color-danger: {colors.danger};
            --color-info: {colors.info};
            --color-background: {colors.background};
            --color-text-primary: {colors.text_primary};
            --color-text-secondary: {colors.text_secondary};
            --color-border: {colors.border};
            --color-card-bg: {colors.card_bg};
            
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            
            --border-radius-sm: 0.5rem;
            --border-radius-md: 0.75rem;
            --border-radius-lg: 1rem;
            
            --shadow-sm: 0 2px 4px rgba(59, 130, 246, 0.1);
            --shadow-md: 0 4px 12px rgba(59, 130, 246, 0.15);
            --shadow-lg: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
            
            --transition-all: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        
        .stApp {{
            background: var(--color-background);
        }}
        
        .main {{
            padding: var(--spacing-lg) !important;
        }}
        
        .block-container {{
            padding-top: var(--spacing-lg) !important;
            padding-bottom: var(--spacing-lg) !important;
            max-width: none !important;
        }}
        
        .dashboard-card {{
            background: var(--color-card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            transition: var(--transition-all);
        }}
        
        .dashboard-card:hover {{
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }}
        
        .metric-card {{
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            color: white;
        }}
        
        .stButton > button {{
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: var(--border-radius-md) !important;
            padding: var(--spacing-sm) var(--spacing-lg) !important;
            font-weight: 500 !important;
            box-shadow: var(--shadow-sm) !important;
            transition: var(--transition-all) !important;
        }}
        
        .stButton > button:hover {{
            box-shadow: var(--shadow-md) !important;
            transform: translateY(-2px) !important;
        }}
        """
    
    def inject_component_styles(self, component_type: str) -> None:
        """Внедряет стили для конкретного компонента"""
        if component_type in self.loaded_files:
            return
        
        component_css = self._get_component_css(component_type)
        if component_css:
            st.markdown(f"<style>{component_css}</style>", unsafe_allow_html=True)
            self.loaded_files.add(component_type)
            logger.debug(f"Загружены стили компонента: {component_type}")
    
    def _get_component_css(self, component_type: str) -> str:
        """Возвращает CSS для конкретного компонента"""
        component_styles = {
            "charts": """
                .chart-container {
                    background: var(--color-card-bg);
                    border-radius: var(--border-radius-lg);
                    box-shadow: var(--shadow-md);
                    padding: var(--spacing-lg);
                    margin-bottom: var(--spacing-lg);
                }
                
                .chart-title {
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--color-text-primary);
                    margin-bottom: var(--spacing-md);
                    text-align: center;
                }
                
                .js-plotly-plot {
                    border-radius: var(--border-radius-md);
                    overflow: hidden;
                }
            """,
            
            "tables": """
                .table-container {
                    background: var(--color-card-bg);
                    border-radius: var(--border-radius-lg);
                    box-shadow: var(--shadow-md);
                    overflow: hidden;
                    margin-bottom: var(--spacing-lg);
                }
                
                .table-header {
                    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
                    color: white;
                    padding: var(--spacing-md) var(--spacing-lg);
                    font-weight: 600;
                    font-size: 1.125rem;
                }
                
                .stDataFrame {
                    border-radius: var(--border-radius-md) !important;
                    overflow: hidden !important;
                }
            """,
            
            "forms": """
                .stSelectbox > div > div {
                    background: var(--color-card-bg) !important;
                    border: 1px solid var(--color-border) !important;
                    border-radius: var(--border-radius-md) !important;
                }
                
                .stTextInput > div > div > input {
                    background: var(--color-card-bg) !important;
                    border: 1px solid var(--color-border) !important;
                    border-radius: var(--border-radius-md) !important;
                    color: var(--color-text-primary) !important;
                }
                
                .stTextInput > div > div > input:focus {
                    border-color: var(--color-primary) !important;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
                }
            """,
            
            "metrics": """
                .metric-value {
                    font-size: 2.25rem;
                    font-weight: 700;
                    color: white;
                    line-height: 1;
                    font-variant-numeric: tabular-nums;
                }
                
                .metric-label {
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: rgba(255, 255, 255, 0.9);
                    line-height: 1.25;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                }
                
                .metric-delta {
                    font-size: 0.875rem;
                    font-weight: 600;
                    line-height: 1.25;
                }
            """
        }
        
        return component_styles.get(component_type, "")
    
    def inject_theme_styles(self, theme: str = "light") -> None:
        """Внедряет стили темы"""
        theme_key = f"theme_{theme}"
        if theme_key in self.loaded_files:
            return
        
        theme_css = self._get_theme_css(theme)
        if theme_css:
            st.markdown(f"<style>{theme_css}</style>", unsafe_allow_html=True)
            self.loaded_files.add(theme_key)
            logger.debug(f"Загружена тема: {theme}")
    
    def _get_theme_css(self, theme: str) -> str:
        """Возвращает CSS для темы"""
        if theme == "dark":
            return """
                [data-theme="dark"] {
                    --color-background: #0f172a;
                    --color-card-bg: #1e293b;
                    --color-text-primary: #f8fafc;
                    --color-text-secondary: #cbd5e1;
                    --color-border: #334155;
                }
                
                [data-theme="dark"] .stApp {
                    background: var(--color-background);
                }
                
                [data-theme="dark"] .dashboard-card,
                [data-theme="dark"] .table-container,
                [data-theme="dark"] .chart-container {
                    background: var(--color-card-bg);
                    border: 1px solid var(--color-border);
                }
            """
        
        return ""  # Светлая тема по умолчанию
    
    def inject_responsive_styles(self) -> None:
        """Внедряет адаптивные стили"""
        if "responsive" in self.loaded_files:
            return
        
        responsive_css = """
            @media (max-width: 768px) {
                .main {
                    padding: var(--spacing-md) !important;
                }
                
                .dashboard-card,
                .metric-card {
                    padding: var(--spacing-md);
                }
                
                .metric-value {
                    font-size: 1.875rem;
                }
                
                .grid-cols-4 {
                    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
                }
                
                .grid-cols-3 {
                    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
                }
            }
            
            @media (max-width: 480px) {
                .main {
                    padding: var(--spacing-sm) !important;
                }
                
                .dashboard-card,
                .metric-card {
                    padding: var(--spacing-sm);
                    margin-bottom: var(--spacing-md);
                }
                
                .metric-value {
                    font-size: 1.5rem;
                }
                
                .stButton > button {
                    padding: var(--spacing-xs) var(--spacing-md) !important;
                    font-size: 0.875rem !important;
                }
            }
        """
        
        st.markdown(f"<style>{responsive_css}</style>", unsafe_allow_html=True)
        self.loaded_files.add("responsive")
        logger.debug("Загружены адаптивные стили")
    
    def inject_animation_styles(self) -> None:
        """Внедряет стили анимаций"""
        if not app_config.ui.animation_enabled:
            return
        
        if "animations" in self.loaded_files:
            return
        
        animation_css = """
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            @keyframes slideIn {
                from { transform: translateX(-100%); }
                to { transform: translateX(0); }
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
            
            .animate-fade-in {
                animation: fadeIn 0.5s ease-out;
            }
            
            .animate-slide-in {
                animation: slideIn 0.3s ease-out;
            }
            
            .animate-pulse {
                animation: pulse 2s infinite;
            }
            
            .dashboard-card,
            .metric-card {
                animation: fadeIn 0.5s ease-out;
            }
            
            /* Отключаем анимации для пользователей с prefers-reduced-motion */
            @media (prefers-reduced-motion: reduce) {
                *,
                *::before,
                *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        """
        
        st.markdown(f"<style>{animation_css}</style>", unsafe_allow_html=True)
        self.loaded_files.add("animations")
        logger.debug("Загружены стили анимаций")
    
    def inject_all_styles(self, theme: str = "light") -> None:
        """Внедряет все необходимые стили"""
        try:
            # Основные модульные стили (включают все компоненты)
            self.inject_modular_styles()

            # Дополнительные стили для конкретных компонентов
            if app_config.ui.animation_enabled:
                self.inject_animation_styles()

            # Устанавливаем тему через data-атрибут
            self._set_theme_attribute(theme)

            logger.info("✅ Все стили успешно загружены")

        except Exception as e:
            logger.error(f"❌ Ошибка загрузки стилей: {e}")
            # Загружаем минимальные fallback стили
            self._inject_fallback_styles()

    def _set_theme_attribute(self, theme: str) -> None:
        """Устанавливает атрибут темы для документа"""
        theme_script = f"""
        <script>
        document.documentElement.setAttribute('data-theme', '{theme}');
        </script>
        """
        st.markdown(theme_script, unsafe_allow_html=True)
        logger.debug(f"Установлена тема: {theme}")

    def toggle_theme(self) -> str:
        """Переключает тему и возвращает новую тему"""
        current_theme = getattr(st.session_state, 'theme', 'light')
        new_theme = 'dark' if current_theme == 'light' else 'light'

        st.session_state.theme = new_theme
        self._set_theme_attribute(new_theme)

        logger.info(f"Тема переключена на: {new_theme}")
        return new_theme
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Возвращает статистику кеша"""
        return {
            "loaded_files": len(self.loaded_files),
            "cache_size": len(self.css_cache),
            "cache_info": dict(self.load_css_file.cache_info()._asdict())
        }
    
    def clear_cache(self) -> None:
        """Очищает кеш стилей"""
        self.css_cache.clear()
        self.loaded_files.clear()
        self.load_css_file.cache_clear()
        logger.info("Кеш стилей очищен")


# Глобальный экземпляр менеджера
css_manager = OptimizedCSSManager()
