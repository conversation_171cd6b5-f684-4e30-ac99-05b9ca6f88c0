"""
Менеджер CSS стилей для приложения
"""
import streamlit as st
from pathlib import Path


class CSSManager:
    """Класс для управления CSS стилями"""
    
    def __init__(self):
        self.static_dir = Path("static/css")
        self.loaded_styles = set()
    
    def load_css_file(self, filename: str) -> str:
        """Загружает CSS файл и возвращает его содержимое"""
        css_path = self.static_dir / filename
        if css_path.exists():
            with open(css_path, 'r', encoding='utf-8') as f:
                return f.read()
        return ""
    
    def inject_css(self, css_content: str):
        """Внедряет CSS стили в Streamlit"""
        st.markdown(f"<style>{css_content}</style>", unsafe_allow_html=True)
    
    def load_dashboard_styles(self):
        """Загружает основные стили дашборда"""
        if "dashboard" not in self.loaded_styles:
            css_content = self.load_css_file("dashboard.css")
            if css_content:
                self.inject_css(css_content)
                self.loaded_styles.add("dashboard")
    
    def load_component_styles(self):
        """Загружает стили компонентов"""
        if "components" not in self.loaded_styles:
            css_content = self.load_css_file("components.css")
            if css_content:
                self.inject_css(css_content)
                self.loaded_styles.add("components")
    
    def load_theme_styles(self, theme: str = "default"):
        """Загружает стили темы"""
        theme_key = f"theme_{theme}"
        if theme_key not in self.loaded_styles:
            css_content = self.load_css_file(f"themes/{theme}.css")
            if css_content:
                self.inject_css(css_content)
                self.loaded_styles.add(theme_key)
    
    def load_all_styles(self):
        """Загружает все необходимые стили"""
        self.load_dashboard_styles()
        self.load_component_styles()
        self.load_theme_styles()
    
    def get_inline_styles(self) -> str:
        """Возвращает базовые встроенные стили"""
        return """
        /* Базовые стили приложения */
        .main {
            padding: 1rem;
        }
        
        .block-container {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }
        
        /* Скрываем стандартные элементы Streamlit */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        header {visibility: hidden;}
        
        /* Улучшенные стили для графиков */
        .plotly-graph-div {
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            border: 2px solid #3b82f6;
            background: white;
            margin: 1rem 0;
        }
        
        /* Стили для таблиц */
        .dataframe {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            border: 2px solid #3b82f6;
            background: white;
        }
        """


# Глобальный экземпляр менеджера стилей
css_manager = CSSManager()
