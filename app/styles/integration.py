"""
Интеграция новой модульной системы стилей с существующими компонентами
Обеспечивает обратную совместимость и плавный переход
"""
import streamlit as st
import logging
from typing import Dict, Any, Optional, List
from functools import wraps

from app.styles.optimized_css_manager import css_manager

# Импорт конфигурации с fallback
try:
    from config.app_config import app_config
except ImportError:
    # Создаем простую заглушку
    class SimpleConfig:
        class UI:
            theme = 'light'
            animation_enabled = True
        ui = UI()
    app_config = SimpleConfig()

logger = logging.getLogger(__name__)


class StyleIntegrator:
    """Класс для интеграции новых стилей с существующими компонентами"""
    
    def __init__(self):
        self.initialized = False
        self.theme = getattr(st.session_state, 'theme', app_config.ui.theme)
    
    def initialize(self) -> None:
        """Инициализирует систему стилей"""
        if self.initialized:
            return
        
        try:
            # Загружаем все стили
            css_manager.inject_all_styles(self.theme)
            
            # Добавляем переключатель темы если включен
            if app_config.ui.theme != "light":
                self._add_theme_toggle()
            
            self.initialized = True
            logger.info("✅ Система стилей инициализирована")
            
        except Exception as e:
            logger.error(f"❌ Ошибка инициализации стилей: {e}")
    
    def _add_theme_toggle(self) -> None:
        """Добавляет переключатель темы"""
        theme_toggle_html = """
        <div class="theme-toggle" onclick="toggleTheme()">
            <div class="theme-toggle__icon theme-toggle__icon--light">🌙</div>
            <div class="theme-toggle__icon theme-toggle__icon--dark">☀️</div>
        </div>
        
        <script>
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Уведомляем Streamlit о смене темы
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: newTheme
            }, '*');
        }
        
        // Восстанавливаем тему из localStorage
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        </script>
        """
        
        st.markdown(theme_toggle_html, unsafe_allow_html=True)
    
    def apply_card_styles(self, content: str, card_type: str = "default") -> str:
        """Применяет стили карточек к контенту"""
        card_classes = {
            "default": "dashboard-card",
            "metric": "metric-card",
            "gradient": "card card--gradient-header",
            "interactive": "card card--interactive",
            "compact": "card card--compact"
        }
        
        css_class = card_classes.get(card_type, "dashboard-card")
        
        return f'<div class="{css_class}">{content}</div>'
    
    def create_metric_card(self, value: str, label: str, delta: Optional[str] = None) -> str:
        """Создает карточку метрики с новыми стилями"""
        delta_html = ""
        if delta:
            delta_class = "metric-card__delta--positive" if not delta.startswith("-") else "metric-card__delta--negative"
            delta_html = f'<div class="metric-card__delta {delta_class}">{delta}</div>'
        
        return f"""
        <div class="metric-card">
            <div class="metric-card__value">{value}</div>
            <div class="metric-card__label">{label}</div>
            {delta_html}
        </div>
        """
    
    def create_stats_grid(self, stats: List[Dict[str, Any]], columns: int = 4) -> str:
        """Создает сетку статистических карточек"""
        grid_class = f"stats-grid"

        cards_html = ""
        for stat in stats:
            value = stat.get('value', '0')
            label = stat.get('label', 'Метрика')
            change = stat.get('change', None)

            change_html = ""
            if change:
                change_class = "stat-card__change--positive" if change > 0 else "stat-card__change--negative"
                change_html = f'<div class="stat-card__change {change_class}">{change:+.1f}%</div>'

            cards_html += f"""
            <div class="stat-card">
                <div class="stat-card__value">{value}</div>
                <div class="stat-card__label">{label}</div>
                {change_html}
            </div>
            """

        return f'<div class="{grid_class}">{cards_html}</div>'


    def create_dashboard_section(self, title: str, content: str, description: Optional[str] = None) -> str:
        """Создает секцию дашборда с заголовком"""
        description_html = ""
        if description:
            description_html = f'<p class="dashboard-section__description">{description}</p>'

        return f"""
        <div class="dashboard-section">
            <h2 class="dashboard-section__title">{title}</h2>
            {description_html}
            {content}
        </div>
        """

    def create_alert(self, message: str, alert_type: str = "info", title: Optional[str] = None) -> str:
        """Создает уведомление с новыми стилями"""
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "danger": "❌"
        }
        
        icon = icons.get(alert_type, "ℹ️")
        title_html = f'<div class="alert__title">{title}</div>' if title else ""
        
        return f"""
        <div class="alert alert--{alert_type}">
            <div class="alert__icon">{icon}</div>
            <div class="alert__content">
                {title_html}
                <div class="alert__description">{message}</div>
            </div>
        </div>
        """
    
    def create_loading_skeleton(self, skeleton_type: str = "card") -> str:
        """Создает скелетон загрузки"""
        skeletons = {
            "card": '<div class="loading-skeleton loading-skeleton--card"></div>',
            "text": '<div class="loading-skeleton loading-skeleton--text"></div>',
            "title": '<div class="loading-skeleton loading-skeleton--title"></div>'
        }
        
        return skeletons.get(skeleton_type, skeletons["card"])
    
    def create_empty_state(self, title: str, description: str, icon: str = "📊") -> str:
        """Создает состояние пустых данных"""
        return f"""
        <div class="empty-state">
            <div class="empty-state__icon">{icon}</div>
            <h3 class="empty-state__title">{title}</h3>
            <p class="empty-state__description">{description}</p>
        </div>
        """
    
    def wrap_with_grid(self, content: str, columns: int = 2) -> str:
        """Оборачивает контент в адаптивную сетку"""
        grid_class = f"dashboard-grid dashboard-grid--{columns}"
        return f'<div class="{grid_class}">{content}</div>'
    
    def get_utility_classes(self) -> Dict[str, str]:
        """Возвращает часто используемые утилитарные классы"""
        return {
            # Отступы
            "mb-3": "mb-3",  # margin-bottom: var(--spacing-md)
            "p-4": "p-4",    # padding: var(--spacing-lg)
            "px-3": "px-3",  # padding-left/right: var(--spacing-md)
            "py-2": "py-2",  # padding-top/bottom: var(--spacing-sm)
            
            # Flexbox
            "flex": "flex",
            "flex-col": "flex-col",
            "items-center": "items-center",
            "justify-between": "justify-between",
            
            # Grid
            "grid": "grid",
            "grid-cols-2": "grid-cols-2",
            "grid-cols-3": "grid-cols-3",
            "grid-cols-4": "grid-cols-4",
            "gap-3": "gap-3",
            
            # Цвета
            "text-primary": "text-primary",
            "text-secondary": "text-secondary",
            "text-muted": "text-muted",
            "bg-white": "bg-white",
            "bg-primary": "bg-primary",
            
            # Границы и скругления
            "border": "border",
            "rounded": "rounded",
            "rounded-lg": "rounded-lg",
            "shadow": "shadow",
            "shadow-lg": "shadow-lg",
            
            # Размеры
            "w-full": "w-full",
            "h-full": "h-full",
            
            # Адаптивность
            "md:grid-cols-2": "md:grid-cols-2",
            "sm:grid-cols-1": "sm:grid-cols-1"
        }


def with_styles(func):
    """Декоратор для автоматической инициализации стилей"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not hasattr(st.session_state, 'style_integrator'):
            st.session_state.style_integrator = StyleIntegrator()
        
        st.session_state.style_integrator.initialize()
        return func(*args, **kwargs)
    
    return wrapper


def apply_component_styles(component_type: str):
    """Декоратор для применения стилей к компонентам"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Загружаем стили для конкретного компонента
            css_manager.inject_component_styles(component_type)
            return func(*args, **kwargs)
        return wrapper
    return decorator


# Глобальный экземпляр интегратора
style_integrator = StyleIntegrator()


# Утилитарные функции для быстрого использования
def init_styles(theme: Optional[str] = None) -> None:
    """Инициализирует систему стилей"""
    if theme is None:
        theme = app_config.ui.theme
    
    style_integrator.theme = theme
    style_integrator.initialize()


def create_metric_card(value: str, label: str, delta: Optional[str] = None) -> None:
    """Создает и отображает карточку метрики"""
    card_html = style_integrator.create_metric_card(value, label, delta)
    st.markdown(card_html, unsafe_allow_html=True)


def create_dashboard_grid(content_list: List[str], columns: int = 2) -> None:
    """Создает и отображает сетку дашборда"""
    content = "".join(content_list)
    grid_html = style_integrator.wrap_with_grid(content, columns)
    st.markdown(grid_html, unsafe_allow_html=True)


def show_alert(message: str, alert_type: str = "info", title: Optional[str] = None) -> None:
    """Показывает уведомление используя нативные Streamlit компоненты"""
    # Combine title and message if title is provided
    full_message = f"**{title}**: {message}" if title else message

    if alert_type == "success":
        st.success(full_message)
    elif alert_type == "warning":
        st.warning(full_message)
    elif alert_type == "danger" or alert_type == "error":
        st.error(full_message)
    else:  # info or default
        st.info(full_message)


def show_loading_skeleton(skeleton_type: str = "card") -> None:
    """Показывает скелетон загрузки"""
    skeleton_html = style_integrator.create_loading_skeleton(skeleton_type)
    st.markdown(skeleton_html, unsafe_allow_html=True)


def show_empty_state(title: str, description: str, icon: str = "📊") -> None:
    """Показывает состояние пустых данных используя нативные Streamlit компоненты"""
    st.markdown(f"### {icon} {title}")
    st.info(description)
    st.markdown("---")


def create_stats_grid(stats: List[Dict[str, Any]], columns: int = 4) -> str:
    """Глобальная функция для создания сетки статистических карточек"""
    return style_integrator.create_stats_grid(stats, columns)
