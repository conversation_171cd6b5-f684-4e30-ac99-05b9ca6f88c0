"""
Enhanced Styling System
Provides modern CSS styles and design components for the dashboard
"""
import streamlit as st
from typing import Dict, Any, Optional


class EnhancedStyleManager:
    """Manager for enhanced dashboard styles"""
    
    def __init__(self):
        """Initialize enhanced style manager"""
        self.color_palette = {
            'primary': '#3b82f6',
            'primary_light': '#60a5fa',
            'primary_dark': '#1e40af',
            'success': '#10b981',
            'success_light': '#34d399',
            'warning': '#f59e0b',
            'warning_light': '#fbbf24',
            'danger': '#ef4444',
            'danger_light': '#f87171',
            'info': '#06b6d4',
            'info_light': '#22d3ee',
            'secondary': '#6b7280',
            'light': '#f8fafc',
            'dark': '#1e293b',
            'white': '#ffffff',
            'gray_50': '#f9fafb',
            'gray_100': '#f3f4f6',
            'gray_200': '#e5e7eb',
            'gray_300': '#d1d5db',
            'gray_400': '#9ca3af',
            'gray_500': '#6b7280',
            'gray_600': '#4b5563',
            'gray_700': '#374151',
            'gray_800': '#1f2937',
            'gray_900': '#111827'
        }
    
    def inject_enhanced_styles(self) -> None:
        """Inject enhanced CSS styles into the Streamlit app"""
        enhanced_css = f"""
        <style>
        /* Global Styles */
        .main .block-container {{
            padding-top: 2rem;
            padding-bottom: 2rem;
            max-width: 1200px;
        }}
        
        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 1rem;
        }}
        
        h1 {{ font-size: 2.5rem; color: {self.color_palette['gray_900']}; }}
        h2 {{ font-size: 2rem; color: {self.color_palette['gray_800']}; }}
        h3 {{ font-size: 1.5rem; color: {self.color_palette['gray_700']}; }}
        
        /* Enhanced Buttons */
        .stButton > button {{
            background: linear-gradient(135deg, {self.color_palette['primary']}, {self.color_palette['primary_dark']});
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }}
        
        .stButton > button:hover {{
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }}
        
        /* Enhanced Metrics */
        [data-testid="metric-container"] {{
            background: white;
            border: 1px solid {self.color_palette['gray_200']};
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }}
        
        [data-testid="metric-container"]:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }}
        
        /* Enhanced Sidebar */
        .css-1d391kg {{
            background: linear-gradient(180deg, {self.color_palette['gray_50']}, white);
            border-right: 1px solid {self.color_palette['gray_200']};
        }}
        
        /* Enhanced Tables */
        .stDataFrame {{
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}
        
        /* Enhanced Selectbox */
        .stSelectbox > div > div {{
            border-radius: 8px;
            border: 1px solid {self.color_palette['gray_300']};
        }}
        
        /* Enhanced Text Input */
        .stTextInput > div > div > input {{
            border-radius: 8px;
            border: 1px solid {self.color_palette['gray_300']};
            padding: 0.75rem;
        }}
        
        /* Enhanced Checkbox */
        .stCheckbox > label {{
            font-weight: 500;
            color: {self.color_palette['gray_700']};
        }}
        
        /* Custom Alert Styles */
        .alert {{
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid;
            font-weight: 500;
        }}
        
        .alert-success {{
            background-color: #ecfdf5;
            border-left-color: {self.color_palette['success']};
            color: #065f46;
        }}
        
        .alert-warning {{
            background-color: #fffbeb;
            border-left-color: {self.color_palette['warning']};
            color: #92400e;
        }}
        
        .alert-danger {{
            background-color: #fef2f2;
            border-left-color: {self.color_palette['danger']};
            color: #991b1b;
        }}
        
        .alert-info {{
            background-color: #f0f9ff;
            border-left-color: {self.color_palette['info']};
            color: #0c4a6e;
        }}
        
        /* Loading Animation */
        .loading-spinner {{
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid {self.color_palette['gray_200']};
            border-radius: 50%;
            border-top-color: {self.color_palette['primary']};
            animation: spin 1s ease-in-out infinite;
        }}
        
        @keyframes spin {{
            to {{ transform: rotate(360deg); }}
        }}
        
        /* Enhanced Cards */
        .dashboard-card {{
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid {self.color_palette['gray_100']};
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }}
        
        .dashboard-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }}
        
        /* Status Badges */
        .status-badge {{
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
        }}
        
        .status-active {{
            background-color: #dcfce7;
            color: #166534;
        }}
        
        .status-completed {{
            background-color: #dbeafe;
            color: #1e40af;
        }}
        
        .status-overdue {{
            background-color: #fee2e2;
            color: #991b1b;
        }}
        
        .status-pending {{
            background-color: #fef3c7;
            color: #92400e;
        }}
        
        /* Responsive Design */
        @media (max-width: 768px) {{
            .main .block-container {{
                padding-left: 1rem;
                padding-right: 1rem;
            }}
            
            h1 {{ font-size: 2rem; }}
            h2 {{ font-size: 1.5rem; }}
            h3 {{ font-size: 1.25rem; }}
        }}
        
        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {{
            .dashboard-card {{
                background: {self.color_palette['gray_800']};
                border-color: {self.color_palette['gray_700']};
                color: white;
            }}
        }}
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {{
            width: 8px;
        }}
        
        ::-webkit-scrollbar-track {{
            background: {self.color_palette['gray_100']};
            border-radius: 4px;
        }}
        
        ::-webkit-scrollbar-thumb {{
            background: {self.color_palette['gray_400']};
            border-radius: 4px;
        }}
        
        ::-webkit-scrollbar-thumb:hover {{
            background: {self.color_palette['gray_500']};
        }}
        
        /* Animation Classes */
        .fade-in {{
            animation: fadeIn 0.5s ease-in;
        }}
        
        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(10px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        .slide-in {{
            animation: slideIn 0.3s ease-out;
        }}
        
        @keyframes slideIn {{
            from {{ transform: translateX(-10px); opacity: 0; }}
            to {{ transform: translateX(0); opacity: 1; }}
        }}
        </style>
        """
        
        st.markdown(enhanced_css, unsafe_allow_html=True)
    
    def create_status_badge(self, status: str, text: Optional[str] = None) -> str:
        """Create a status badge HTML"""
        status_classes = {
            'active': 'status-active',
            'completed': 'status-completed',
            'overdue': 'status-overdue',
            'pending': 'status-pending'
        }
        
        css_class = status_classes.get(status.lower(), 'status-pending')
        display_text = text or status.title()
        
        return f'<span class="status-badge {css_class}">{display_text}</span>'
    
    def create_alert(self, message: str, alert_type: str = 'info', title: Optional[str] = None) -> str:
        """Create an alert HTML"""
        title_html = f'<strong>{title}</strong><br>' if title else ''
        
        return f'''
        <div class="alert alert-{alert_type}">
            {title_html}{message}
        </div>
        '''
    
    def create_loading_spinner(self, text: str = "Загрузка...") -> str:
        """Create a loading spinner HTML"""
        return f'''
        <div style="display: flex; align-items: center; gap: 8px; justify-content: center; padding: 2rem;">
            <div class="loading-spinner"></div>
            <span>{text}</span>
        </div>
        '''
