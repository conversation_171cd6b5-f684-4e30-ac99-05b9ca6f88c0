"""
Компоненты для отображения метрик
"""
import streamlit as st
from typing import Dict, Any


def render_metric_card(title: str, value: str, icon: str, delta: str = "", color: str = "blue") -> None:
    """Отрисовывает карточку метрики"""
    gradient_map = {
        "blue": "linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%)",
        "green": "linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)",
        "orange": "linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%)",
        "red": "linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%)"
    }
    
    gradient = gradient_map.get(color, gradient_map["blue"])
    
    st.markdown(f"""
    <div style="
        background: {gradient};
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        border: 1px solid #3b82f6;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
        margin-bottom: 1rem;
    ">
        <div style="font-size: 30px; margin-bottom: 10px; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));">{icon}</div>
        <div style="font-size: 24px; font-weight: bold; color: white; margin-bottom: 5px; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">{value}</div>
        <div style="color: rgba(255,255,255,0.9); font-size: 14px; font-weight: 500;">{title}</div>
        {f'<div style="color: rgba(255,255,255,0.7); font-size: 12px; margin-top: 5px;">{delta}</div>' if delta else ''}
    </div>
    """, unsafe_allow_html=True)


def render_key_metrics_row(summary: Dict[str, Any]) -> None:
    """Отрисовывает строку ключевых метрик"""
    col1, col2, col3, col4 = st.columns(4)
    
    total_contracts = int(summary.get('total_contracts', 0))
    active_contracts = int(summary.get('active_contracts', 0))
    total_amount = summary.get('total_amount', 0)
    overdue_count = int(summary.get('overdue_contracts', 0))
    
    # Вычисляем проценты
    active_percentage = (active_contracts / total_contracts * 100) if total_contracts > 0 else 0
    avg_amount = summary.get('average_amount', 0)
    overdue_rate = summary.get('overdue_rate', 0)
    
    with col1:
        render_metric_card(
            "Всего договоров",
            f"{total_contracts:,}",
            "📋",
            "В системе",
            "blue"
        )
    
    with col2:
        render_metric_card(
            "Активные договоры",
            f"{active_contracts:,}",
            "✅",
            f"{active_percentage:.1f}% от общего числа",
            "green"
        )
    
    with col3:
        render_metric_card(
            "Общая сумма",
            f"{total_amount:,.0f} ₽",
            "💰",
            f"Средняя: {avg_amount:,.0f} ₽",
            "orange"
        )
    
    with col4:
        render_metric_card(
            "Просроченные",
            f"{overdue_count:,}",
            "⚠️",
            f"{overdue_rate:.1f}% от активных",
            "red"
        )


def render_secondary_metrics_row(summary: Dict[str, Any]) -> None:
    """Отрисовывает вторую строку метрик"""
    col5, col6, col7, col8 = st.columns(4)
    
    total_contracts = int(summary.get('total_contracts', 0))
    completed_contracts = int(summary.get('completed_contracts', 0))
    suspended_count = int(summary.get('suspended_contracts', 0))
    avg_duration = summary.get('average_duration', 0)
    overdue_rate = summary.get('overdue_rate', 0)
    
    # Вычисляем проценты
    completion_rate = (completed_contracts / total_contracts * 100) if total_contracts > 0 else 0
    suspended_rate = (suspended_count / total_contracts * 100) if total_contracts > 0 else 0
    efficiency = 100 - overdue_rate if overdue_rate > 0 else 100
    
    with col5:
        render_metric_card(
            "Завершенные",
            f"{completed_contracts:,}",
            "✓",
            f"{completion_rate:.1f}% завершено",
            "blue"
        )
    
    with col6:
        render_metric_card(
            "Приостановленные",
            f"{suspended_count:,}",
            "⏸️",
            f"{suspended_rate:.1f}% приостановлено",
            "orange"
        )
    
    with col7:
        render_metric_card(
            "Средний срок",
            f"{avg_duration:.0f}",
            "📅",
            "дней выполнения",
            "green"
        )
    
    with col8:
        # Определяем цвет в зависимости от эффективности
        efficiency_color = "blue" if efficiency > 90 else "orange" if efficiency > 70 else "red"
        render_metric_card(
            "Эффективность",
            f"{efficiency:.1f}%",
            "🎯",
            "без просрочек",
            efficiency_color
        )


def render_progress_indicator(current: float, total: float, title: str, color: str = "blue") -> None:
    """Отрисовывает индикатор прогресса"""
    percentage = (current / total * 100) if total > 0 else 0
    
    st.markdown(f"""
    <div style="
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        border: 2px solid #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        margin: 1rem 0;
    ">
        <h4 style="color: #1e40af; margin-bottom: 1rem;">{title}</h4>
        <div style="
            background: #f1f5f9;
            border-radius: 10px;
            height: 20px;
            position: relative;
            overflow: hidden;
        ">
            <div style="
                background: linear-gradient(90deg, #3b82f6, #1e40af);
                height: 100%;
                width: {percentage}%;
                border-radius: 10px;
                transition: width 0.5s ease;
                position: relative;
            ">
                <div style="
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                    animation: progress-shine 2s infinite;
                "></div>
            </div>
        </div>
        <div style="
            display: flex;
            justify-content: space-between;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #64748b;
        ">
            <span>{current:,.0f}</span>
            <span>{percentage:.1f}%</span>
            <span>{total:,.0f}</span>
        </div>
    </div>
    """, unsafe_allow_html=True)
