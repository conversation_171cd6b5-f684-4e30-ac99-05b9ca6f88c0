"""
Enhanced Dashboard Components
Provides modern, interactive dashboard widgets with improved visual design
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import numpy as np

logger = logging.getLogger(__name__)


class EnhancedDashboardComponents:
    """Enhanced dashboard components with modern UI and interactive features"""
    
    def __init__(self):
        """Initialize enhanced dashboard components"""
        self.color_palette = {
            'primary': '#3b82f6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'info': '#06b6d4',
            'secondary': '#6b7280'
        }

    def _apply_theme_to_figure(self, fig, title: str = "") -> None:
        """Применяет текущую тему к графику Plotly"""
        current_theme = st.session_state.get('theme', 'light')

        if current_theme == 'dark':
            # Темная тема для графиков
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'family': 'Arial, sans-serif', 'color': '#fafafa'}
                },
                paper_bgcolor='#1e2130',
                plot_bgcolor='#1e2130',
                font={'color': '#fafafa'},
                legend={'font': {'color': '#fafafa'}},
                xaxis={
                    'gridcolor': '#404040',
                    'linecolor': '#404040',
                    'tickcolor': '#404040',
                    'tickfont': {'color': '#fafafa'},
                    'titlefont': {'color': '#fafafa'}
                },
                yaxis={
                    'gridcolor': '#404040',
                    'linecolor': '#404040',
                    'tickcolor': '#404040',
                    'tickfont': {'color': '#fafafa'},
                    'titlefont': {'color': '#fafafa'}
                }
            )
        else:
            # Светлая тема для графиков
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'family': 'Arial, sans-serif', 'color': '#262730'}
                },
                paper_bgcolor='#ffffff',
                plot_bgcolor='#ffffff',
                font={'color': '#262730'},
                legend={'font': {'color': '#262730'}},
                xaxis={
                    'gridcolor': '#e1e5e9',
                    'linecolor': '#e1e5e9',
                    'tickcolor': '#e1e5e9',
                    'tickfont': {'color': '#262730'},
                    'titlefont': {'color': '#262730'}
                },
                yaxis={
                    'gridcolor': '#e1e5e9',
                    'linecolor': '#e1e5e9',
                    'tickcolor': '#e1e5e9',
                    'tickfont': {'color': '#262730'},
                    'titlefont': {'color': '#262730'}
                }
            )
    
    def render_enhanced_kpi_cards(self, summary_data: Dict[str, Any]) -> None:
        """Render enhanced KPI cards using native Streamlit metrics"""
        try:
            st.markdown("## 📊 Ключевые показатели эффективности")

            # Create responsive columns
            col1, col2, col3, col4 = st.columns(4)

            # Calculate values
            total_contracts = int(summary_data.get('total_contracts', 0))
            active_contracts = int(summary_data.get('active_contracts', 0))
            total_amount = summary_data.get('total_amount', 0)
            overdue_count = int(summary_data.get('overdue_contracts', 0))
            overdue_rate = summary_data.get('overdue_rate', 0)
            completed_contracts = int(summary_data.get('completed_contracts', 0))
            avg_amount = summary_data.get('average_amount', 0)

            # Calculate percentages
            active_percentage = (active_contracts / total_contracts * 100) if total_contracts > 0 else 0
            completion_rate = (completed_contracts / total_contracts * 100) if total_contracts > 0 else 0
            efficiency = 100 - overdue_rate if overdue_rate > 0 else 100

            with col1:
                st.metric(
                    label="📋 Всего договоров",
                    value=total_contracts,
                    delta=self._get_trend_delta(summary_data, 'total_contracts')
                )

            with col2:
                st.metric(
                    label="✅ Активные договоры",
                    value=active_contracts,
                    delta=f"{active_percentage:.1f}% от общего числа",
                    help="Процент активных договоров от общего количества"
                )

            with col3:
                st.metric(
                    label="💰 Общая сумма",
                    value=f"{total_amount:,.0f} ₽",
                    delta=self._get_trend_delta(summary_data, 'total_amount')
                )

            with col4:
                st.metric(
                    label="⚠️ Просроченные",
                    value=overdue_count,
                    delta=f"{overdue_rate:.1f}% просрочка",
                    delta_color="inverse"  # Red for high values
                )

            # Second row with additional metrics
            st.markdown("---")
            col5, col6, col7, col8 = st.columns(4)

            with col5:
                st.metric(
                    label="🎯 Завершенные",
                    value=completed_contracts,
                    delta=f"{completion_rate:.1f}% завершено"
                )

            with col6:
                st.metric(
                    label="📊 Средняя сумма",
                    value=f"{avg_amount:,.0f} ₽",
                    help="Средняя сумма договора"
                )

            with col7:
                st.metric(
                    label="⚡ Эффективность",
                    value=f"{efficiency:.1f}%",
                    delta="Высокая" if efficiency > 80 else "Требует внимания",
                    delta_color="normal" if efficiency > 80 else "inverse"
                )

            with col8:
                # Estimated completion time (mock calculation)
                avg_duration = 45  # This should be calculated from actual data
                st.metric(
                    label="⏱️ Средний срок",
                    value=f"{avg_duration} дней",
                    help="Средний срок выполнения договора"
                )

        except Exception as e:
            logger.error(f"Error rendering enhanced KPI cards: {e}")
            st.error("Ошибка отображения KPI карточек")
    
    def _get_trend_delta(self, data: Dict[str, Any], metric: str) -> Optional[str]:
        """Get trend delta for a metric (mock implementation)"""
        # This would normally compare with historical data
        # For now, return a mock delta for demonstration
        import random

        # Mock trend calculation - in real implementation, compare with previous period
        trend_value = random.randint(-10, 15)
        if trend_value > 0:
            return f"+{trend_value}"
        elif trend_value < 0:
            return str(trend_value)
        else:
            return None



    def render_interactive_charts(self, contracts_df: pd.DataFrame) -> None:
        """Render interactive charts with enhanced visualizations"""
        try:
            st.markdown("## 📈 Интерактивная аналитика")

            if contracts_df.empty:
                st.warning("Нет данных для отображения графиков")
                return

            # Create tabs for different chart types
            tab1, tab2, tab3, tab4 = st.tabs(["📊 Обзор", "📈 Тренды", "🎯 Производительность", "💰 Финансы"])

            with tab1:
                self._render_overview_charts(contracts_df)

            with tab2:
                self._render_trend_charts(contracts_df)

            with tab3:
                self._render_performance_charts(contracts_df)

            with tab4:
                self._render_financial_charts(contracts_df)

        except Exception as e:
            logger.error(f"Error rendering interactive charts: {e}")
            st.error("Ошибка отображения интерактивных графиков")

    def _render_overview_charts(self, df: pd.DataFrame) -> None:
        """Render overview charts"""
        col1, col2 = st.columns(2)

        with col1:
            # Enhanced status distribution
            if 'status' in df.columns:
                status_counts = df['status'].value_counts()

                fig = go.Figure(data=[go.Pie(
                    labels=status_counts.index,
                    values=status_counts.values,
                    hole=0.4,
                    textinfo='label+percent',
                    textposition='outside',
                    marker=dict(
                        colors=['#10b981', '#3b82f6', '#f59e0b', '#ef4444'],
                        line=dict(color='#FFFFFF', width=2)
                    )
                )])

                # Применяем тему к графику
                self._apply_theme_to_figure(fig, "Распределение по статусам")

                fig.update_layout(
                    height=400,
                    showlegend=True,
                    legend=dict(orientation="v", yanchor="middle", y=0.5, xanchor="left", x=1.05)
                )

                st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Contract types distribution
            if 'contract_type' in df.columns:
                type_counts = df['contract_type'].value_counts().head(8)

                fig = go.Figure(data=[go.Bar(
                    x=type_counts.values,
                    y=type_counts.index,
                    orientation='h',
                    marker=dict(
                        color=type_counts.values,
                        colorscale='Blues',
                        showscale=True,
                        colorbar=dict(title="Количество")
                    ),
                    text=type_counts.values,
                    textposition='auto'
                )])

                # Применяем тему к графику
                self._apply_theme_to_figure(fig, "Топ-8 типов договоров")

                fig.update_layout(
                    height=400,
                    yaxis={'categoryorder': 'total ascending'},
                    xaxis_title="Количество договоров",
                    yaxis_title="Тип договора"
                )

                st.plotly_chart(fig, use_container_width=True)

    def _render_trend_charts(self, df: pd.DataFrame) -> None:
        """Render trend analysis charts"""
        if 'start_date' not in df.columns:
            st.warning("Нет данных о датах для анализа трендов")
            return

        # Time series analysis
        df_time = df.copy()
        df_time['start_date'] = pd.to_datetime(df_time['start_date'])
        df_time['month'] = df_time['start_date'].dt.to_period('M')

        monthly_data = df_time.groupby('month').agg({
            'contract_id': 'count',
            'total_amount': 'sum'
        }).reset_index()

        monthly_data['month'] = monthly_data['month'].astype(str)

        # Create subplot with secondary y-axis
        fig = make_subplots(
            rows=1, cols=1,
            specs=[[{"secondary_y": True}]],
            subplot_titles=["Динамика заключения договоров"]
        )

        # Add contract count
        fig.add_trace(
            go.Scatter(
                x=monthly_data['month'],
                y=monthly_data['contract_id'],
                mode='lines+markers',
                name='Количество договоров',
                line=dict(color='#3b82f6', width=3),
                marker=dict(size=8)
            ),
            secondary_y=False
        )

        # Add total amount
        fig.add_trace(
            go.Scatter(
                x=monthly_data['month'],
                y=monthly_data['total_amount'],
                mode='lines+markers',
                name='Общая сумма (₽)',
                line=dict(color='#10b981', width=3),
                marker=dict(size=8)
            ),
            secondary_y=True
        )

        fig.update_xaxes(title_text="Месяц")
        fig.update_yaxes(title_text="Количество договоров", secondary_y=False)
        fig.update_yaxes(title_text="Сумма (₽)", secondary_y=True)

        # Применяем тему к графику
        self._apply_theme_to_figure(fig, "Динамика договоров и сумм по месяцам")

        fig.update_layout(
            height=500,
            hovermode='x unified',
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )

        st.plotly_chart(fig, use_container_width=True)

    def _render_performance_charts(self, df: pd.DataFrame) -> None:
        """Render performance analysis charts"""
        col1, col2 = st.columns(2)

        with col1:
            # Completion rate by contract type
            if 'contract_type' in df.columns and 'status' in df.columns:
                performance_data = df.groupby('contract_type')['status'].apply(
                    lambda x: (x == 'completed').sum() / len(x) * 100
                ).reset_index()
                performance_data.columns = ['contract_type', 'completion_rate']
                performance_data = performance_data.sort_values('completion_rate', ascending=True)

                fig = go.Figure(data=[go.Bar(
                    x=performance_data['completion_rate'],
                    y=performance_data['contract_type'],
                    orientation='h',
                    marker=dict(
                        color=performance_data['completion_rate'],
                        colorscale='RdYlGn',
                        showscale=True,
                        colorbar=dict(title="Процент завершения")
                    ),
                    text=[f"{x:.1f}%" for x in performance_data['completion_rate']],
                    textposition='auto'
                )])

                # Применяем тему к графику
                self._apply_theme_to_figure(fig, "Процент завершения по типам договоров")

                fig.update_layout(
                    height=400,
                    xaxis_title="Процент завершения (%)",
                    yaxis_title="Тип договора"
                )

                st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Average contract duration (mock data)
            if 'start_date' in df.columns and 'end_date' in df.columns:
                df_duration = df.copy()
                df_duration['start_date'] = pd.to_datetime(df_duration['start_date'])
                df_duration['end_date'] = pd.to_datetime(df_duration['end_date'])
                df_duration['duration'] = (df_duration['end_date'] - df_duration['start_date']).dt.days

                avg_duration = df_duration.groupby('contract_type')['duration'].mean().reset_index()
                avg_duration = avg_duration.sort_values('duration', ascending=False)

                fig = go.Figure(data=[go.Scatter(
                    x=avg_duration['contract_type'],
                    y=avg_duration['duration'],
                    mode='markers+lines',
                    marker=dict(
                        size=12,
                        color=avg_duration['duration'],
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="Дни")
                    ),
                    line=dict(color='#3b82f6', width=2)
                )])

                # Применяем тему к графику
                self._apply_theme_to_figure(fig, "Средняя продолжительность по типам")

                fig.update_layout(
                    height=400,
                    xaxis_title="Тип договора",
                    yaxis_title="Средняя продолжительность (дни)",
                    xaxis={'tickangle': 45}
                )

                st.plotly_chart(fig, use_container_width=True)

    def _render_financial_charts(self, df: pd.DataFrame) -> None:
        """Render financial analysis charts"""
        if 'total_amount' not in df.columns:
            st.warning("Нет финансовых данных для анализа")
            return

        col1, col2 = st.columns(2)

        with col1:
            # Amount distribution by status
            if 'status' in df.columns:
                amount_by_status = df.groupby('status')['total_amount'].sum().reset_index()

                fig = go.Figure(data=[go.Pie(
                    labels=amount_by_status['status'],
                    values=amount_by_status['total_amount'],
                    hole=0.3,
                    textinfo='label+percent+value',
                    texttemplate='%{label}<br>%{percent}<br>%{value:,.0f} ₽',
                    marker=dict(
                        colors=['#10b981', '#3b82f6', '#f59e0b', '#ef4444'],
                        line=dict(color='#FFFFFF', width=2)
                    )
                )])

                # Применяем тему к графику
                self._apply_theme_to_figure(fig, "Распределение сумм по статусам")

                fig.update_layout(
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Top contracts by amount
            top_contracts = df.nlargest(10, 'total_amount')

            fig = go.Figure(data=[go.Bar(
                x=top_contracts['total_amount'],
                y=top_contracts['contract_name'].str[:30] + '...',
                orientation='h',
                marker=dict(
                    color='#3b82f6',
                    opacity=0.8
                ),
                text=[f"{x:,.0f} ₽" for x in top_contracts['total_amount']],
                textposition='auto'
            )])

            # Применяем тему к графику
            self._apply_theme_to_figure(fig, "Топ-10 договоров по сумме")

            fig.update_layout(
                height=400,
                xaxis_title="Сумма (₽)",
                yaxis_title="Договор",
                yaxis={'categoryorder': 'total ascending'}
            )

            st.plotly_chart(fig, use_container_width=True)

    def render_quick_actions_panel(self) -> None:
        """Render quick actions panel with shortcuts"""
        try:
            st.markdown("## ⚡ Быстрые действия")

            # Create action buttons in columns
            col1, col2, col3, col4, col5 = st.columns(5)

            with col1:
                if st.button("🔄 Обновить данные", key="enhanced_refresh_data", help="Обновить все данные дашборда"):
                    st.rerun()

            with col2:
                if st.button("📊 Экспорт отчета", key="export_report", help="Экспортировать текущий отчет"):
                    self._export_dashboard_report()

            with col3:
                if st.button("📈 Анализ трендов", key="trend_analysis", help="Открыть детальный анализ трендов"):
                    st.session_state.show_trend_analysis = True

            with col4:
                if st.button("⚠️ Проблемные договоры", key="problem_contracts", help="Показать договоры с проблемами"):
                    st.session_state.show_problem_contracts = True

            with col5:
                if st.button("➕ Новый договор", key="new_contract", help="Добавить новый договор"):
                    st.session_state.show_new_contract_form = True

            # Action panels based on button clicks
            if st.session_state.get('show_trend_analysis', False):
                self._render_trend_analysis_panel()

            if st.session_state.get('show_problem_contracts', False):
                self._render_problem_contracts_panel()

            if st.session_state.get('show_new_contract_form', False):
                self._render_new_contract_form()

        except Exception as e:
            logger.error(f"Error rendering quick actions panel: {e}")
            st.error("Ошибка отображения панели быстрых действий")

    def _export_dashboard_report(self) -> None:
        """Export dashboard report"""
        try:
            # Create a simple report
            report_data = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_contracts': st.session_state.get('total_contracts', 0),
                'active_contracts': st.session_state.get('active_contracts', 0),
                'overdue_rate': st.session_state.get('overdue_rate', 0)
            }

            report_text = f"""
            ОТЧЕТ ДАШБОРДА ДОГОВОРОВ
            Дата создания: {report_data['timestamp']}

            ОСНОВНЫЕ ПОКАЗАТЕЛИ:
            - Всего договоров: {report_data['total_contracts']}
            - Активные договоры: {report_data['active_contracts']}
            - Процент просрочки: {report_data['overdue_rate']:.1f}%

            Отчет создан автоматически системой дашборда.
            """

            st.download_button(
                label="📥 Скачать отчет",
                data=report_text,
                file_name=f"dashboard_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )

            st.success("Отчет готов к скачиванию!")

        except Exception as e:
            logger.error(f"Error exporting report: {e}")
            st.error("Ошибка создания отчета")

    def _render_trend_analysis_panel(self) -> None:
        """Render detailed trend analysis panel"""
        with st.expander("📈 Детальный анализ трендов", expanded=True):
            st.markdown("### Анализ трендов по ключевым показателям")

            # Mock trend data
            trend_data = {
                'Заключение договоров': {'trend': 'up', 'change': '+15%', 'period': 'за месяц'},
                'Средняя сумма': {'trend': 'down', 'change': '-5%', 'period': 'за квартал'},
                'Процент завершения': {'trend': 'up', 'change': '+8%', 'period': 'за месяц'},
                'Просрочки': {'trend': 'down', 'change': '-12%', 'period': 'за месяц'}
            }

            for metric, data in trend_data.items():
                trend_icon = '📈' if data['trend'] == 'up' else '📉'
                trend_color = 'green' if data['trend'] == 'up' else 'red'

                st.markdown(f"""
                <div style="padding: 10px; border-left: 4px solid {trend_color}; margin: 5px 0; background-color: #f8f9fa;">
                    <strong>{trend_icon} {metric}</strong><br>
                    Изменение: <span style="color: {trend_color};">{data['change']}</span> {data['period']}
                </div>
                """, unsafe_allow_html=True)

            if st.button("❌ Закрыть анализ трендов", key="close_trend_analysis"):
                st.session_state.show_trend_analysis = False
                st.rerun()

    def _render_problem_contracts_panel(self) -> None:
        """Render problem contracts panel"""
        with st.expander("⚠️ Проблемные договоры", expanded=True):
            st.markdown("### Договоры, требующие внимания")

            # Mock problem contracts data
            problem_contracts = [
                {'name': 'Договор №123', 'issue': 'Просрочен на 15 дней', 'severity': 'high'},
                {'name': 'Договор №456', 'issue': 'Низкий процент выполнения', 'severity': 'medium'},
                {'name': 'Договор №789', 'issue': 'Отсутствует отчетность', 'severity': 'low'}
            ]

            for contract in problem_contracts:
                severity_colors = {'high': '#ef4444', 'medium': '#f59e0b', 'low': '#6b7280'}
                severity_icons = {'high': '🔴', 'medium': '🟡', 'low': '⚪'}

                st.markdown(f"""
                <div style="padding: 10px; border: 1px solid {severity_colors[contract['severity']]};
                           border-radius: 5px; margin: 5px 0; background-color: #f8f9fa;">
                    <strong>{severity_icons[contract['severity']]} {contract['name']}</strong><br>
                    Проблема: {contract['issue']}
                </div>
                """, unsafe_allow_html=True)

            if st.button("❌ Закрыть список проблем", key="close_problem_contracts"):
                st.session_state.show_problem_contracts = False
                st.rerun()

    def _render_new_contract_form(self) -> None:
        """Render new contract form"""
        with st.expander("➕ Добавление нового договора", expanded=True):
            st.markdown("### Форма создания договора")

            with st.form("new_contract_form"):
                col1, col2 = st.columns(2)

                with col1:
                    contract_name = st.text_input("Название договора")
                    contract_type = st.selectbox("Тип договора",
                                               ["Поставка", "Услуги", "Работы", "Аренда"])
                    start_date = st.date_input("Дата начала")

                with col2:
                    contractor_name = st.text_input("Контрагент")
                    total_amount = st.number_input("Сумма договора", min_value=0.0)
                    end_date = st.date_input("Дата окончания")

                submitted = st.form_submit_button("✅ Создать договор")

                if submitted:
                    if contract_name and contractor_name and total_amount > 0:
                        st.success(f"Договор '{contract_name}' успешно создан!")
                        st.session_state.show_new_contract_form = False
                        st.rerun()
                    else:
                        st.error("Пожалуйста, заполните все обязательные поля")

            if st.button("❌ Отменить создание", key="cancel_new_contract"):
                st.session_state.show_new_contract_form = False
                st.rerun()

    def render_real_time_updates(self) -> None:
        """Render real-time data updates section"""
        try:
            st.markdown("## 🔄 Обновления в реальном времени")

            # Auto-refresh toggle
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                auto_refresh = st.checkbox("Автообновление данных", value=False, key="enhanced_auto_refresh")

            with col2:
                refresh_interval = st.selectbox(
                    "Интервал (сек)",
                    options=[30, 60, 300, 600],
                    index=2,
                    key="refresh_interval"
                )

            with col3:
                if st.button("🔄 Обновить сейчас", key="manual_refresh"):
                    st.rerun()

            # Show last update time
            last_update = datetime.now().strftime("%H:%M:%S")
            st.markdown(f"**Последнее обновление:** {last_update}")

            # Real-time metrics (mock data)
            if auto_refresh:
                # This would normally trigger automatic refresh
                st.markdown("🟢 **Автообновление активно**")

                # Placeholder for real-time metrics
                placeholder = st.empty()

                with placeholder.container():
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("Активные сессии", "12", "↑2")

                    with col2:
                        st.metric("Новые договоры", "3", "↑1")

                    with col3:
                        st.metric("Обновления", "8", "↑3")

                    with col4:
                        st.metric("Уведомления", "5", "↓1")
            else:
                st.markdown("⚪ **Автообновление отключено**")

        except Exception as e:
            logger.error(f"Error rendering real-time updates: {e}")
            st.error("Ошибка отображения обновлений в реальном времени")

    def render_insights_panel(self, contracts_df: pd.DataFrame) -> None:
        """Render AI-powered insights panel"""
        try:
            st.markdown("## 🧠 Умные инсайты")

            if contracts_df.empty:
                st.warning("Недостаточно данных для анализа")
                return

            # Generate insights based on data
            insights = self._generate_insights(contracts_df)

            # Display insights in cards
            for i, insight in enumerate(insights):
                self._render_insight_card(insight, i)

        except Exception as e:
            logger.error(f"Error rendering insights panel: {e}")
            st.error("Ошибка отображения панели инсайтов")

    def _generate_insights(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate insights from contract data"""
        insights = []

        try:
            # Insight 1: Overdue rate analysis
            if 'status' in df.columns:
                overdue_count = len(df[df['status'] == 'overdue'])
                total_count = len(df)
                overdue_rate = (overdue_count / total_count * 100) if total_count > 0 else 0

                if overdue_rate > 15:
                    insights.append({
                        'type': 'warning',
                        'title': 'Высокий уровень просрочек',
                        'description': f'Процент просроченных договоров составляет {overdue_rate:.1f}%, что превышает норму в 15%',
                        'recommendation': 'Рекомендуется усилить контроль за исполнением договоров',
                        'icon': '⚠️'
                    })
                elif overdue_rate < 5:
                    insights.append({
                        'type': 'success',
                        'title': 'Отличная дисциплина исполнения',
                        'description': f'Процент просрочек всего {overdue_rate:.1f}% - это отличный результат',
                        'recommendation': 'Продолжайте поддерживать текущий уровень контроля',
                        'icon': '✅'
                    })

            # Insight 2: Contract amount analysis
            if 'total_amount' in df.columns:
                avg_amount = df['total_amount'].mean()
                median_amount = df['total_amount'].median()

                if avg_amount > median_amount * 1.5:
                    insights.append({
                        'type': 'info',
                        'title': 'Неравномерное распределение сумм',
                        'description': f'Средняя сумма ({avg_amount:,.0f} ₽) значительно превышает медианную ({median_amount:,.0f} ₽)',
                        'recommendation': 'Возможно, стоит пересмотреть стратегию работы с крупными договорами',
                        'icon': '📊'
                    })

            # Insight 3: Seasonal patterns (mock)
            insights.append({
                'type': 'info',
                'title': 'Сезонные тренды',
                'description': 'Наблюдается увеличение активности заключения договоров в конце квартала',
                'recommendation': 'Планируйте ресурсы с учетом сезонности',
                'icon': '📈'
            })

        except Exception as e:
            logger.error(f"Error generating insights: {e}")

        return insights

    def _render_insight_card(self, insight: Dict[str, Any], index: int) -> None:
        """Render individual insight card using native Streamlit components"""
        # Use native Streamlit alert components based on insight type
        title_with_icon = f"{insight['icon']} **{insight['title']}**"
        full_message = f"{title_with_icon}\n\n{insight['description']}\n\n💡 **Рекомендация:** {insight['recommendation']}"

        if insight['type'] == 'success':
            st.success(full_message)
        elif insight['type'] == 'warning':
            st.warning(full_message)
        elif insight['type'] == 'danger':
            st.error(full_message)
        else:  # info or default
            st.info(full_message)
