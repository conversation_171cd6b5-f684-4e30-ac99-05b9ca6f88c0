"""
Компонент расширенных фильтров
Отвечает за дополнительные фильтры в основной области
"""
import streamlit as st
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class FiltersComponent:
    """Компонент расширенных фильтров"""
    
    def __init__(self):
        """Инициализирует компонент фильтров"""
        pass
    
    def render(self, current_filters: Dict[str, Any]) -> None:
        """Отрисовывает расширенные фильтры"""
        try:
            st.markdown("### 🔍 Расширенные фильтры")
            
            # Показываем активные фильтры
            self._show_active_filters(current_filters)
            
            # Дополнительные опции фильтрации
            self._render_additional_filters()
            
            logger.debug("Расширенные фильтры отрисованы")
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки расширенных фильтров: {e}")
    
    def _show_active_filters(self, filters: Dict[str, Any]) -> None:
        """Показывает активные фильтры"""
        active_filters = []
        
        if filters.get('status') and filters['status'] != 'Все':
            active_filters.append(f"Статус: {filters['status']}")
        
        if filters.get('contract_type') and filters['contract_type'] != 'Все':
            active_filters.append(f"Тип: {filters['contract_type']}")
        
        if filters.get('department') and filters['department'] != 'Все':
            active_filters.append(f"Департамент: {filters['department']}")
        
        if filters.get('start_date'):
            active_filters.append(f"От: {filters['start_date']}")
        
        if filters.get('end_date'):
            active_filters.append(f"До: {filters['end_date']}")
        
        if active_filters:
            st.info(f"Активные фильтры: {', '.join(active_filters)}")
        else:
            st.info("Фильтры не применены")
    
    def _render_additional_filters(self) -> None:
        """Отрисовывает дополнительные фильтры"""
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.checkbox("Только с просрочкой", key="filter_overdue_only")
        
        with col2:
            st.checkbox("Крупные договоры (>1М)", key="filter_large_contracts")
        
        with col3:
            st.checkbox("Новые (за месяц)", key="filter_recent_contracts")
