"""
Компонент обзора договоров
Отвечает за отображение таблиц и графиков договоров
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import logging
from typing import Dict, Any

from app.styles.integration import show_empty_state

logger = logging.getLogger(__name__)


class OverviewComponent:
    """Компонент обзора договоров"""
    
    def __init__(self):
        """Инициализирует компонент обзора"""
        pass
    
    def render(self, contracts_df: pd.DataFrame, filters: Dict[str, Any]) -> None:
        """Отрисовывает обзор договоров"""
        try:
            if contracts_df.empty:
                show_empty_state(
                    "Нет договоров для отображения",
                    "Проверьте настройки фильтров или подключение к базе данных",
                    "📋"
                )
                return
            
            # Графики (если включены)
            if filters.get('show_charts', True):
                self._render_charts(contracts_df)
                st.markdown("---")
            
            # Таблица договоров
            self._render_contracts_table(contracts_df)
            
            logger.debug("Обзор договоров отрисован успешно")
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки обзора: {e}")
            st.error(f"Ошибка отображения обзора: {e}")
    
    def _render_charts(self, df: pd.DataFrame) -> None:
        """Отрисовывает графики"""
        try:
            st.markdown("### 📊 Визуализация данных")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # График по статусам
                if 'status' in df.columns:
                    status_counts = df['status'].value_counts()
                    
                    fig = px.pie(
                        values=status_counts.values,
                        names=status_counts.index,
                        title="Распределение по статусам",
                        color_discrete_sequence=px.colors.qualitative.Set3
                    )
                    
                    fig.update_layout(
                        height=400,
                        showlegend=True,
                        title_x=0.5
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # График по департаментам
                if 'department' in df.columns:
                    dept_counts = df['department'].value_counts().head(10)
                    
                    fig = px.bar(
                        x=dept_counts.values,
                        y=dept_counts.index,
                        orientation='h',
                        title="Топ-10 департаментов",
                        color=dept_counts.values,
                        color_continuous_scale='Blues'
                    )
                    
                    fig.update_layout(
                        height=400,
                        showlegend=False,
                        title_x=0.5,
                        yaxis={'categoryorder': 'total ascending'}
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
            
            # График динамики по времени
            if 'start_date' in df.columns:
                st.markdown("#### 📈 Динамика заключения договоров")
                
                # Преобразуем даты и группируем по месяцам
                df_time = df.copy()
                df_time['start_date'] = pd.to_datetime(df_time['start_date'])
                df_time['month'] = df_time['start_date'].dt.to_period('M')
                
                monthly_counts = df_time.groupby('month').size().reset_index(name='count')
                monthly_counts['month'] = monthly_counts['month'].astype(str)
                
                fig = px.line(
                    monthly_counts,
                    x='month',
                    y='count',
                    title='Количество договоров по месяцам',
                    markers=True
                )
                
                fig.update_layout(
                    height=300,
                    title_x=0.5,
                    xaxis_title="Месяц",
                    yaxis_title="Количество договоров"
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки графиков: {e}")
            st.error("Не удалось отобразить графики")
    
    def _render_contracts_table(self, df: pd.DataFrame) -> None:
        """Отрисовывает таблицу договоров"""
        try:
            st.markdown("### 📋 Список договоров")
            
            # Настройки отображения таблицы
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                search_term = st.text_input(
                    "🔍 Поиск по названию или номеру:",
                    key="contracts_search"
                )
            
            with col2:
                page_size = st.selectbox(
                    "Строк на странице:",
                    options=[10, 25, 50, 100],
                    index=1,
                    key="table_page_size"
                )
            
            with col3:
                sort_column = st.selectbox(
                    "Сортировка:",
                    options=df.columns.tolist(),
                    index=0,
                    key="table_sort"
                )
            
            # Применяем поиск
            display_df = df.copy()
            if search_term:
                mask = display_df.astype(str).apply(
                    lambda x: x.str.contains(search_term, case=False, na=False)
                ).any(axis=1)
                display_df = display_df[mask]
            
            # Сортировка
            if sort_column in display_df.columns:
                display_df = display_df.sort_values(sort_column, ascending=False)
            
            # Пагинация
            total_rows = len(display_df)
            total_pages = (total_rows - 1) // page_size + 1 if total_rows > 0 else 1
            
            if total_pages > 1:
                page = st.number_input(
                    f"Страница (1-{total_pages}):",
                    min_value=1,
                    max_value=total_pages,
                    value=1,
                    key="table_page"
                )
                
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                display_df = display_df.iloc[start_idx:end_idx]
            
            # Отображаем информацию о результатах
            st.info(f"Показано {len(display_df)} из {total_rows} записей")
            
            # Отображаем таблицу
            if not display_df.empty:
                # Форматируем колонки для лучшего отображения
                formatted_df = self._format_dataframe(display_df)
                
                st.dataframe(
                    formatted_df,
                    use_container_width=True,
                    height=400
                )
                
                # Кнопка экспорта
                if st.button("📥 Экспорт в CSV", key="overview_export_csv"):
                    csv = display_df.to_csv(index=False)
                    st.download_button(
                        label="Скачать CSV",
                        data=csv,
                        file_name=f"contracts_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
            else:
                st.warning("Нет данных для отображения")
                
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки таблицы: {e}")
            st.error("Не удалось отобразить таблицу договоров")
    
    def _format_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Форматирует DataFrame для отображения"""
        try:
            formatted_df = df.copy()
            
            # Форматируем денежные суммы
            if 'total_amount' in formatted_df.columns:
                formatted_df['total_amount'] = formatted_df['total_amount'].apply(
                    lambda x: f"{x:,.0f} ₽" if pd.notnull(x) else "—"
                )
            
            # Форматируем даты
            date_columns = ['start_date', 'end_date', 'created_at', 'updated_at']
            for col in date_columns:
                if col in formatted_df.columns:
                    formatted_df[col] = pd.to_datetime(formatted_df[col], errors='coerce').dt.strftime('%d.%m.%Y')
            
            # Переименовываем колонки на русский
            column_mapping = {
                'contract_number': 'Номер договора',
                'contract_name': 'Название',
                'status': 'Статус',
                'contract_type': 'Тип',
                'department': 'Департамент',
                'total_amount': 'Сумма',
                'start_date': 'Дата начала',
                'end_date': 'Дата окончания',
                'created_at': 'Создан',
                'updated_at': 'Обновлен'
            }
            
            formatted_df = formatted_df.rename(columns=column_mapping)
            
            return formatted_df
            
        except Exception as e:
            logger.error(f"❌ Ошибка форматирования DataFrame: {e}")
            return df
