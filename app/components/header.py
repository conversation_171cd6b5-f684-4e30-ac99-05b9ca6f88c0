"""
Компонент заголовка дашборда
Отвечает за отображение заголовка и основной информации
"""
import streamlit as st
import logging
from datetime import datetime

from app.styles.integration import style_integrator
from app.components.theme_switcher import theme_switcher

logger = logging.getLogger(__name__)


class HeaderComponent:
    """Компонент заголовка дашборда"""
    
    def __init__(self):
        """Инициализирует компонент заголовка"""
        self.title = "📊 Дашборд освоения договоров"
        self.subtitle = "Система мониторинга и анализа договоров"
        
    def render(self) -> None:
        """Отрисовывает заголовок дашборда используя нативные Streamlit компоненты"""
        try:
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M")

            # Create header using native Streamlit components
            col1, col2 = st.columns([3, 1])

            with col1:
                st.title(self.title)
                st.caption(self.subtitle)

            with col2:
                st.metric(
                    label="Последнее обновление",
                    value=current_time,
                    help="Время последнего обновления данных"
                )

            # Navigation buttons using native Streamlit
            st.markdown("---")
            nav_col1, nav_col2, nav_col3, nav_col4, nav_col5 = st.columns([1, 1, 1, 1, 1])

            with nav_col1:
                if st.button("🏠 Главная", use_container_width=True):
                    st.session_state.current_page = "dashboard"

            with nav_col2:
                if st.button("📊 Аналитика", use_container_width=True):
                    st.session_state.current_page = "analytics"

            with nav_col3:
                if st.button("📋 Отчеты", use_container_width=True):
                    st.session_state.current_page = "reports"

            with nav_col4:
                if st.button("🔄 Обновить", use_container_width=True):
                    st.rerun()

            with nav_col5:
                # Компактный переключатель темы в заголовке
                theme_switcher.render_mini_toggle("header")

            st.markdown("---")
            logger.info("✅ Заголовок отрисован успешно")

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки заголовка: {e}")
            # Fallback к простому заголовку
            st.title(self.title)
            st.caption(self.subtitle)
            st.error(f"Ошибка отображения заголовка: {e}")
    
    def render_simple(self) -> None:
        """Отрисовывает простой заголовок без стилей"""
        st.title(self.title)
        st.caption(self.subtitle)
        st.caption(f"Обновлено: {datetime.now().strftime('%d.%m.%Y %H:%M')}")
    
    def render_with_navigation(self, pages: list) -> str:
        """Отрисовывает заголовок с навигацией используя нативные Streamlit компоненты"""
        try:
            # Основной заголовок
            self.render()

            # Навигация используя нативные компоненты
            if pages:
                st.markdown("### Навигация")

                # Создаем кнопки навигации
                nav_cols = st.columns(len(pages))
                selected_page = "dashboard"

                for i, page in enumerate(pages):
                    with nav_cols[i]:
                        if st.button(
                            f"{page['icon']} {page['title']}",
                            key=f"nav_{page['id']}",
                            use_container_width=True
                        ):
                            selected_page = page['id']
                            st.session_state.current_page = page['id']

                # Альтернативная навигация через selectbox
                selected_page = st.selectbox(
                    "Выберите раздел:",
                    options=[p['id'] for p in pages],
                    format_func=lambda x: next(f"{p['icon']} {p['title']}" for p in pages if p['id'] == x),
                    key="page_selector"
                )

                return selected_page

            return "dashboard"

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки навигации: {e}")
            return "dashboard"
