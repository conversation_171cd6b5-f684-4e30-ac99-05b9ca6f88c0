"""
Interactive Features Component
Provides advanced interactive functionality for the dashboard
"""
import streamlit as st
import pandas as pd
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go

logger = logging.getLogger(__name__)


class InteractiveFeatures:
    """Component for interactive dashboard features"""
    
    def __init__(self):
        """Initialize interactive features"""
        self.search_history = []
        self.filter_presets = {
            'active_contracts': {'status': 'active'},
            'overdue_contracts': {'status': 'overdue'},
            'high_value': {'amount_min': 1000000},
            'recent': {'days_back': 30}
        }
    
    def render_advanced_search(self, contracts_df: pd.DataFrame) -> pd.DataFrame:
        """Render advanced search and filtering interface"""
        try:
            st.markdown("## 🔍 Расширенный поиск и фильтрация")
            
            # Create search interface
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                search_query = st.text_input(
                    "🔍 Поиск по названию, номеру или контрагенту:",
                    key="advanced_search",
                    placeholder="Введите текст для поиска..."
                )
            
            with col2:
                search_type = st.selectbox(
                    "Тип поиска:",
                    options=["Содержит", "Точное совпадение", "Начинается с"],
                    key="search_type"
                )
            
            with col3:
                case_sensitive = st.checkbox("Учитывать регистр", key="case_sensitive")
            
            # Filter presets
            st.markdown("### 🎯 Быстрые фильтры")
            preset_cols = st.columns(4)
            
            with preset_cols[0]:
                if st.button("✅ Активные", key="filter_active"):
                    st.session_state.filter_preset = 'active_contracts'
            
            with preset_cols[1]:
                if st.button("⚠️ Просроченные", key="filter_overdue"):
                    st.session_state.filter_preset = 'overdue_contracts'
            
            with preset_cols[2]:
                if st.button("💰 Крупные", key="filter_high_value"):
                    st.session_state.filter_preset = 'high_value'
            
            with preset_cols[3]:
                if st.button("🕐 Недавние", key="filter_recent"):
                    st.session_state.filter_preset = 'recent'
            
            # Advanced filters
            with st.expander("🔧 Расширенные фильтры", expanded=False):
                self._render_advanced_filters()
            
            # Apply search and filters
            filtered_df = self._apply_search_and_filters(contracts_df, search_query, search_type, case_sensitive)
            
            # Show results summary
            self._show_search_results_summary(contracts_df, filtered_df, search_query)
            
            return filtered_df
            
        except Exception as e:
            logger.error(f"Error in advanced search: {e}")
            st.error("Ошибка в расширенном поиске")
            return contracts_df
    
    def _render_advanced_filters(self) -> None:
        """Render advanced filter controls"""
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Фильтры по датам:**")
            date_from = st.date_input("Дата начала от:", key="date_from")
            date_to = st.date_input("Дата начала до:", key="date_to")
            
            st.markdown("**Фильтры по сумме:**")
            amount_range = st.slider(
                "Диапазон сумм (млн ₽):",
                min_value=0.0,
                max_value=100.0,
                value=(0.0, 100.0),
                step=0.1,
                key="amount_range"
            )
        
        with col2:
            st.markdown("**Фильтры по статусу:**")
            status_options = st.multiselect(
                "Статусы:",
                options=['active', 'completed', 'overdue', 'pending'],
                default=['active'],
                key="status_filter"
            )
            
            st.markdown("**Фильтры по типу:**")
            type_filter = st.text_input("Тип договора содержит:", key="type_filter")
    
    def _apply_search_and_filters(self, df: pd.DataFrame, query: str, search_type: str, case_sensitive: bool) -> pd.DataFrame:
        """Apply search query and filters to dataframe"""
        if df.empty:
            return df

        filtered_df = df.copy()
        logger.info(f"🔍 ADVANCED_SEARCH: Входные данные: {len(filtered_df)} записей")

        try:
            # Apply text search
            if query:
                logger.info(f"🔍 ADVANCED_SEARCH: Применяем текстовый поиск: '{query}' (тип: {search_type})")
                search_columns = ['contract_name', 'contract_number', 'contractor_name']

                if search_type == "Содержит":
                    mask = filtered_df[search_columns].astype(str).apply(
                        lambda x: x.str.contains(query, case=case_sensitive, na=False)
                    ).any(axis=1)
                elif search_type == "Точное совпадение":
                    mask = filtered_df[search_columns].astype(str).apply(
                        lambda x: x.str.equals(query) if case_sensitive else x.str.lower().str.equals(query.lower())
                    ).any(axis=1)
                else:  # Начинается с
                    mask = filtered_df[search_columns].astype(str).apply(
                        lambda x: x.str.startswith(query, na=False) if case_sensitive else x.str.lower().str.startswith(query.lower(), na=False)
                    ).any(axis=1)

                filtered_df = filtered_df[mask]
                logger.info(f"🔍 ADVANCED_SEARCH: После текстового поиска: {len(filtered_df)} записей")
            else:
                logger.info("🔍 ADVANCED_SEARCH: Пропускаем текстовый поиск - запрос пустой")
            
            # ВРЕМЕННО ОТКЛЮЧАЕМ preset filters чтобы они не конфликтовали с основными фильтрами
            # Apply preset filters - ОТКЛЮЧЕНО
            # if hasattr(st.session_state, 'filter_preset') and st.session_state.filter_preset:
            #     preset = self.filter_presets.get(st.session_state.filter_preset, {})
            #
            #     if 'status' in preset:
            #         filtered_df = filtered_df[filtered_df['status'] == preset['status']]
            #
            #     if 'amount_min' in preset:
            #         filtered_df = filtered_df[filtered_df['total_amount'] >= preset['amount_min']]
            #
            #     if 'days_back' in preset:
            #         cutoff_date = datetime.now() - timedelta(days=preset['days_back'])
            #         filtered_df = filtered_df[pd.to_datetime(filtered_df['start_date']) >= cutoff_date]
            
            # ВРЕМЕННО ОТКЛЮЧАЕМ дополнительные фильтры advanced_search
            # чтобы они не конфликтовали с основными фильтрами
            # TODO: В будущем нужно интегрировать эти фильтры с основными

            # Apply advanced filters from session state - ОТКЛЮЧЕНО
            # if hasattr(st.session_state, 'date_from') and st.session_state.date_from:
            #     filtered_df = filtered_df[pd.to_datetime(filtered_df['start_date']) >= pd.to_datetime(st.session_state.date_from)]
            #
            # if hasattr(st.session_state, 'date_to') and st.session_state.date_to:
            #     filtered_df = filtered_df[pd.to_datetime(filtered_df['start_date']) <= pd.to_datetime(st.session_state.date_to)]
            #
            # if hasattr(st.session_state, 'amount_range') and st.session_state.amount_range:
            #     min_amount, max_amount = st.session_state.amount_range
            #     min_amount_rub = min_amount * 1_000_000
            #     max_amount_rub = max_amount * 1_000_000
            #     filtered_df = filtered_df[
            #         (filtered_df['total_amount'] >= min_amount_rub) &
            #         (filtered_df['total_amount'] <= max_amount_rub)
            #     ]
            #
            # if hasattr(st.session_state, 'status_filter') and st.session_state.status_filter:
            #     filtered_df = filtered_df[filtered_df['status'].isin(st.session_state.status_filter)]
            
            if hasattr(st.session_state, 'type_filter') and st.session_state.type_filter:
                logger.info(f"🔍 ADVANCED_SEARCH: Применяем фильтр по типу: '{st.session_state.type_filter}'")
                filtered_df = filtered_df[
                    filtered_df['contract_type'].str.contains(st.session_state.type_filter, case=False, na=False)
                ]
                logger.info(f"🔍 ADVANCED_SEARCH: После фильтра по типу: {len(filtered_df)} записей")
            else:
                logger.info("🔍 ADVANCED_SEARCH: Пропускаем фильтр по типу")

            logger.info(f"🔍 ADVANCED_SEARCH: ИТОГОВЫЙ РЕЗУЛЬТАТ: {len(filtered_df)} записей")
            return filtered_df

        except Exception as e:
            logger.error(f"❌ ADVANCED_SEARCH: Ошибка применения фильтров: {e}")
            return df
    
    def _show_search_results_summary(self, original_df: pd.DataFrame, filtered_df: pd.DataFrame, query: str) -> None:
        """Show summary of search results"""
        original_count = len(original_df)
        filtered_count = len(filtered_df)
        
        if query or filtered_count != original_count:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Найдено", f"{filtered_count:,}", f"{filtered_count - original_count:+,}")
            
            with col2:
                percentage = (filtered_count / original_count * 100) if original_count > 0 else 0
                st.metric("Процент от общего", f"{percentage:.1f}%")
            
            with col3:
                if filtered_count > 0:
                    avg_amount = filtered_df['total_amount'].mean()
                    st.metric("Средняя сумма", f"{avg_amount:,.0f} ₽")
                else:
                    st.metric("Средняя сумма", "—")
            
            with col4:
                if st.button("🗑️ Сбросить фильтры", key="interactive_reset_filters"):
                    # Clear all filter-related session state
                    filter_keys = ['filter_preset', 'date_from', 'date_to', 'amount_range', 'status_filter', 'type_filter']
                    for key in filter_keys:
                        if key in st.session_state:
                            del st.session_state[key]
                    st.rerun()
    
    def render_data_export_options(self, df: pd.DataFrame) -> None:
        """Render data export options"""
        try:
            st.markdown("## 📥 Экспорт данных")
            
            if df.empty:
                st.warning("Нет данных для экспорта")
                return
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                # CSV Export
                csv_data = df.to_csv(index=False)
                st.download_button(
                    label="📄 Скачать CSV",
                    data=csv_data,
                    file_name=f"contracts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    key="export_csv"
                )
            
            with col2:
                # Excel Export (simplified)
                if st.button("📊 Подготовить Excel", key="prepare_excel"):
                    st.success("Excel файл готов к скачиванию!")
                    # In a real implementation, you would use pandas.to_excel()
            
            with col3:
                # JSON Export
                json_data = df.to_json(orient='records', date_format='iso')
                st.download_button(
                    label="🔗 Скачать JSON",
                    data=json_data,
                    file_name=f"contracts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json",
                    key="export_json"
                )
            
            with col4:
                # Report Export
                if st.button("📋 Создать отчет", key="create_report"):
                    self._generate_summary_report(df)
            
        except Exception as e:
            logger.error(f"Error in data export: {e}")
            st.error("Ошибка экспорта данных")
    
    def _generate_summary_report(self, df: pd.DataFrame) -> None:
        """Generate and display summary report"""
        try:
            report_content = f"""
            ОТЧЕТ ПО ДОГОВОРАМ
            Дата создания: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}
            
            ОБЩАЯ СТАТИСТИКА:
            - Всего договоров: {len(df):,}
            - Общая сумма: {df['total_amount'].sum():,.0f} ₽
            - Средняя сумма: {df['total_amount'].mean():,.0f} ₽
            - Медианная сумма: {df['total_amount'].median():,.0f} ₽
            
            РАСПРЕДЕЛЕНИЕ ПО СТАТУСАМ:
            """
            
            if 'status' in df.columns:
                status_counts = df['status'].value_counts()
                for status, count in status_counts.items():
                    percentage = (count / len(df) * 100)
                    report_content += f"- {status.title()}: {count:,} ({percentage:.1f}%)\n"
            
            report_content += f"""
            
            ТОП-5 КОНТРАГЕНТОВ ПО СУММЕ:
            """
            
            if 'contractor_name' in df.columns:
                top_contractors = df.groupby('contractor_name')['total_amount'].sum().nlargest(5)
                for contractor, amount in top_contractors.items():
                    report_content += f"- {contractor}: {amount:,.0f} ₽\n"
            
            st.download_button(
                label="📥 Скачать отчет",
                data=report_content,
                file_name=f"summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain",
                key="download_report"
            )
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            st.error("Ошибка создания отчета")
