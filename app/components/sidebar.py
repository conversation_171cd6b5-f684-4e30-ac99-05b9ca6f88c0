"""
Компонент боковой панели
Отвечает за фильтры и настройки дашборда
"""
import streamlit as st
import logging
from datetime import datetime, date, timedelta
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class SidebarComponent:
    """Компонент боковой панели с фильтрами"""
    
    def __init__(self):
        """Инициализирует компонент боковой панели"""
        self.default_filters = {
            'status': 'Все',
            'contract_type': 'Все',
            'department': 'Все',
            'start_date': None,
            'end_date': None,
            'amount_range': None,
            'show_charts': True,
            'show_advanced_filters': False,
            'auto_refresh': False
        }
    
    def render(self, references: Dict[str, List] = None) -> Dict[str, Any]:
        """Отрисовывает боковую панель и возвращает фильтры"""
        try:
            st.sidebar.markdown("## 🎛️ Панель управления")
            st.sidebar.markdown("---")
            
            filters = {}
            
            # Основные фильтры
            filters.update(self._render_basic_filters(references or {}))
            
            st.sidebar.markdown("---")
            
            # Фильтры по датам
            filters.update(self._render_date_filters())
            
            st.sidebar.markdown("---")
            
            # Фильтры по суммам
            filters.update(self._render_amount_filters())
            
            st.sidebar.markdown("---")
            
            # Настройки отображения
            filters.update(self._render_display_settings())
            
            st.sidebar.markdown("---")
            
            # Действия
            self._render_actions()
            
            logger.debug(f"Боковая панель отрисована, фильтров: {len(filters)}")
            return filters
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки боковой панели: {e}")
            return self.default_filters.copy()
    
    def _render_basic_filters(self, references: Dict[str, List]) -> Dict[str, Any]:
        """Отрисовывает основные фильтры"""
        filters = {}
        
        try:
            st.sidebar.subheader("📋 Основные фильтры")
            
            # Фильтр по статусу
            statuses = ['Все'] + references.get('statuses', ['active', 'completed', 'overdue', 'suspended'])
            filters['status'] = st.sidebar.selectbox(
                "Статус договора:",
                options=statuses,
                index=0,
                key="filter_status"
            )
            
            # Фильтр по типу договора
            types = ['Все'] + references.get('types', ['Поставка', 'Услуги', 'Работы', 'Аренда'])
            filters['contract_type'] = st.sidebar.selectbox(
                "Тип договора:",
                options=types,
                index=0,
                key="filter_type"
            )
            
            # Фильтр по департаменту
            departments = ['Все'] + references.get('departments', ['IT', 'Закупки', 'Финансы', 'Юридический'])
            filters['department'] = st.sidebar.selectbox(
                "Департамент:",
                options=departments,
                index=0,
                key="filter_department"
            )
            
            return filters
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки основных фильтров: {e}")
            return {
                'status': 'Все',
                'contract_type': 'Все',
                'department': 'Все'
            }
    
    def _render_date_filters(self) -> Dict[str, Any]:
        """Отрисовывает фильтры по датам"""
        filters = {}
        
        try:
            st.sidebar.subheader("📅 Фильтры по датам")
            
            # Быстрые периоды
            period = st.sidebar.selectbox(
                "Быстрый выбор периода:",
                options=[
                    "Произвольный период",
                    "Последние 30 дней",
                    "Последние 90 дней",
                    "Текущий год",
                    "Прошлый год"
                ],
                key="date_period"
            )
            
            # Вычисляем даты на основе выбранного периода
            today = date.today()
            
            if period == "Последние 30 дней":
                start_date = today - timedelta(days=30)
                end_date = today
            elif period == "Последние 90 дней":
                start_date = today - timedelta(days=90)
                end_date = today
            elif period == "Текущий год":
                start_date = date(today.year, 1, 1)
                end_date = today
            elif period == "Прошлый год":
                start_date = date(today.year - 1, 1, 1)
                end_date = date(today.year - 1, 12, 31)
            else:
                start_date = None
                end_date = None
            
            # Поля для ручного ввода дат
            if period == "Произвольный период":
                col1, col2 = st.sidebar.columns(2)
                
                with col1:
                    filters['start_date'] = st.date_input(
                        "От:",
                        value=start_date,
                        key="filter_start_date"
                    )
                
                with col2:
                    filters['end_date'] = st.date_input(
                        "До:",
                        value=end_date,
                        key="filter_end_date"
                    )
            else:
                filters['start_date'] = start_date
                filters['end_date'] = end_date
                
                if start_date and end_date:
                    st.sidebar.info(f"Период: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}")
            
            return filters
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки фильтров по датам: {e}")
            return {'start_date': None, 'end_date': None}
    
    def _render_amount_filters(self) -> Dict[str, Any]:
        """Отрисовывает фильтры по суммам"""
        filters = {}
        
        try:
            st.sidebar.subheader("💰 Фильтры по суммам")
            
            # Включение фильтра по сумме
            use_amount_filter = st.sidebar.checkbox(
                "Фильтровать по сумме договора",
                key="use_amount_filter"
            )
            
            if use_amount_filter:
                # Диапазон сумм
                amount_range = st.sidebar.slider(
                    "Диапазон сумм (тыс. руб.):",
                    min_value=0,
                    max_value=10000,
                    value=(0, 5000),
                    step=100,
                    key="amount_range_slider"
                )
                
                filters['amount_range'] = (amount_range[0] * 1000, amount_range[1] * 1000)
                
                st.sidebar.info(f"Выбран диапазон: {amount_range[0]:,} - {amount_range[1]:,} тыс. руб.")
            else:
                filters['amount_range'] = None
            
            return filters
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки фильтров по суммам: {e}")
            return {'amount_range': None}
    
    def _render_display_settings(self) -> Dict[str, Any]:
        """Отрисовывает настройки отображения"""
        filters = {}
        
        try:
            st.sidebar.subheader("⚙️ Настройки отображения")
            
            # Показывать графики
            filters['show_charts'] = st.sidebar.checkbox(
                "Показывать графики",
                value=True,
                key="show_charts"
            )
            
            # Показывать расширенные фильтры
            filters['show_advanced_filters'] = st.sidebar.checkbox(
                "Расширенные фильтры",
                value=False,
                key="show_advanced_filters"
            )
            
            # Автообновление
            filters['auto_refresh'] = st.sidebar.checkbox(
                "Автообновление данных",
                value=False,
                key="auto_refresh"
            )
            
            if filters['auto_refresh']:
                refresh_interval = st.sidebar.selectbox(
                    "Интервал обновления:",
                    options=[30, 60, 300, 600],
                    format_func=lambda x: f"{x} сек" if x < 60 else f"{x//60} мин",
                    key="refresh_interval"
                )
                filters['refresh_interval'] = refresh_interval
            
            return filters
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки настроек отображения: {e}")
            return {
                'show_charts': True,
                'show_advanced_filters': False,
                'auto_refresh': False
            }
    
    def _render_actions(self) -> None:
        """Отрисовывает кнопки действий"""
        try:
            st.sidebar.subheader("🔧 Действия")
            
            # Кнопка обновления данных
            if st.sidebar.button("🔄 Обновить данные", key="sidebar_refresh_data"):
                if 'dashboard_app' in st.session_state:
                    st.session_state.dashboard_app.refresh_data()
                    st.rerun()

            # Кнопка сброса фильтров
            if st.sidebar.button("🗑️ Сбросить фильтры", key="sidebar_reset_filters"):
                # Очищаем все ключи фильтров из session_state
                filter_keys = [key for key in st.session_state.keys() if key.startswith('filter_')]
                for key in filter_keys:
                    del st.session_state[key]
                st.rerun()

            # Кнопка экспорта данных
            if st.sidebar.button("📥 Экспорт данных", key="sidebar_export_data"):
                st.sidebar.info("Функция экспорта будет добавлена в следующей версии")
            
            # Информация о системе
            with st.sidebar.expander("ℹ️ Информация о системе"):
                st.write("**Версия:** 2.0.0")
                st.write("**Последнее обновление:** Декабрь 2024")
                st.write("**Статус:** Активна")
                
                if st.button("🔍 Диагностика", key="diagnostics"):
                    st.info("Все системы работают нормально ✅")
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки действий: {e}")
    
    def get_active_filters_count(self, filters: Dict[str, Any]) -> int:
        """Возвращает количество активных фильтров"""
        count = 0
        
        if filters.get('status') and filters['status'] != 'Все':
            count += 1
        if filters.get('contract_type') and filters['contract_type'] != 'Все':
            count += 1
        if filters.get('department') and filters['department'] != 'Все':
            count += 1
        if filters.get('start_date'):
            count += 1
        if filters.get('end_date'):
            count += 1
        if filters.get('amount_range'):
            count += 1
            
        return count
