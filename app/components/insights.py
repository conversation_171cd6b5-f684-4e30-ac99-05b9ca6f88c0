"""
Компонент быстрых инсайтов
Отвечает за аналитические инсайты и рекомендации
"""
import streamlit as st
import pandas as pd
import logging
from typing import List, Dict, Any

from app.styles.integration import show_alert

logger = logging.getLogger(__name__)


class InsightsComponent:
    """Компонент быстрых инсайтов"""
    
    def __init__(self):
        """Инициализирует компонент инсайтов"""
        pass
    
    def render(self, contracts_df: pd.DataFrame) -> None:
        """Отрисовывает быстрые инсайты"""
        try:
            if contracts_df.empty:
                show_alert("Нет данных для анализа", "warning", "Инсайты")
                return
            
            st.markdown("### 💡 Быстрые инсайты")
            
            # Генерируем инсайты
            insights = self._generate_insights(contracts_df)
            
            # Отображаем инсайты
            for insight in insights:
                self._render_insight(insight)
            
            logger.debug("Инсайты отрисованы успешно")
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки инсайтов: {e}")
            show_alert(f"Ошибка генерации инсайтов: {e}", "danger", "Ошибка")
    
    def _generate_insights(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Генерирует инсайты на основе данных"""
        insights = []
        
        try:
            # Инсайт о просрочке
            overdue_count = len(df[df['status'] == 'overdue'])
            if overdue_count > 0:
                overdue_rate = (overdue_count / len(df)) * 100
                insights.append({
                    'type': 'warning' if overdue_rate > 10 else 'info',
                    'title': 'Анализ просрочек',
                    'message': f'Обнаружено {overdue_count} просроченных договоров ({overdue_rate:.1f}%)',
                    'recommendation': 'Рекомендуется принять меры по ускорению выполнения' if overdue_rate > 10 else 'Уровень просрочек в норме'
                })
            
            # Инсайт о крупных договорах
            if 'total_amount' in df.columns:
                large_contracts = df[df['total_amount'] > 1000000]
                if len(large_contracts) > 0:
                    large_sum = large_contracts['total_amount'].sum()
                    total_sum = df['total_amount'].sum()
                    large_percentage = (large_sum / total_sum) * 100
                    
                    insights.append({
                        'type': 'info',
                        'title': 'Крупные договоры',
                        'message': f'{len(large_contracts)} договоров на сумму свыше 1М ₽ ({large_percentage:.1f}% от общей суммы)',
                        'recommendation': 'Уделите особое внимание контролю крупных договоров'
                    })
            
            # Инсайт о департаментах
            if 'department' in df.columns:
                dept_stats = df['department'].value_counts()
                most_active_dept = dept_stats.index[0]
                most_active_count = dept_stats.iloc[0]
                
                insights.append({
                    'type': 'success',
                    'title': 'Активность департаментов',
                    'message': f'Наиболее активный департамент: {most_active_dept} ({most_active_count} договоров)',
                    'recommendation': 'Изучите лучшие практики этого департамента'
                })
            
        except Exception as e:
            logger.error(f"❌ Ошибка генерации инсайтов: {e}")
        
        return insights
    
    def _render_insight(self, insight: Dict[str, Any]) -> None:
        """Отрисовывает отдельный инсайт"""
        try:
            alert_type = insight.get('type', 'info')
            title = insight.get('title', 'Инсайт')
            message = insight.get('message', '')
            recommendation = insight.get('recommendation', '')
            
            full_message = message
            if recommendation:
                full_message += f"<br><strong>Рекомендация:</strong> {recommendation}"
            
            show_alert(full_message, alert_type, title)
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки инсайта: {e}")
