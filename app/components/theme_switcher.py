"""
Компонент переключателя темы дашборда
Обеспечивает переключение между светлой и темной темами
"""
import streamlit as st
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ThemeSwitcher:
    """Компонент для переключения темы дашборда"""
    
    def __init__(self):
        """Инициализирует переключатель темы"""
        self.themes = {
            'light': {
                'name': 'Светлая',
                'icon': '☀️',
                'description': 'Светлая тема для дневной работы'
            },
            'dark': {
                'name': 'Темная', 
                'icon': '🌙',
                'description': 'Темная тема для работы в условиях низкой освещенности'
            }
        }
    
    def render_sidebar_toggle(self) -> None:
        """Отрисовывает компактный переключатель темы в боковой панели"""
        try:
            with st.sidebar:
                st.markdown("---")

                current_theme = st.session_state.get('theme', 'light')
                current_theme_info = self.themes[current_theme]

                # Компактный переключатель
                col1, col2, col3 = st.columns([1, 1, 1])

                with col1:
                    st.markdown(f"**🎨**")

                with col2:
                    # Кнопка светлой темы
                    if st.button(
                        "☀️",
                        disabled=(current_theme == 'light'),
                        key="sidebar_light_theme",
                        help="Светлая тема"
                    ):
                        st.session_state.theme = 'light'
                        st.rerun()

                with col3:
                    # Кнопка темной темы
                    if st.button(
                        "🌙",
                        disabled=(current_theme == 'dark'),
                        key="sidebar_dark_theme",
                        help="Темная тема"
                    ):
                        st.session_state.theme = 'dark'
                        st.rerun()

                # Показываем текущую тему компактно
                st.caption(f"Тема: {current_theme_info['name']}")

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки переключателя темы в боковой панели: {e}")
    
    def render_header_toggle(self) -> None:
        """Отрисовывает компактный переключатель темы для заголовка в виде иконки"""
        try:
            current_theme = st.session_state.get('theme', 'light')

            # Определяем следующую тему
            next_theme = 'dark' if current_theme == 'light' else 'light'
            next_theme_info = self.themes[next_theme]

            # Компактная кнопка только с иконкой
            if st.button(
                next_theme_info['icon'],
                use_container_width=True,
                key="header_theme_switcher",
                help=f"Переключить на {next_theme_info['name'].lower()} тему"
            ):
                st.session_state.theme = next_theme
                st.rerun()

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки переключателя темы в заголовке: {e}")

    def render_compact_toggle(self, key_suffix: str = "") -> None:
        """Отрисовывает очень компактный переключатель темы только с иконками"""
        try:
            current_theme = st.session_state.get('theme', 'light')

            # Создаем две маленькие кнопки рядом
            col1, col2 = st.columns(2)

            with col1:
                if st.button(
                    "☀️",
                    disabled=(current_theme == 'light'),
                    key=f"compact_light_theme_{key_suffix}",
                    help="Светлая тема"
                ):
                    st.session_state.theme = 'light'
                    st.rerun()

            with col2:
                if st.button(
                    "🌙",
                    disabled=(current_theme == 'dark'),
                    key=f"compact_dark_theme_{key_suffix}",
                    help="Темная тема"
                ):
                    st.session_state.theme = 'dark'
                    st.rerun()

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки компактного переключателя темы: {e}")

    def render_mini_toggle(self, key_suffix: str = "") -> None:
        """Отрисовывает мини переключатель темы - одна кнопка с текущей иконкой"""
        try:
            current_theme = st.session_state.get('theme', 'light')
            current_theme_info = self.themes[current_theme]
            next_theme = 'dark' if current_theme == 'light' else 'light'

            if st.button(
                current_theme_info['icon'],
                key=f"mini_theme_toggle_{key_suffix}",
                help=f"Текущая тема: {current_theme_info['name']}. Нажмите для переключения"
            ):
                st.session_state.theme = next_theme
                st.rerun()

        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки мини переключателя темы: {e}")

    def render_floating_toggle(self) -> None:
        """Отрисовывает плавающий переключатель темы"""
        try:
            current_theme = st.session_state.get('theme', 'light')
            next_theme = 'dark' if current_theme == 'light' else 'light'
            next_theme_info = self.themes[next_theme]
            
            # Создаем плавающую кнопку
            st.markdown("""
            <style>
            .floating-theme-toggle {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 999;
                background: var(--primary-color);
                border-radius: 50%;
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .floating-theme-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Используем колонки для позиционирования
            col1, col2, col3 = st.columns([8, 1, 1])
            
            with col3:
                if st.button(
                    next_theme_info['icon'],
                    key="floating_theme_toggle",
                    help=f"Переключить на {next_theme_info['name'].lower()} тему"
                ):
                    st.session_state.theme = next_theme
                    st.rerun()
        
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки плавающего переключателя темы: {e}")
    
    def get_current_theme(self) -> str:
        """Возвращает текущую тему"""
        return st.session_state.get('theme', 'light')
    
    def get_theme_info(self, theme: str = None) -> Dict[str, Any]:
        """Возвращает информацию о теме"""
        if theme is None:
            theme = self.get_current_theme()
        return self.themes.get(theme, self.themes['light'])
    
    def apply_theme_styles(self, theme: str = None) -> None:
        """Применяет стили для выбранной темы"""
        if theme is None:
            theme = self.get_current_theme()
        
        try:
            if theme == 'dark':
                self._apply_dark_theme()
            else:
                self._apply_light_theme()

            # Принудительно обновляем тему
            self.force_theme_refresh()

            logger.info(f"✅ Применены стили для темы: {theme}")

        except Exception as e:
            logger.error(f"❌ Ошибка применения стилей темы: {e}")

    def force_theme_refresh(self) -> None:
        """Принудительно обновляет тему для всех элементов"""
        current_theme = self.get_current_theme()

        # Дополнительные CSS правила для принудительного применения
        st.markdown(f"""
        <style>
        /* ПРИНУДИТЕЛЬНОЕ ПРИМЕНЕНИЕ ТЕМЫ */

        /* МАКСИМАЛЬНО АГРЕССИВНЫЕ ПРАВИЛА ДЛЯ КНОПОК - ПРИМЕНЯЮТСЯ В ПОСЛЕДНЮЮ ОЧЕРЕДЬ */
        button[kind="secondary"][data-testid="stBaseButton-secondary"].st-emotion-cache-15f0lx0 {{
            background-color: {'#404040' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#606060' if current_theme == 'dark' else '#d1d5db'} !important;
            min-height: 2.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
        }}

        button[kind="secondary"][data-testid="stBaseButton-secondary"].st-emotion-cache-15f0lx0:hover {{
            background-color: {'#505050' if current_theme == 'dark' else '#f8f9fa'} !important;
            color: {'#ffffff' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#707070' if current_theme == 'dark' else '#adb5bd'} !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4) !important;
        }}

        /* СПЕЦИФИЧНЫЕ ПРАВИЛА ДЛЯ ВСЕХ ВОЗМОЖНЫХ КОМБИНАЦИЙ КЛАССОВ */
        .st-emotion-cache-8atqhb.e1mlolmg0 button.st-emotion-cache-15f0lx0.el4r43z2 {{
            background-color: {'#404040' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#606060' if current_theme == 'dark' else '#d1d5db'} !important;
        }}

        .st-emotion-cache-8atqhb.e1mlolmg0 button.st-emotion-cache-15f0lx0.el4r43z2:hover {{
            background-color: {'#505050' if current_theme == 'dark' else '#f8f9fa'} !important;
            color: {'#ffffff' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#707070' if current_theme == 'dark' else '#adb5bd'} !important;
        }}

        /* КРИТИЧЕСКИ ВАЖНЫЕ ПРАВИЛА ДЛЯ ЭЛЕМЕНТОВ ВНУТРИ КНОПОК */
        /* Исключаем все элементы внутри кнопок из получения фона контейнеров */
        button p,
        button div,
        button span,
        .stButton p,
        .stButton div,
        .stButton span,
        .stButton button p,
        .stButton button div,
        .stButton button span,
        button[kind="secondary"] p,
        button[kind="secondary"] div,
        button[kind="secondary"] span,
        button[data-testid="stBaseButton-secondary"] p,
        button[data-testid="stBaseButton-secondary"] div,
        button[data-testid="stBaseButton-secondary"] span,
        .st-emotion-cache-15f0lx0 p,
        .st-emotion-cache-15f0lx0 div,
        .st-emotion-cache-15f0lx0 span,
        div[data-testid="stMarkdownContainer"] p,
        .st-emotion-cache-v2zoge p,
        .st-emotion-cache-v2zoge.e1fxfrsf0 p {{
            background-color: transparent !important;
            color: inherit !important;
        }}

        /* Специальные правила для markdown контейнеров внутри кнопок */
        button div[data-testid="stMarkdownContainer"],
        button .st-emotion-cache-v2zoge,
        button .st-emotion-cache-v2zoge.e1fxfrsf0,
        .stButton div[data-testid="stMarkdownContainer"],
        .stButton .st-emotion-cache-v2zoge,
        .stButton .st-emotion-cache-v2zoge.e1fxfrsf0,
        .st-emotion-cache-15f0lx0 div[data-testid="stMarkdownContainer"],
        .st-emotion-cache-15f0lx0 .st-emotion-cache-v2zoge,
        .st-emotion-cache-15f0lx0 .st-emotion-cache-v2zoge.e1fxfrsf0,
        .st-emotion-cache-8atqhb div[data-testid="stMarkdownContainer"],
        .st-emotion-cache-8atqhb .st-emotion-cache-v2zoge,
        .st-emotion-cache-8atqhb .st-emotion-cache-v2zoge.e1fxfrsf0 {{
            background-color: transparent !important;
            color: inherit !important;
        }}

        /* МАКСИМАЛЬНО СПЕЦИФИЧНЫЕ ПРАВИЛА ДЛЯ ПРОБЛЕМНЫХ ЭЛЕМЕНТОВ */
        .st-emotion-cache-8atqhb.e1mlolmg0 button.st-emotion-cache-15f0lx0.el4r43z2 div.st-emotion-cache-v2zoge.e1fxfrsf0 p,
        .st-emotion-cache-8atqhb.e1mlolmg0 button.st-emotion-cache-15f0lx0.el4r43z2 div.st-emotion-cache-v2zoge p,
        .st-emotion-cache-8atqhb.e1mlolmg0 button.st-emotion-cache-15f0lx0.el4r43z2 div[data-testid="stMarkdownContainer"] p,
        button[kind="secondary"][data-testid="stBaseButton-secondary"].st-emotion-cache-15f0lx0 div.st-emotion-cache-v2zoge.e1fxfrsf0 p,
        button[kind="secondary"][data-testid="stBaseButton-secondary"].st-emotion-cache-15f0lx0 div.st-emotion-cache-v2zoge p,
        button[kind="secondary"][data-testid="stBaseButton-secondary"].st-emotion-cache-15f0lx0 div[data-testid="stMarkdownContainer"] p {{
            background-color: transparent !important;
            color: inherit !important;
        }}
        .stApp, .stApp *, .main *, .block-container *,
        div:not(button div):not(.stButton div):not(.stButton button div),
        span:not(button span):not(.stButton span):not(.stButton button span),
        p:not(button p):not(.stButton p):not(.stButton button p),
        h1, h2, h3, h4, h5, h6, label {{
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
        }}

        /* КНОПКИ - МАКСИМАЛЬНО СПЕЦИФИЧНЫЕ СЕЛЕКТОРЫ */
        button,
        .stButton button,
        .stButton > button,
        div[data-testid="stButton"] button,
        div[data-testid="stButton"] > button,
        .st-emotion-cache-8atqhb button,
        .st-emotion-cache-15f0lx0,
        button[data-testid="stBaseButton-secondary"],
        button[kind="secondary"],
        button[class*="st-emotion-cache"],
        button[class*="stButton"],
        button[class*="Button"],
        [class*="st-emotion-cache"] button,
        [class*="stButton"] button,
        [class*="Button"] button,
        .element-container button,
        .stApp button,
        .stDownloadButton button,
        .stFormSubmitButton button {{
            background-color: {'#404040' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#606060' if current_theme == 'dark' else '#d1d5db'} !important;
            min-height: 2.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
        }}

        button:hover,
        .stButton button:hover,
        .stButton > button:hover,
        div[data-testid="stButton"] button:hover,
        div[data-testid="stButton"] > button:hover,
        .st-emotion-cache-8atqhb button:hover,
        .st-emotion-cache-15f0lx0:hover,
        button[data-testid="stBaseButton-secondary"]:hover,
        button[kind="secondary"]:hover,
        button[class*="st-emotion-cache"]:hover,
        button[class*="stButton"]:hover,
        button[class*="Button"]:hover,
        [class*="st-emotion-cache"] button:hover,
        [class*="stButton"] button:hover,
        [class*="Button"] button:hover,
        .element-container button:hover,
        .stApp button:hover,
        .stDownloadButton button:hover,
        .stFormSubmitButton button:hover {{
            background-color: {'#505050' if current_theme == 'dark' else '#f8f9fa'} !important;
            color: {'#ffffff' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#707070' if current_theme == 'dark' else '#adb5bd'} !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4) !important;
        }}

        .stApp {{
            background-color: {'#0e1117' if current_theme == 'dark' else '#ffffff'} !important;
        }}

        .stSidebar {{
            background-color: {'#262730' if current_theme == 'dark' else '#f8f9fa'} !important;
        }}

        /* ПРИНУДИТЕЛЬНЫЕ СТИЛИ ДЛЯ ВСЕХ ЭЛЕМЕНТОВ ФОРМ */
        .stSelectbox, .stSelectbox *,
        .stTextInput, .stTextInput *,
        .stTextArea, .stTextArea *,
        .stNumberInput, .stNumberInput *,
        .stMultiSelect, .stMultiSelect *,
        .stDateInput, .stDateInput *,
        .stTimeInput, .stTimeInput *,
        .stSlider, .stSlider *,
        .stCheckbox, .stCheckbox *,
        .stRadio, .stRadio * {{
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
        }}

        input, textarea, select, option {{
            background-color: {'#1e2130' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
            border: 1px solid {'#404040' if current_theme == 'dark' else '#d1d5db'} !important;
        }}

        /* ПРИНУДИТЕЛЬНЫЕ СТИЛИ ДЛЯ ВЫПАДАЮЩИХ СПИСКОВ */
        [data-baseweb="popover"],
        [data-baseweb="menu"],
        [role="listbox"],
        [data-baseweb="popover"] *,
        [data-baseweb="menu"] *,
        [role="listbox"] * {{
            background-color: {'#1e2130' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
            border-color: {'#404040' if current_theme == 'dark' else '#d1d5db'} !important;
        }}

        [role="option"],
        [data-baseweb="menu"] li {{
            background-color: {'#1e2130' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
        }}

        [role="option"]:hover,
        [data-baseweb="menu"] li:hover {{
            background-color: {'#262730' if current_theme == 'dark' else '#f8f9fa'} !important;
            color: {'#ffffff' if current_theme == 'dark' else '#262730'} !important;
        }}

        /* ПРИНУДИТЕЛЬНЫЕ СТИЛИ ДЛЯ ЭКСПАНДЕРОВ */
        [data-testid="stExpander"],
        [data-testid="stExpander"] *,
        details,
        details *,
        summary,
        summary *,
        .streamlit-expanderHeader,
        .streamlit-expanderHeader *,
        .streamlit-expanderContent,
        .streamlit-expanderContent *:not(button):not(.stButton):not(.stButton *) {{
            background-color: {'#1e2130' if current_theme == 'dark' else '#ffffff'} !important;
            color: {'#fafafa' if current_theme == 'dark' else '#262730'} !important;
            border-color: {'#404040' if current_theme == 'dark' else '#d1d5db'} !important;
        }}
        </style>
        """, unsafe_allow_html=True)

    def _apply_dark_theme(self) -> None:
        """Применяет максимально агрессивные стили темной темы"""
        st.markdown("""
        <style>
        /* === ГЛОБАЛЬНЫЕ ПЕРЕОПРЕДЕЛЕНИЯ === */
        * {
            color: #fafafa !important;
        }

        /* === ОСНОВНЫЕ КОНТЕЙНЕРЫ === */
        .stApp {
            background-color: #0e1117 !important;
            color: #fafafa !important;
        }

        .stApp *, .stApp div, .stApp span, .stApp p, .stApp label {
            color: #fafafa !important;
        }

        .main .block-container {
            background-color: #0e1117 !important;
            color: #fafafa !important;
        }

        .main .block-container * {
            color: #fafafa !important;
        }

        /* === ВЕРХНЯЯ ПАНЕЛЬ STREAMLIT - АГРЕССИВНЫЕ СТИЛИ === */
        .stApp > header {
            background-color: #262730 !important;
        }

        .stApp > header * {
            color: #fafafa !important;
        }

        header[data-testid="stHeader"] {
            background-color: #262730 !important;
        }

        header[data-testid="stHeader"] * {
            color: #fafafa !important;
        }

        .stToolbar {
            background-color: #262730 !important;
        }

        .stToolbar * {
            color: #fafafa !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ СЕЛЕКТОРЫ ДЛЯ ВЕРХНЕЙ ПАНЕЛИ === */
        .stApp > div:first-child {
            background-color: #262730 !important;
        }

        .stApp > div:first-child * {
            color: #fafafa !important;
        }

        /* === ИСПРАВЛЕНИЕ STREAMLIT HEADER === */
        .stApp > div[data-testid="stHeader"] {
            background-color: #262730 !important;
        }

        .stApp > div[data-testid="stHeader"] * {
            color: #fafafa !important;
        }

        /* === ИСПРАВЛЕНИЕ TOOLBAR === */
        div[data-testid="stToolbar"] {
            background-color: #262730 !important;
        }

        div[data-testid="stToolbar"] * {
            color: #fafafa !important;
        }

        /* === ИСПРАВЛЕНИЕ ДЕКОРАТОРА === */
        div[data-testid="stDecoration"] {
            background-color: #262730 !important;
        }

        /* === БОКОВАЯ ПАНЕЛЬ === */
        .stSidebar {
            background-color: #262730 !important;
        }

        .stSidebar * {
            color: #fafafa !important;
        }

        .stSidebar .stSidebar-content {
            background-color: #262730 !important;
        }

        section[data-testid="stSidebar"] {
            background-color: #262730 !important;
        }

        section[data-testid="stSidebar"] * {
            color: #fafafa !important;
        }

        /* === ЗАГОЛОВКИ И ТЕКСТ === */
        h1, h2, h3, h4, h5, h6 {
            color: #fafafa !important;
        }

        p:not(button p):not(.stButton p):not(.stButton button p),
        div:not(button div):not(.stButton div):not(.stButton button div),
        span:not(button span):not(.stButton span):not(.stButton button span),
        label {
            color: #fafafa !important;
        }

        /* СПЕЦИАЛЬНЫЕ ПРАВИЛА ДЛЯ ЭЛЕМЕНТОВ ВНУТРИ КНОПОК */
        button p, button div, button span,
        .stButton p, .stButton div, .stButton span,
        .stButton button p, .stButton button div, .stButton button span,
        button[kind="secondary"] p, button[kind="secondary"] div, button[kind="secondary"] span,
        button[data-testid="stBaseButton-secondary"] p,
        button[data-testid="stBaseButton-secondary"] div,
        button[data-testid="stBaseButton-secondary"] span,
        .st-emotion-cache-15f0lx0 p,
        .st-emotion-cache-15f0lx0 div,
        .st-emotion-cache-15f0lx0 span,
        div[data-testid="stMarkdownContainer"] p,
        .st-emotion-cache-v2zoge p,
        .st-emotion-cache-v2zoge.e1fxfrsf0 p {
            background-color: transparent !important;
            color: inherit !important;
        }

        .stMarkdown * {
            color: #fafafa !important;
        }

        .stCaption, .stCaption * {
            color: #b8bcc8 !important;
        }

        /* === РАЗДЕЛИТЕЛИ === */
        hr {
            border-color: #404040 !important;
            background-color: #404040 !important;
        }

        /* === МЕТРИКИ === */
        .stMetric {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.4) !important;
        }

        .stMetric [data-testid="metric-container"] {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            padding: 1rem !important;
            border-radius: 0.5rem !important;
            color: #fafafa !important;
        }

        .stMetric [data-testid="metric-container"] > div {
            color: #fafafa !important;
        }

        .stMetric label {
            color: #b8bcc8 !important;
        }

        .stMetric [data-testid="metric-container"] [data-testid="metric-value"] {
            color: #fafafa !important;
            font-weight: 600 !important;
        }

        .stMetric [data-testid="metric-container"] [data-testid="metric-delta"] {
            color: #10b981 !important;
        }

        /* === АЛЕРТЫ И УВЕДОМЛЕНИЯ === */
        .stAlert {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            color: #fafafa !important;
        }

        .stAlert > div {
            background-color: transparent !important;
            color: #fafafa !important;
        }

        .stSuccess {
            background-color: #0f2419 !important;
            border-left: 4px solid #10b981 !important;
            color: #fafafa !important;
        }

        .stInfo {
            background-color: #0c1929 !important;
            border-left: 4px solid #06b6d4 !important;
            color: #fafafa !important;
        }

        .stWarning {
            background-color: #2d1b0e !important;
            border-left: 4px solid #f59e0b !important;
            color: #fafafa !important;
        }

        .stError {
            background-color: #2d1319 !important;
            border-left: 4px solid #ef4444 !important;
            color: #fafafa !important;
        }

        /* === ТАБЛИЦЫ И DATAFRAMES === */
        .stDataFrame {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            border-radius: 8px !important;
        }

        .stDataFrame [data-testid="stDataFrame"] {
            background-color: #1e2130 !important;
        }

        .stDataFrame table {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        .stDataFrame thead tr th {
            background-color: #262730 !important;
            color: #fafafa !important;
            border-bottom: 1px solid #404040 !important;
        }

        .stDataFrame tbody tr td {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border-bottom: 1px solid #333333 !important;
        }

        .stDataFrame tbody tr:hover td {
            background-color: #262730 !important;
        }

        /* === КНОПКИ - АГРЕССИВНЫЕ СТИЛИ === */
        button {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
            min-height: 2.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            transition: all 0.2s ease !important;
            font-weight: 500 !important;
        }

        button:hover {
            background-color: #505050 !important;
            border: 1px solid #707070 !important;
            color: #ffffff !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4) !important;
        }

        button:active {
            transform: translateY(0px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
            background-color: #454545 !important;
        }

        button:focus {
            outline: 2px solid #06b6d4 !important;
            outline-offset: 2px !important;
        }

        button:disabled {
            background-color: #2a2a2a !important;
            color: #666666 !important;
            border: 1px solid #333333 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
        }

        /* === СПЕЦИФИЧНЫЕ СЕЛЕКТОРЫ ДЛЯ КНОПОК STREAMLIT === */
        .stButton > button,
        .stButton button,
        div[data-testid="stButton"] > button,
        div[data-testid="stButton"] button,
        .st-emotion-cache-8atqhb button,
        .st-emotion-cache-15f0lx0,
        button[data-testid="stBaseButton-secondary"],
        button[kind="secondary"] {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
        }

        .stButton > button:hover,
        .stButton button:hover,
        div[data-testid="stButton"] > button:hover,
        div[data-testid="stButton"] button:hover,
        .st-emotion-cache-8atqhb button:hover,
        .st-emotion-cache-15f0lx0:hover,
        button[data-testid="stBaseButton-secondary"]:hover,
        button[kind="secondary"]:hover {
            background-color: #505050 !important;
            color: #ffffff !important;
            border: 1px solid #707070 !important;
        }

        /* === СПЕЦИАЛЬНЫЕ КНОПКИ === */
        .stDownloadButton > button {
            background-color: #10b981 !important;
            color: #ffffff !important;
            border: 1px solid #059669 !important;
        }

        .stDownloadButton > button:hover {
            background-color: #059669 !important;
            border: 1px solid #047857 !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ КНОПОК === */
        /* Перехватываем все возможные классы кнопок Streamlit */
        [class*="st-emotion-cache"] button,
        [class*="stButton"] button,
        [class*="Button"] button,
        button[class*="st-emotion-cache"],
        button[class*="stButton"],
        button[class*="Button"],
        .element-container button,
        .stApp button {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
        }

        [class*="st-emotion-cache"] button:hover,
        [class*="stButton"] button:hover,
        [class*="Button"] button:hover,
        button[class*="st-emotion-cache"]:hover,
        button[class*="stButton"]:hover,
        button[class*="Button"]:hover,
        .element-container button:hover,
        .stApp button:hover {
            background-color: #505050 !important;
            color: #ffffff !important;
            border: 1px solid #707070 !important;
        }

        /* === КНОПКИ В БОКОВОЙ ПАНЕЛИ === */
        .stSidebar button,
        .stSidebar [class*="st-emotion-cache"] button,
        .stSidebar [class*="stButton"] button {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
        }

        .stSidebar button:hover,
        .stSidebar [class*="st-emotion-cache"] button:hover,
        .stSidebar [class*="stButton"] button:hover {
            background-color: #505050 !important;
            color: #ffffff !important;
        }

        /* === ФОРМЫ И ИНПУТЫ - МАКСИМАЛЬНО АГРЕССИВНЫЕ СТИЛИ === */
        .stSelectbox, .stSelectbox * {
            color: #fafafa !important;
        }

        .stSelectbox > div > div {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
        }

        .stSelectbox > div > div > div {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        .stSelectbox [data-baseweb="select"] {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        .stSelectbox [data-baseweb="select"] > div {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            color: #fafafa !important;
        }

        .stSelectbox label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        /* === АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ СЕЛЕКТОВ === */
        select, .stSelectbox select {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        option {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === ВЫПАДАЮЩИЕ СПИСКИ - МАКСИМАЛЬНО АГРЕССИВНЫЕ СТИЛИ === */
        .stSelectbox [data-baseweb="popover"] {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
        }

        .stSelectbox [data-baseweb="menu"] {
            background-color: #1e2130 !important;
        }

        .stSelectbox [data-baseweb="menu"] li {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        .stSelectbox [data-baseweb="menu"] li:hover {
            background-color: #262730 !important;
            color: #ffffff !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ СЕЛЕКТОРЫ ДЛЯ ВЫПАДАЮЩИХ СПИСКОВ === */
        [data-baseweb="popover"] {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
        }

        [data-baseweb="menu"] {
            background-color: #1e2130 !important;
        }

        [data-baseweb="menu"] * {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        [data-baseweb="menu"] li {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        [data-baseweb="menu"] li:hover {
            background-color: #262730 !important;
            color: #ffffff !important;
        }

        /* === АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ ВЫПАДАЮЩИХ ЭЛЕМЕНТОВ === */
        [role="listbox"] {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
        }

        [role="listbox"] * {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        [role="option"] {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        [role="option"]:hover {
            background-color: #262730 !important;
            color: #ffffff !important;
        }

        .stTextInput > div > div > input {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
            padding: 0.5rem !important;
        }

        .stTextInput > div > div > input:focus {
            border-color: #06b6d4 !important;
            box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
            outline: none !important;
        }

        .stTextInput > div > div > input::placeholder {
            color: #6b7280 !important;
        }

        .stTextInput label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        /* === ДАТА И ВРЕМЯ === */
        .stDateInput > div > div > input {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
        }

        .stTimeInput > div > div > input {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
        }

        .stTextArea > div > div > textarea {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
            padding: 0.5rem !important;
        }

        .stTextArea > div > div > textarea:focus {
            border-color: #06b6d4 !important;
            box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
            outline: none !important;
        }

        .stTextArea > div > div > textarea::placeholder {
            color: #6b7280 !important;
        }

        .stTextArea label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stNumberInput > div > div > input {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
            padding: 0.5rem !important;
        }

        .stNumberInput > div > div > input:focus {
            border-color: #06b6d4 !important;
            box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
        }

        .stNumberInput label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        /* === СЛАЙДЕРЫ === */
        .stSlider > div > div > div > div {
            background-color: #404040 !important;
        }

        .stSlider > div > div > div > div > div {
            background-color: #06b6d4 !important;
        }

        .stSlider label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        /* === ЧЕКБОКСЫ И РАДИО === */
        .stCheckbox > label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stCheckbox > label > div {
            color: #fafafa !important;
        }

        .stRadio > label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stRadio > div > label {
            color: #fafafa !important;
        }

        .stRadio > div > label > div {
            color: #fafafa !important;
        }

        /* === ТАБЫ === */
        .stTabs [data-baseweb="tab-list"] {
            background-color: #262730 !important;
            border-bottom: 1px solid #404040 !important;
        }

        .stTabs [data-baseweb="tab"] {
            background-color: #1e2130 !important;
            color: #b8bcc8 !important;
            border: 1px solid #404040 !important;
            border-bottom: none !important;
            margin-right: 2px !important;
            border-radius: 6px 6px 0 0 !important;
        }

        .stTabs [data-baseweb="tab"]:hover {
            background-color: #262730 !important;
            color: #fafafa !important;
        }

        .stTabs [data-baseweb="tab"][aria-selected="true"] {
            background-color: #0e1117 !important;
            color: #fafafa !important;
            border-bottom: 1px solid #0e1117 !important;
        }

        .stTabs [data-baseweb="tab-panel"] {
            background-color: #0e1117 !important;
            padding: 1rem !important;
        }

        /* === ЭКСПАНДЕРЫ - МАКСИМАЛЬНО АГРЕССИВНЫЕ СТИЛИ === */
        .streamlit-expanderHeader {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
        }

        .streamlit-expanderContent {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            border-top: none !important;
            border-radius: 0 0 6px 6px !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ СЕЛЕКТОРЫ ДЛЯ ЭКСПАНДЕРОВ === */
        [data-testid="stExpander"] {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
        }

        [data-testid="stExpander"] * {
            color: #fafafa !important;
        }

        [data-testid="stExpander"] > div {
            background-color: #1e2130 !important;
        }

        [data-testid="stExpander"] > div > div {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === ЗАГОЛОВКИ ЭКСПАНДЕРОВ === */
        [data-testid="stExpander"] summary {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        [data-testid="stExpander"] summary * {
            color: #fafafa !important;
        }

        /* === СОДЕРЖИМОЕ ЭКСПАНДЕРОВ === */
        [data-testid="stExpander"] .streamlit-expanderContent {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        [data-testid="stExpander"] .streamlit-expanderContent *:not(button):not(.stButton):not(.stButton *) {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ DETAILS/SUMMARY === */
        details {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            border-radius: 6px !important;
        }

        details * {
            color: #fafafa !important;
        }

        summary {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border-bottom: 1px solid #404040 !important;
        }

        summary * {
            color: #fafafa !important;
        }

        details > div {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        details > div *:not(button):not(.stButton):not(.stButton *) {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === ГРАФИКИ === */
        .stPlotlyChart {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            border-radius: 8px !important;
            padding: 10px !important;
        }

        .js-plotly-plot {
            background-color: #1e2130 !important;
        }

        /* === ПРОГРЕСС БАРЫ === */
        .stProgress > div > div > div {
            background-color: #404040 !important;
        }

        .stProgress > div > div > div > div {
            background-color: #06b6d4 !important;
        }

        /* === СПИННЕРЫ === */
        .stSpinner > div {
            border-color: #06b6d4 !important;
        }

        /* === КОМПАКТНЫЕ КНОПКИ ТЕМЫ === */
        button[title="Светлая тема"], button[title="Темная тема"] {
            width: 2.5rem !important;
            height: 2.5rem !important;
            padding: 0.25rem !important;
            font-size: 1.2rem !important;
            border-radius: 50% !important;
        }

        /* === ЗАГОЛОВКИ И ТЕКСТ === */
        .stMarkdown h1, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4, .stMarkdown h5, .stMarkdown h6 {
            color: #fafafa !important;
        }

        .stMarkdown p {
            color: #fafafa !important;
        }

        .stCaption {
            color: #b8bcc8 !important;
        }

        /* === БОКОВАЯ ПАНЕЛЬ === */
        .stSidebar .stMarkdown {
            color: #fafafa !important;
        }

        .stSidebar .stMarkdown h1,
        .stSidebar .stMarkdown h2,
        .stSidebar .stMarkdown h3,
        .stSidebar .stMarkdown h4,
        .stSidebar .stMarkdown h5,
        .stSidebar .stMarkdown h6 {
            color: #fafafa !important;
        }

        .stSidebar .stMarkdown p,
        .stSidebar .stMarkdown div,
        .stSidebar .stMarkdown span {
            color: #fafafa !important;
        }

        .stSidebar .stSelectbox label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stSidebar .stButton > button {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
        }

        .stSidebar .stButton > button:hover {
            background-color: #505050 !important;
            color: #ffffff !important;
        }

        .stSidebar .stTextInput label,
        .stSidebar .stNumberInput label,
        .stSidebar .stTextArea label,
        .stSidebar .stDateInput label,
        .stSidebar .stTimeInput label,
        .stSidebar .stSlider label,
        .stSidebar .stCheckbox label,
        .stSidebar .stRadio label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stSidebar .stCaption {
            color: #b8bcc8 !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ ЭЛЕМЕНТЫ === */
        .stCode {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        .stJson {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ ЭЛЕМЕНТЫ === */
        .stMultiSelect > div > div {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            color: #fafafa !important;
        }

        .stMultiSelect label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stFileUploader > div {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
            color: #fafafa !important;
        }

        .stFileUploader label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        .stColorPicker > div > div {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
        }

        .stColorPicker label {
            color: #fafafa !important;
            font-weight: 500 !important;
        }

        /* === ИСПРАВЛЕНИЕ КОНТРАСТНОСТИ === */
        div[data-testid="stSidebar"] {
            background-color: #262730 !important;
        }

        div[data-testid="stSidebar"] > div {
            background-color: #262730 !important;
        }

        section[data-testid="stSidebar"] {
            background-color: #262730 !important;
        }

        div[data-testid="stSidebar"] * {
            color: #fafafa !important;
        }

        /* === МАКСИМАЛЬНО АГРЕССИВНЫЕ ГЛОБАЛЬНЫЕ ИСПРАВЛЕНИЯ === */
        .stApp * {
            color: #fafafa !important;
        }

        .stApp .stMarkdown * {
            color: #fafafa !important;
        }

        .stApp label {
            color: #fafafa !important;
        }

        .stApp .stCaption {
            color: #b8bcc8 !important;
        }

        /* === ИСПРАВЛЕНИЯ ДЛЯ ВСЕХ ЭЛЕМЕНТОВ === */
        [data-testid="metric-container"] * {
            color: #fafafa !important;
        }

        [data-testid="metric-container"] .metric-label {
            color: #b8bcc8 !important;
        }

        [data-testid="metric-container"] .metric-value {
            color: #fafafa !important;
        }

        /* === НАВИГАЦИОННЫЕ ЭЛЕМЕНТЫ === */
        .element-container * {
            color: #fafafa !important;
        }

        .row-widget * {
            color: #fafafa !important;
        }

        .widget-label * {
            color: #fafafa !important;
        }

        /* === МАКСИМАЛЬНО АГРЕССИВНЫЕ СЕЛЕКТОРЫ ДЛЯ ВСЕХ ФОРМ === */
        .stSelectbox, .stSelectbox * {
            color: #fafafa !important;
        }

        .stTextInput, .stTextInput * {
            color: #fafafa !important;
        }

        .stTextArea, .stTextArea * {
            color: #fafafa !important;
        }

        .stNumberInput, .stNumberInput * {
            color: #fafafa !important;
        }

        .stSlider, .stSlider * {
            color: #fafafa !important;
        }

        .stCheckbox, .stCheckbox * {
            color: #fafafa !important;
        }

        .stRadio, .stRadio * {
            color: #fafafa !important;
        }

        .stMultiSelect, .stMultiSelect * {
            color: #fafafa !important;
        }

        .stDateInput, .stDateInput * {
            color: #fafafa !important;
        }

        .stTimeInput, .stTimeInput * {
            color: #fafafa !important;
        }

        /* === АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ ИНПУТОВ === */
        input, textarea, select {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        input:focus, textarea:focus, select:focus {
            border-color: #06b6d4 !important;
            box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
        }

        /* === СТИЛИ ДЛЯ ЭКСПАНДЕРОВ === */
        .streamlit-expanderHeader, .streamlit-expanderHeader * {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        .streamlit-expanderContent, .streamlit-expanderContent *:not(button):not(.stButton):not(.stButton *) {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ ЭЛЕМЕНТОВ ВНУТРИ ЭКСПАНДЕРОВ === */
        [data-testid="stExpander"] div,
        [data-testid="stExpander"] span,
        [data-testid="stExpander"] p,
        [data-testid="stExpander"] h1,
        [data-testid="stExpander"] h2,
        [data-testid="stExpander"] h3,
        [data-testid="stExpander"] h4,
        [data-testid="stExpander"] h5,
        [data-testid="stExpander"] h6,
        details div,
        details span,
        details p,
        details h1,
        details h2,
        details h3,
        details h4,
        details h5,
        details h6 {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === ДОПОЛНИТЕЛЬНЫЕ АГРЕССИВНЫЕ СТИЛИ ДЛЯ SELECTBOX === */
        .stSelectbox div[data-baseweb="select"] {
            background-color: #1e2130 !important;
            color: #fafafa !important;
            border: 1px solid #404040 !important;
        }

        .stSelectbox div[data-baseweb="select"] * {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        /* === СТИЛИ ДЛЯ ВСЕХ ВОЗМОЖНЫХ ВЫПАДАЮЩИХ ЭЛЕМЕНТОВ === */
        div[data-baseweb="popover"] {
            background-color: #1e2130 !important;
            border: 1px solid #404040 !important;
        }

        div[data-baseweb="popover"] * {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        ul[data-baseweb="menu"] {
            background-color: #1e2130 !important;
        }

        ul[data-baseweb="menu"] * {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        li[data-baseweb="menu-item"] {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        li[data-baseweb="menu-item"]:hover {
            background-color: #262730 !important;
            color: #ffffff !important;
        }

        /* === СТИЛИ ДЛЯ ВСЕХ DIV ВНУТРИ SELECTBOX === */
        .stSelectbox div {
            background-color: #1e2130 !important;
            color: #fafafa !important;
        }

        .stSelectbox span {
            color: #fafafa !important;
        }

        /* === ИСПРАВЛЕНИЕ ВЕРХНЕЙ ПАНЕЛИ === */
        .stApp > div:first-child {
            background-color: #262730 !important;
        }

        .stApp > div:first-child * {
            color: #fafafa !important;
        }

        /* === ИСПРАВЛЕНИЕ ВСЕХ КОНТЕЙНЕРОВ === */
        div[data-testid="stVerticalBlock"] * {
            color: #fafafa !important;
        }

        div[data-testid="stHorizontalBlock"] * {
            color: #fafafa !important;
        }

        div[data-testid="column"] * {
            color: #fafafa !important;
        }

        /* === ФИНАЛЬНЫЕ АГРЕССИВНЫЕ СТИЛИ ДЛЯ ВСЕХ КНОПОК === */
        /* Перехватываем все возможные селекторы кнопок */
        button,
        .stButton button,
        .stButton > button,
        div[data-testid="stButton"] button,
        div[data-testid="stButton"] > button,
        .st-emotion-cache-8atqhb button,
        .st-emotion-cache-15f0lx0,
        button[data-testid="stBaseButton-secondary"],
        button[kind="secondary"],
        button[class*="st-emotion-cache"],
        button[class*="stButton"],
        button[class*="Button"],
        [class*="st-emotion-cache"] button,
        [class*="stButton"] button,
        [class*="Button"] button,
        .element-container button,
        .stApp button,
        .stDownloadButton button,
        .stFormSubmitButton button {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
            min-height: 2.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            transition: all 0.2s ease !important;
            font-weight: 500 !important;
        }

        /* Hover состояния для всех кнопок */
        button:hover,
        .stButton button:hover,
        .stButton > button:hover,
        div[data-testid="stButton"] button:hover,
        div[data-testid="stButton"] > button:hover,
        .st-emotion-cache-8atqhb button:hover,
        .st-emotion-cache-15f0lx0:hover,
        button[data-testid="stBaseButton-secondary"]:hover,
        button[kind="secondary"]:hover,
        button[class*="st-emotion-cache"]:hover,
        button[class*="stButton"]:hover,
        button[class*="Button"]:hover,
        [class*="st-emotion-cache"] button:hover,
        [class*="stButton"] button:hover,
        [class*="Button"] button:hover,
        .element-container button:hover,
        .stApp button:hover,
        .stDownloadButton button:hover,
        .stFormSubmitButton button:hover {
            background-color: #505050 !important;
            color: #ffffff !important;
            border: 1px solid #707070 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4) !important;
        }

        /* Активные состояния для всех кнопок */
        button:active,
        .stButton button:active,
        .stButton > button:active,
        div[data-testid="stButton"] button:active,
        div[data-testid="stButton"] > button:active,
        .st-emotion-cache-8atqhb button:active,
        .st-emotion-cache-15f0lx0:active,
        button[data-testid="stBaseButton-secondary"]:active,
        button[kind="secondary"]:active {
            transform: translateY(0px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
            background-color: #454545 !important;
        }

        /* === СПЕЦИАЛЬНЫЕ ПРАВИЛА ДЛЯ КНОПОК В ЭКСПАНДЕРАХ === */
        [data-testid="stExpander"] button,
        [data-testid="stExpander"] .stButton button,
        [data-testid="stExpander"] .stButton > button,
        [data-testid="stExpander"] div[data-testid="stButton"] button,
        [data-testid="stExpander"] .st-emotion-cache-8atqhb button,
        [data-testid="stExpander"] .st-emotion-cache-15f0lx0,
        [data-testid="stExpander"] button[data-testid="stBaseButton-secondary"],
        [data-testid="stExpander"] button[kind="secondary"],
        details button,
        details .stButton button,
        details .stButton > button,
        details div[data-testid="stButton"] button,
        details .st-emotion-cache-8atqhb button,
        details .st-emotion-cache-15f0lx0,
        details button[data-testid="stBaseButton-secondary"],
        details button[kind="secondary"] {
            background-color: #404040 !important;
            color: #fafafa !important;
            border: 1px solid #606060 !important;
            min-height: 2.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
        }

        /* Hover для кнопок в экспандерах */
        [data-testid="stExpander"] button:hover,
        [data-testid="stExpander"] .stButton button:hover,
        [data-testid="stExpander"] .stButton > button:hover,
        [data-testid="stExpander"] div[data-testid="stButton"] button:hover,
        [data-testid="stExpander"] .st-emotion-cache-8atqhb button:hover,
        [data-testid="stExpander"] .st-emotion-cache-15f0lx0:hover,
        [data-testid="stExpander"] button[data-testid="stBaseButton-secondary"]:hover,
        [data-testid="stExpander"] button[kind="secondary"]:hover,
        details button:hover,
        details .stButton button:hover,
        details .stButton > button:hover,
        details div[data-testid="stButton"] button:hover,
        details .st-emotion-cache-8atqhb button:hover,
        details .st-emotion-cache-15f0lx0:hover,
        details button[data-testid="stBaseButton-secondary"]:hover,
        details button[kind="secondary"]:hover {
            background-color: #505050 !important;
            color: #ffffff !important;
            border: 1px solid #707070 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.4) !important;
        }
        </style>

        <script>
        // JavaScript для принудительного применения темной темы
        function applyDarkThemeForce() {
            // Исправляем верхнюю панель
            const headers = document.querySelectorAll('header, [data-testid="stHeader"], [data-testid="stToolbar"], [data-testid="stDecoration"]');
            headers.forEach(header => {
                header.style.backgroundColor = '#262730';
                header.style.color = '#fafafa';
                const headerElements = header.querySelectorAll('*');
                headerElements.forEach(el => {
                    el.style.color = '#fafafa';
                });
            });

            // Исправляем все кнопки - максимально агрессивно
            const buttonSelectors = [
                'button',
                '.stButton button',
                '.stButton > button',
                'div[data-testid="stButton"] button',
                'div[data-testid="stButton"] > button',
                '.st-emotion-cache-8atqhb button',
                '.st-emotion-cache-15f0lx0',
                'button[data-testid="stBaseButton-secondary"]',
                'button[kind="secondary"]',
                'button[class*="st-emotion-cache"]',
                'button[class*="stButton"]',
                'button[class*="Button"]',
                '[class*="st-emotion-cache"] button',
                '[class*="stButton"] button',
                '[class*="Button"] button',
                '.element-container button',
                '.stApp button',
                '.stDownloadButton button',
                '.stFormSubmitButton button'
            ];

            buttonSelectors.forEach(selector => {
                const buttons = document.querySelectorAll(selector);
                buttons.forEach(button => {
                    button.style.setProperty('background-color', '#404040', 'important');
                    button.style.setProperty('color', '#fafafa', 'important');
                    button.style.setProperty('border', '1px solid #606060', 'important');
                    button.style.setProperty('min-height', '2.5rem', 'important');
                    button.style.setProperty('padding', '0.5rem 1rem', 'important');
                    button.style.setProperty('border-radius', '6px', 'important');
                    button.style.setProperty('font-weight', '500', 'important');
                });
            });

            // Исправляем все инпуты и селекты
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.style.backgroundColor = '#1e2130';
                input.style.color = '#fafafa';
                input.style.border = '1px solid #404040';
            });

            // Исправляем все лейблы
            const labels = document.querySelectorAll('label');
            labels.forEach(label => {
                label.style.color = '#fafafa';
            });

            // Исправляем все опции в селектах
            const options = document.querySelectorAll('option');
            options.forEach(option => {
                option.style.backgroundColor = '#1e2130';
                option.style.color = '#fafafa';
            });

            // Исправляем выпадающие меню
            const dropdowns = document.querySelectorAll('[data-baseweb="popover"], [data-baseweb="menu"], [role="listbox"]');
            dropdowns.forEach(dropdown => {
                dropdown.style.backgroundColor = '#1e2130';
                dropdown.style.border = '1px solid #404040';
                dropdown.style.color = '#fafafa';
            });

            // Исправляем элементы списков
            const listItems = document.querySelectorAll('[data-baseweb="menu"] li, [role="option"]');
            listItems.forEach(item => {
                item.style.backgroundColor = '#1e2130';
                item.style.color = '#fafafa';
            });

            // Исправляем все div внутри выпадающих списков
            const dropdownDivs = document.querySelectorAll('[data-baseweb="popover"] div, [data-baseweb="menu"] div');
            dropdownDivs.forEach(div => {
                div.style.backgroundColor = '#1e2130';
                div.style.color = '#fafafa';
            });

            // Исправляем экспандеры
            const expanders = document.querySelectorAll('[data-testid="stExpander"], details');
            expanders.forEach(expander => {
                expander.style.backgroundColor = '#1e2130';
                expander.style.border = '1px solid #404040';
                expander.style.borderRadius = '6px';

                // Исправляем все элементы внутри экспандера, КРОМЕ кнопок
                const expanderElements = expander.querySelectorAll('*:not(button):not(.stButton):not(.stButton *)');
                expanderElements.forEach(element => {
                    // Проверяем, что это не кнопка и не элемент внутри кнопки
                    if (!element.tagName || element.tagName.toLowerCase() !== 'button') {
                        if (!element.closest('button') && !element.closest('.stButton')) {
                            element.style.backgroundColor = '#1e2130';
                            element.style.color = '#fafafa';
                        }
                    }
                });
            });

            // Исправляем заголовки экспандеров
            const summaries = document.querySelectorAll('summary, .streamlit-expanderHeader');
            summaries.forEach(summary => {
                summary.style.backgroundColor = '#1e2130';
                summary.style.color = '#fafafa';
                summary.style.border = '1px solid #404040';
            });

            // Исправляем содержимое экспандеров
            const expanderContents = document.querySelectorAll('.streamlit-expanderContent');
            expanderContents.forEach(content => {
                content.style.backgroundColor = '#1e2130';
                content.style.color = '#fafafa';

                // Исправляем все дочерние элементы, КРОМЕ кнопок
                const contentElements = content.querySelectorAll('*:not(button):not(.stButton):not(.stButton *)');
                contentElements.forEach(element => {
                    // Проверяем, что это не кнопка и не элемент внутри кнопки
                    if (!element.tagName || element.tagName.toLowerCase() !== 'button') {
                        if (!element.closest('button') && !element.closest('.stButton')) {
                            element.style.backgroundColor = '#1e2130';
                            element.style.color = '#fafafa';
                        }
                    }
                });
            });

            // Исправляем весь текст
            const textElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6, label');
            textElements.forEach(el => {
                if (el.tagName !== 'SCRIPT' && el.tagName !== 'STYLE') {
                    el.style.color = '#fafafa';
                }
            });

            // Исправляем основной контейнер
            const app = document.querySelector('.stApp');
            if (app) {
                app.style.backgroundColor = '#0e1117';
                app.style.color = '#fafafa';
            }

            // Исправляем боковую панель
            const sidebar = document.querySelector('.stSidebar');
            if (sidebar) {
                sidebar.style.backgroundColor = '#262730';
                sidebar.style.color = '#fafafa';
            }
        }

        // Применяем стили сразу и через интервалы
        applyDarkThemeForce();
        setTimeout(applyDarkThemeForce, 100);
        setTimeout(applyDarkThemeForce, 500);
        setTimeout(applyDarkThemeForce, 1000);

        // Специальная функция для исправления выпадающих списков и экспандеров
        function fixDropdownsAndExpanders() {
            // Ищем все возможные селекторы выпадающих списков
            const dropdownSelectors = [
                '[data-baseweb="popover"]',
                '[data-baseweb="menu"]',
                '[role="listbox"]',
                '.stSelectbox div[data-baseweb="select"]',
                'ul[data-baseweb="menu"]',
                'li[data-baseweb="menu-item"]',
                '[role="option"]'
            ];

            dropdownSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.backgroundColor = '#1e2130';
                    element.style.color = '#fafafa';
                    element.style.border = '1px solid #404040';

                    // Исправляем все дочерние элементы
                    const children = element.querySelectorAll('*');
                    children.forEach(child => {
                        child.style.backgroundColor = '#1e2130';
                        child.style.color = '#fafafa';
                    });
                });
            });

            // Ищем все возможные селекторы экспандеров
            const expanderSelectors = [
                '[data-testid="stExpander"]',
                'details',
                'summary',
                '.streamlit-expanderHeader',
                '.streamlit-expanderContent'
            ];

            expanderSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.backgroundColor = '#1e2130';
                    element.style.color = '#fafafa';
                    element.style.border = '1px solid #404040';

                    // Исправляем все дочерние элементы
                    const children = element.querySelectorAll('*');
                    children.forEach(child => {
                        child.style.backgroundColor = '#1e2130';
                        child.style.color = '#fafafa';
                    });
                });
            });
        }

        // Наблюдатель за изменениями DOM с дополнительной проверкой выпадающих списков и экспандеров
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    setTimeout(applyDarkThemeForce, 50);
                    setTimeout(fixDropdownsAndExpanders, 100);
                    setTimeout(fixButtonsSpecifically, 150);
                    setTimeout(fixButtonInnerElements, 200);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Специальная функция для исправления кнопок
        function fixButtonsSpecifically() {
            // Находим все кнопки с проблемными классами
            const problematicButtons = document.querySelectorAll(
                'button[kind="secondary"][data-testid="stBaseButton-secondary"].st-emotion-cache-15f0lx0, ' +
                '.st-emotion-cache-8atqhb button.st-emotion-cache-15f0lx0, ' +
                'button.st-emotion-cache-15f0lx0'
            );

            problematicButtons.forEach(button => {
                button.style.setProperty('background-color', '#404040', 'important');
                button.style.setProperty('color', '#fafafa', 'important');
                button.style.setProperty('border', '1px solid #606060', 'important');
                button.style.setProperty('min-height', '2.5rem', 'important');
                button.style.setProperty('padding', '0.5rem 1rem', 'important');
                button.style.setProperty('border-radius', '6px', 'important');
                button.style.setProperty('font-weight', '500', 'important');

                // КРИТИЧЕСКИ ВАЖНО: Исправляем все элементы внутри кнопки
                const innerElements = button.querySelectorAll('p, div, span');
                innerElements.forEach(element => {
                    element.style.setProperty('background-color', 'transparent', 'important');
                    element.style.setProperty('color', 'inherit', 'important');
                });

                // Специально исправляем markdown контейнеры
                const markdownContainers = button.querySelectorAll('[data-testid="stMarkdownContainer"], .st-emotion-cache-v2zoge');
                markdownContainers.forEach(container => {
                    container.style.setProperty('background-color', 'transparent', 'important');
                    container.style.setProperty('color', 'inherit', 'important');

                    // Исправляем параграфы внутри markdown контейнеров
                    const paragraphs = container.querySelectorAll('p');
                    paragraphs.forEach(p => {
                        p.style.setProperty('background-color', 'transparent', 'important');
                        p.style.setProperty('color', 'inherit', 'important');
                    });
                });

                // Добавляем обработчик hover
                button.addEventListener('mouseenter', function() {
                    this.style.setProperty('background-color', '#505050', 'important');
                    this.style.setProperty('color', '#ffffff', 'important');
                    this.style.setProperty('border', '1px solid #707070', 'important');
                });

                button.addEventListener('mouseleave', function() {
                    this.style.setProperty('background-color', '#404040', 'important');
                    this.style.setProperty('color', '#fafafa', 'important');
                    this.style.setProperty('border', '1px solid #606060', 'important');
                });
            });
        }

        // Дополнительная функция для исправления всех элементов внутри кнопок
        function fixButtonInnerElements() {
            const allButtons = document.querySelectorAll('button, .stButton button');
            allButtons.forEach(button => {
                const innerElements = button.querySelectorAll('p, div, span, [data-testid="stMarkdownContainer"]');
                innerElements.forEach(element => {
                    element.style.setProperty('background-color', 'transparent', 'important');
                    element.style.setProperty('color', 'inherit', 'important');
                });
            });
        }

        // Дополнительно запускаем исправление выпадающих списков и экспандеров
        setTimeout(fixDropdownsAndExpanders, 200);
        setTimeout(fixDropdownsAndExpanders, 500);
        setTimeout(fixDropdownsAndExpanders, 1000);

        // Запускаем специальное исправление кнопок
        setTimeout(fixButtonsSpecifically, 100);
        setTimeout(fixButtonsSpecifically, 300);
        setTimeout(fixButtonsSpecifically, 600);
        setTimeout(fixButtonsSpecifically, 1200);

        // Запускаем исправление внутренних элементов кнопок
        setTimeout(fixButtonInnerElements, 150);
        setTimeout(fixButtonInnerElements, 400);
        setTimeout(fixButtonInnerElements, 800);
        setTimeout(fixButtonInnerElements, 1500);
        </script>
        """, unsafe_allow_html=True)
    
    def _apply_light_theme(self) -> None:
        """Применяет улучшенные стили светлой темы"""
        st.markdown("""
        <style>
        /* === ОСНОВНЫЕ КОНТЕЙНЕРЫ === */
        .stApp {
            background-color: #ffffff !important;
            color: #262730 !important;
        }

        .stSidebar {
            background-color: #f8f9fa !important;
        }

        .stSidebar .stSidebar-content {
            background-color: #f8f9fa !important;
        }

        /* === МЕТРИКИ === */
        .stMetric {
            background-color: #ffffff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        }

        .stMetric [data-testid="metric-container"] {
            background-color: #ffffff !important;
            border: 1px solid #e1e5e9 !important;
            padding: 1rem !important;
            border-radius: 0.5rem !important;
            color: #262730 !important;
        }

        .stMetric [data-testid="metric-container"] > div {
            color: #262730 !important;
        }

        .stMetric label {
            color: #6b7280 !important;
        }

        .stMetric [data-testid="metric-container"] [data-testid="metric-value"] {
            color: #262730 !important;
            font-weight: 600 !important;
        }

        .stMetric [data-testid="metric-container"] [data-testid="metric-delta"] {
            color: #10b981 !important;
        }

        /* === АЛЕРТЫ И УВЕДОМЛЕНИЯ === */
        .stAlert {
            background-color: #ffffff !important;
            border: 1px solid #e1e5e9 !important;
            color: #262730 !important;
        }

        .stAlert > div {
            background-color: transparent !important;
            color: #262730 !important;
        }

        .stSuccess {
            background-color: #f0fdf4 !important;
            border-left: 4px solid #10b981 !important;
            color: #065f46 !important;
        }

        .stInfo {
            background-color: #f0f9ff !important;
            border-left: 4px solid #06b6d4 !important;
            color: #0c4a6e !important;
        }

        .stWarning {
            background-color: #fffbeb !important;
            border-left: 4px solid #f59e0b !important;
            color: #92400e !important;
        }

        .stError {
            background-color: #fef2f2 !important;
            border-left: 4px solid #ef4444 !important;
            color: #991b1b !important;
        }

        /* === ТАБЛИЦЫ И DATAFRAMES === */
        .stDataFrame {
            background-color: #ffffff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
        }

        .stDataFrame [data-testid="stDataFrame"] {
            background-color: #ffffff !important;
        }

        .stDataFrame table {
            background-color: #ffffff !important;
            color: #262730 !important;
        }

        .stDataFrame thead tr th {
            background-color: #f8f9fa !important;
            color: #262730 !important;
            border-bottom: 1px solid #e1e5e9 !important;
        }

        .stDataFrame tbody tr td {
            background-color: #ffffff !important;
            color: #262730 !important;
            border-bottom: 1px solid #f1f3f4 !important;
        }

        .stDataFrame tbody tr:hover td {
            background-color: #f8f9fa !important;
        }

        /* === КНОПКИ === */
        .stButton > button {
            background-color: #ffffff !important;
            color: #262730 !important;
            border: 1px solid #d1d5db !important;
            min-height: 2.5rem !important;
            padding: 0.25rem 0.75rem !important;
            border-radius: 6px !important;
            transition: all 0.2s ease !important;
        }

        .stButton > button:hover {
            background-color: #f9fafb !important;
            border: 1px solid #9ca3af !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .stButton > button:active {
            transform: translateY(0px) !important;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        }

        .stButton > button:disabled {
            background-color: #f5f5f5 !important;
            color: #9ca3af !important;
            border: 1px solid #e5e7eb !important;
            cursor: not-allowed !important;
        }

        /* === ФОРМЫ И ИНПУТЫ (СВЕТЛАЯ ТЕМА) === */
        .stTextInput > div > div > input {
            background-color: #ffffff !important;
            color: #262730 !important;
            border: 1px solid #d1d5db !important;
            border-radius: 6px !important;
            padding: 0.5rem !important;
        }

        .stTextInput > div > div > input:focus {
            border-color: #06b6d4 !important;
            box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
        }

        .stTextInput label {
            color: #262730 !important;
            font-weight: 500 !important;
        }

        .stSelectbox > div > div {
            background-color: #ffffff !important;
            color: #262730 !important;
            border: 1px solid #d1d5db !important;
        }

        .stSelectbox label {
            color: #262730 !important;
            font-weight: 500 !important;
        }

        .stTextArea > div > div > textarea {
            background-color: #ffffff !important;
            color: #262730 !important;
            border: 1px solid #d1d5db !important;
        }

        .stTextArea label {
            color: #262730 !important;
            font-weight: 500 !important;
        }

        .stNumberInput > div > div > input {
            background-color: #ffffff !important;
            color: #262730 !important;
            border: 1px solid #d1d5db !important;
        }

        .stNumberInput label {
            color: #262730 !important;
            font-weight: 500 !important;
        }

        /* === КОМПАКТНЫЕ КНОПКИ ТЕМЫ === */
        button[title="Светлая тема"], button[title="Темная тема"] {
            width: 2.5rem !important;
            height: 2.5rem !important;
            padding: 0.25rem !important;
            font-size: 1.2rem !important;
            border-radius: 50% !important;
        }
        </style>
        """, unsafe_allow_html=True)


# Глобальный экземпляр переключателя темы
theme_switcher = ThemeSwitcher()
