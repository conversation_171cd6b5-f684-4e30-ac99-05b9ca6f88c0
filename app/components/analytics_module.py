"""
Advanced Analytics Module
Provides comprehensive analytics, forecasting, and insights for the dashboard
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
try:
    from sklearn.linear_model import LinearRegression
    from sklearn.preprocessing import PolynomialFeatures
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class AdvancedAnalytics:
    """Advanced analytics and forecasting component"""
    
    def __init__(self):
        """Initialize analytics module"""
        self.forecast_periods = 6  # months
        self.confidence_level = 0.95

        if not SKLEARN_AVAILABLE:
            logger.warning("scikit-learn not available, using simplified forecasting")

    def _apply_theme_to_figure(self, fig, title: str = "") -> None:
        """Применяет текущую тему к графику Plotly"""
        current_theme = st.session_state.get('theme', 'light')

        if current_theme == 'dark':
            # Темная тема для графиков
            fig.update_layout(
                title={
                    'text': title,
                    'font': {'size': 18, 'family': 'Arial, sans-serif', 'color': '#fafafa'}
                },
                paper_bgcolor='#1e2130',
                plot_bgcolor='#1e2130',
                font={'color': '#fafafa'},
                legend={'font': {'color': '#fafafa'}},
                xaxis={
                    'gridcolor': '#404040',
                    'linecolor': '#404040',
                    'tickcolor': '#404040',
                    'tickfont': {'color': '#fafafa'},
                    'titlefont': {'color': '#fafafa'}
                },
                yaxis={
                    'gridcolor': '#404040',
                    'linecolor': '#404040',
                    'tickcolor': '#404040',
                    'tickfont': {'color': '#fafafa'},
                    'titlefont': {'color': '#fafafa'}
                }
            )
        else:
            # Светлая тема для графиков
            fig.update_layout(
                title={
                    'text': title,
                    'font': {'size': 18, 'family': 'Arial, sans-serif', 'color': '#262730'}
                },
                paper_bgcolor='#ffffff',
                plot_bgcolor='#ffffff',
                font={'color': '#262730'},
                legend={'font': {'color': '#262730'}},
                xaxis={
                    'gridcolor': '#e1e5e9',
                    'linecolor': '#e1e5e9',
                    'tickcolor': '#e1e5e9',
                    'tickfont': {'color': '#262730'},
                    'titlefont': {'color': '#262730'}
                },
                yaxis={
                    'gridcolor': '#e1e5e9',
                    'linecolor': '#e1e5e9',
                    'tickcolor': '#e1e5e9',
                    'tickfont': {'color': '#262730'},
                    'titlefont': {'color': '#262730'}
                }
            )

    def render_analytics_dashboard(self, contracts_df: pd.DataFrame) -> None:
        """Render comprehensive analytics dashboard"""
        try:
            st.markdown("## 🧠 Расширенная аналитика")
            
            if contracts_df.empty:
                st.warning("Недостаточно данных для аналитики")
                return
            
            # Create analytics tabs
            tab1, tab2, tab3, tab4 = st.tabs([
                "📈 Прогнозирование", 
                "🎯 Анализ эффективности", 
                "💡 Инсайты", 
                "📊 Сравнительный анализ"
            ])
            
            with tab1:
                self._render_forecasting_analysis(contracts_df)
            
            with tab2:
                self._render_performance_analysis(contracts_df)
            
            with tab3:
                self._render_insights_analysis(contracts_df)
            
            with tab4:
                self._render_comparative_analysis(contracts_df)
                
        except Exception as e:
            logger.error(f"Error in analytics dashboard: {e}")
            st.error("Ошибка в модуле аналитики")
    
    def _render_forecasting_analysis(self, df: pd.DataFrame) -> None:
        """Render forecasting analysis"""
        try:
            st.markdown("### 🔮 Прогнозирование трендов")
            
            # Prepare time series data
            if 'start_date' not in df.columns:
                st.warning("Нет данных о датах для прогнозирования")
                return
            
            # Monthly aggregation
            df_time = df.copy()
            df_time['start_date'] = pd.to_datetime(df_time['start_date'])
            df_time['month'] = df_time['start_date'].dt.to_period('M')
            
            monthly_data = df_time.groupby('month').agg({
                'contract_id': 'count',
                'total_amount': 'sum'
            }).reset_index()
            
            if len(monthly_data) < 3:
                st.warning("Недостаточно исторических данных для прогнозирования")
                return
            
            # Create forecasts
            col1, col2 = st.columns(2)
            
            with col1:
                self._create_contract_count_forecast(monthly_data)
            
            with col2:
                self._create_amount_forecast(monthly_data)
            
            # Forecast summary
            self._render_forecast_summary(monthly_data)
            
        except Exception as e:
            logger.error(f"Error in forecasting analysis: {e}")
            st.error("Ошибка прогнозирования")
    
    def _create_contract_count_forecast(self, monthly_data: pd.DataFrame) -> None:
        """Create contract count forecast"""
        try:
            # Prepare data for forecasting
            monthly_data['month_num'] = range(len(monthly_data))

            if SKLEARN_AVAILABLE:
                # Use scikit-learn for advanced forecasting
                X = monthly_data[['month_num']].values
                y = monthly_data['contract_id'].values

                model = LinearRegression()
                model.fit(X, y)

                # Generate future months
                future_months = range(len(monthly_data), len(monthly_data) + self.forecast_periods)
                future_X = np.array(future_months).reshape(-1, 1)
                forecast = model.predict(future_X)
            else:
                # Simple linear trend calculation
                y_values = monthly_data['contract_id'].values
                x_values = monthly_data['month_num'].values

                # Calculate simple linear trend
                if len(y_values) >= 2:
                    slope = (y_values[-1] - y_values[0]) / (x_values[-1] - x_values[0]) if x_values[-1] != x_values[0] else 0
                    intercept = y_values[-1] - slope * x_values[-1]

                    # Generate forecast
                    future_months = range(len(monthly_data), len(monthly_data) + self.forecast_periods)
                    forecast = [slope * month + intercept for month in future_months]
                else:
                    # Fallback to last value
                    forecast = [y_values[-1]] * self.forecast_periods
            
            # Create visualization
            fig = go.Figure()
            
            # Historical data
            fig.add_trace(go.Scatter(
                x=monthly_data['month'].astype(str),
                y=monthly_data['contract_id'],
                mode='lines+markers',
                name='Исторические данные',
                line=dict(color='#3b82f6', width=3)
            ))
            
            # Forecast
            future_months_str = [f"2024-{i:02d}" for i in range(1, self.forecast_periods + 1)]
            fig.add_trace(go.Scatter(
                x=future_months_str,
                y=forecast,
                mode='lines+markers',
                name='Прогноз',
                line=dict(color='#ef4444', width=3, dash='dash')
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, "Прогноз количества договоров")

            fig.update_layout(
                xaxis_title="Месяц",
                yaxis_title="Количество договоров",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating contract count forecast: {e}")
            st.error("Ошибка прогноза количества договоров")
    
    def _create_amount_forecast(self, monthly_data: pd.DataFrame) -> None:
        """Create amount forecast"""
        try:
            # Prepare data
            monthly_data['month_num'] = range(len(monthly_data))

            if SKLEARN_AVAILABLE:
                # Use scikit-learn for polynomial regression
                X = monthly_data[['month_num']].values
                y = monthly_data['total_amount'].values

                poly_features = PolynomialFeatures(degree=2)
                X_poly = poly_features.fit_transform(X)

                model = LinearRegression()
                model.fit(X_poly, y)

                # Generate forecast
                future_months = range(len(monthly_data), len(monthly_data) + self.forecast_periods)
                future_X = np.array(future_months).reshape(-1, 1)
                future_X_poly = poly_features.transform(future_X)
                forecast = model.predict(future_X_poly)
            else:
                # Simple moving average forecast
                y_values = monthly_data['total_amount'].values

                if len(y_values) >= 3:
                    # Use last 3 months average as trend
                    recent_avg = np.mean(y_values[-3:])
                    trend = (y_values[-1] - y_values[-3]) / 2 if len(y_values) >= 3 else 0

                    # Generate forecast with trend
                    forecast = [recent_avg + trend * i for i in range(1, self.forecast_periods + 1)]
                else:
                    # Fallback to last value
                    forecast = [y_values[-1]] * self.forecast_periods
            
            # Create visualization
            fig = go.Figure()
            
            # Historical data
            fig.add_trace(go.Scatter(
                x=monthly_data['month'].astype(str),
                y=monthly_data['total_amount'],
                mode='lines+markers',
                name='Исторические данные',
                line=dict(color='#10b981', width=3)
            ))
            
            # Forecast
            future_months_str = [f"2024-{i:02d}" for i in range(1, self.forecast_periods + 1)]
            fig.add_trace(go.Scatter(
                x=future_months_str,
                y=forecast,
                mode='lines+markers',
                name='Прогноз',
                line=dict(color='#f59e0b', width=3, dash='dash')
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, "Прогноз суммы договоров")

            fig.update_layout(
                xaxis_title="Месяц",
                yaxis_title="Сумма (₽)",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating amount forecast: {e}")
            st.error("Ошибка прогноза сумм")
    
    def _render_forecast_summary(self, monthly_data: pd.DataFrame) -> None:
        """Render forecast summary"""
        try:
            st.markdown("#### 📋 Сводка прогноза")
            
            # Calculate trends
            recent_avg_contracts = monthly_data['contract_id'].tail(3).mean()
            recent_avg_amount = monthly_data['total_amount'].tail(3).mean()
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric(
                    "Прогноз договоров/месяц",
                    f"{recent_avg_contracts:.0f}",
                    f"±{recent_avg_contracts * 0.1:.0f}"
                )
            
            with col2:
                st.metric(
                    "Прогноз суммы/месяц",
                    f"{recent_avg_amount/1_000_000:.1f}М ₽",
                    f"±{recent_avg_amount * 0.15/1_000_000:.1f}М"
                )
            
            with col3:
                growth_rate = ((monthly_data['contract_id'].iloc[-1] / monthly_data['contract_id'].iloc[0]) - 1) * 100
                st.metric(
                    "Темп роста",
                    f"{growth_rate:.1f}%",
                    "за период"
                )
            
            with col4:
                confidence = 85  # Mock confidence level
                st.metric(
                    "Достоверность",
                    f"{confidence}%",
                    "прогноза"
                )
            
        except Exception as e:
            logger.error(f"Error in forecast summary: {e}")
    
    def _render_performance_analysis(self, df: pd.DataFrame) -> None:
        """Render performance analysis"""
        try:
            st.markdown("### ⚡ Анализ эффективности")
            
            # Performance metrics
            col1, col2 = st.columns(2)
            
            with col1:
                self._create_efficiency_radar_chart(df)
            
            with col2:
                self._create_performance_heatmap(df)
            
            # Performance trends
            self._create_performance_trends(df)
            
        except Exception as e:
            logger.error(f"Error in performance analysis: {e}")
            st.error("Ошибка анализа эффективности")
    
    def _create_efficiency_radar_chart(self, df: pd.DataFrame) -> None:
        """Create efficiency radar chart"""
        try:
            # Calculate efficiency metrics
            total_contracts = len(df)
            active_rate = len(df[df['status'] == 'active']) / total_contracts * 100 if total_contracts > 0 else 0
            completion_rate = len(df[df['status'] == 'completed']) / total_contracts * 100 if total_contracts > 0 else 0
            overdue_rate = len(df[df['status'] == 'overdue']) / total_contracts * 100 if total_contracts > 0 else 0
            
            # Mock additional metrics
            quality_score = 85
            client_satisfaction = 78
            time_efficiency = 92
            
            categories = ['Активность', 'Завершение', 'Качество', 'Удовлетворенность', 'Время', 'Просрочки']
            values = [active_rate, completion_rate, quality_score, client_satisfaction, time_efficiency, 100-overdue_rate]
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name='Текущие показатели',
                line_color='#3b82f6'
            ))
            
            # Add benchmark line
            benchmark = [80, 75, 85, 80, 85, 90]
            fig.add_trace(go.Scatterpolar(
                r=benchmark,
                theta=categories,
                fill='toself',
                name='Целевые показатели',
                line_color='#10b981',
                opacity=0.6
            ))
            
            # Применяем тему к графику
            current_theme = st.session_state.get('theme', 'light')

            if current_theme == 'dark':
                fig.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 100],
                            gridcolor='#404040',
                            linecolor='#404040'
                        ),
                        angularaxis=dict(
                            gridcolor='#404040',
                            linecolor='#404040',
                            tickfont={'color': '#fafafa'}
                        )
                    ),
                    title={
                        'text': "Радар эффективности",
                        'font': {'color': '#fafafa'}
                    },
                    height=400,
                    paper_bgcolor='#1e2130',
                    plot_bgcolor='#1e2130',
                    font={'color': '#fafafa'},
                    legend={'font': {'color': '#fafafa'}}
                )
            else:
                fig.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 100]
                        )),
                    title="Радар эффективности",
                    height=400,
                    paper_bgcolor='#ffffff',
                    plot_bgcolor='#ffffff',
                    font={'color': '#262730'}
                )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating radar chart: {e}")
    
    def _create_performance_heatmap(self, df: pd.DataFrame) -> None:
        """Create performance heatmap"""
        try:
            # Mock performance data by month and type
            months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн']
            contract_types = ['Поставка', 'Услуги', 'Работы', 'Аренда']
            
            # Generate mock performance scores
            np.random.seed(42)
            performance_matrix = np.random.randint(60, 100, size=(len(contract_types), len(months)))
            
            fig = go.Figure(data=go.Heatmap(
                z=performance_matrix,
                x=months,
                y=contract_types,
                colorscale='RdYlGn',
                text=performance_matrix,
                texttemplate="%{text}%",
                textfont={"size": 12},
                colorbar=dict(title="Эффективность (%)")
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, "Тепловая карта эффективности")

            fig.update_layout(
                xaxis_title="Месяц",
                yaxis_title="Тип договора",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating heatmap: {e}")
    
    def _create_performance_trends(self, df: pd.DataFrame) -> None:
        """Create performance trends visualization"""
        try:
            st.markdown("#### 📈 Тренды эффективности")
            
            # Mock trend data
            dates = pd.date_range(start='2024-01-01', periods=12, freq='M')
            efficiency_trend = [75, 78, 82, 85, 83, 87, 89, 91, 88, 92, 94, 96]
            quality_trend = [80, 82, 79, 85, 87, 84, 88, 90, 92, 89, 93, 95]
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=dates,
                y=efficiency_trend,
                mode='lines+markers',
                name='Общая эффективность',
                line=dict(color='#3b82f6', width=3)
            ))
            
            fig.add_trace(go.Scatter(
                x=dates,
                y=quality_trend,
                mode='lines+markers',
                name='Качество исполнения',
                line=dict(color='#10b981', width=3)
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, "Динамика показателей эффективности")

            fig.update_layout(
                xaxis_title="Месяц",
                yaxis_title="Показатель (%)",
                height=400,
                hovermode='x unified'
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating performance trends: {e}")
    
    def _render_insights_analysis(self, df: pd.DataFrame) -> None:
        """Render AI-powered insights analysis"""
        try:
            st.markdown("### 🔍 Глубокий анализ и рекомендации")
            
            # Generate comprehensive insights
            insights = self._generate_comprehensive_insights(df)
            
            # Display insights in expandable sections
            for category, category_insights in insights.items():
                with st.expander(f"📊 {category}", expanded=True):
                    for insight in category_insights:
                        self._render_detailed_insight_card(insight)
            
        except Exception as e:
            logger.error(f"Error in insights analysis: {e}")
            st.error("Ошибка анализа инсайтов")
    
    def _generate_comprehensive_insights(self, df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """Generate comprehensive insights from data"""
        insights = {
            "Финансовые показатели": [],
            "Операционная эффективность": [],
            "Риски и возможности": [],
            "Стратегические рекомендации": []
        }
        
        try:
            # Financial insights
            if 'total_amount' in df.columns:
                avg_amount = df['total_amount'].mean()
                median_amount = df['total_amount'].median()
                
                if avg_amount > median_amount * 1.5:
                    insights["Финансовые показатели"].append({
                        'title': 'Концентрация крупных договоров',
                        'description': f'Средняя сумма договора ({avg_amount:,.0f} ₽) значительно превышает медианную ({median_amount:,.0f} ₽)',
                        'impact': 'high',
                        'recommendation': 'Рассмотрите диверсификацию портфеля договоров для снижения концентрационного риска',
                        'action_items': ['Анализ крупных контрагентов', 'Разработка стратегии диверсификации', 'Мониторинг концентрационных рисков']
                    })
            
            # Operational insights
            if 'status' in df.columns:
                overdue_rate = len(df[df['status'] == 'overdue']) / len(df) * 100 if len(df) > 0 else 0
                
                if overdue_rate > 15:
                    insights["Операционная эффективность"].append({
                        'title': 'Высокий уровень просрочек',
                        'description': f'Процент просроченных договоров составляет {overdue_rate:.1f}%',
                        'impact': 'high',
                        'recommendation': 'Необходимо усилить систему контроля исполнения договоров',
                        'action_items': ['Внедрение системы раннего предупреждения', 'Автоматизация напоминаний', 'Анализ причин просрочек']
                    })
            
            # Risk insights
            insights["Риски и возможности"].append({
                'title': 'Сезонность заключения договоров',
                'description': 'Наблюдается неравномерное распределение договоров в течение года',
                'impact': 'medium',
                'recommendation': 'Планируйте ресурсы с учетом сезонных колебаний',
                'action_items': ['Анализ сезонных паттернов', 'Планирование ресурсов', 'Оптимизация рабочих процессов']
            })
            
            # Strategic insights
            insights["Стратегические рекомендации"].append({
                'title': 'Цифровизация процессов',
                'description': 'Внедрение цифровых решений может повысить эффективность на 20-30%',
                'impact': 'high',
                'recommendation': 'Рассмотрите внедрение системы электронного документооборота',
                'action_items': ['Аудит текущих процессов', 'Выбор платформы ЭДО', 'Обучение персонала']
            })
            
        except Exception as e:
            logger.error(f"Error generating insights: {e}")
        
        return insights
    
    def _render_detailed_insight_card(self, insight: Dict[str, Any]) -> None:
        """Render detailed insight card"""
        impact_colors = {
            'high': {'color': '#ef4444', 'bg': '#fef2f2'},
            'medium': {'color': '#f59e0b', 'bg': '#fffbeb'},
            'low': {'color': '#10b981', 'bg': '#ecfdf5'}
        }
        
        colors = impact_colors.get(insight.get('impact', 'medium'), impact_colors['medium'])
        
        card_html = f"""
        <div style="
            border: 1px solid {colors['color']};
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            background: {colors['bg']};
        ">
            <h4 style="color: {colors['color']}; margin: 0 0 8px 0;">
                {insight['title']}
            </h4>
            <p style="margin: 8px 0; color: #374151;">
                {insight['description']}
            </p>
            <div style="
                background: rgba(255,255,255,0.8);
                padding: 12px;
                border-radius: 6px;
                margin: 12px 0;
            ">
                <strong>💡 Рекомендация:</strong> {insight['recommendation']}
            </div>
        """
        
        if 'action_items' in insight:
            card_html += """
            <div style="margin-top: 12px;">
                <strong>📋 План действий:</strong>
                <ul style="margin: 8px 0; padding-left: 20px;">
            """
            for item in insight['action_items']:
                card_html += f"<li>{item}</li>"
            card_html += "</ul></div>"
        
        card_html += "</div>"
        
        st.markdown(card_html, unsafe_allow_html=True)
    
    def _render_comparative_analysis(self, df: pd.DataFrame) -> None:
        """Render comparative analysis"""
        try:
            st.markdown("### 📊 Сравнительный анализ")
            
            # Benchmark comparison
            col1, col2 = st.columns(2)
            
            with col1:
                self._create_benchmark_comparison(df)
            
            with col2:
                self._create_peer_comparison(df)
            
            # Industry analysis
            self._create_industry_analysis(df)
            
        except Exception as e:
            logger.error(f"Error in comparative analysis: {e}")
            st.error("Ошибка сравнительного анализа")
    
    def _create_benchmark_comparison(self, df: pd.DataFrame) -> None:
        """Create benchmark comparison chart"""
        try:
            # Mock benchmark data
            metrics = ['Эффективность', 'Качество', 'Скорость', 'Стоимость']
            current_values = [85, 78, 92, 88]
            benchmark_values = [80, 85, 85, 90]
            industry_avg = [75, 80, 80, 85]
            
            fig = go.Figure()
            
            fig.add_trace(go.Bar(
                name='Текущие показатели',
                x=metrics,
                y=current_values,
                marker_color='#3b82f6'
            ))
            
            fig.add_trace(go.Bar(
                name='Целевые показатели',
                x=metrics,
                y=benchmark_values,
                marker_color='#10b981'
            ))
            
            fig.add_trace(go.Bar(
                name='Среднее по отрасли',
                x=metrics,
                y=industry_avg,
                marker_color='#6b7280'
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, 'Сравнение с бенчмарками')

            fig.update_layout(
                barmode='group',
                height=400,
                yaxis_title='Показатель (%)'
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating benchmark comparison: {e}")
    
    def _create_peer_comparison(self, df: pd.DataFrame) -> None:
        """Create peer comparison visualization"""
        try:
            # Mock peer data
            companies = ['Наша компания', 'Конкурент A', 'Конкурент B', 'Конкурент C', 'Лидер рынка']
            efficiency = [85, 78, 82, 75, 95]
            contract_volume = [100, 85, 120, 90, 150]  # Relative to our company
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=efficiency,
                y=contract_volume,
                mode='markers+text',
                text=companies,
                textposition='top center',
                marker=dict(
                    size=[20, 15, 15, 15, 25],
                    color=['#ef4444', '#6b7280', '#6b7280', '#6b7280', '#10b981'],
                    opacity=0.7
                ),
                name='Компании'
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, 'Позиционирование относительно конкурентов')

            fig.update_layout(
                xaxis_title='Эффективность (%)',
                yaxis_title='Объем договоров (относительно)',
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating peer comparison: {e}")
    
    def _create_industry_analysis(self, df: pd.DataFrame) -> None:
        """Create industry analysis"""
        try:
            st.markdown("#### 🏭 Отраслевой анализ")
            
            # Mock industry trends
            quarters = ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024', 'Q2 2024']
            our_performance = [78, 82, 85, 88, 90, 92]
            industry_avg = [75, 77, 79, 82, 84, 86]
            market_leaders = [88, 90, 92, 94, 95, 97]
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=quarters,
                y=our_performance,
                mode='lines+markers',
                name='Наша компания',
                line=dict(color='#3b82f6', width=3)
            ))
            
            fig.add_trace(go.Scatter(
                x=quarters,
                y=industry_avg,
                mode='lines+markers',
                name='Среднее по отрасли',
                line=dict(color='#6b7280', width=2)
            ))
            
            fig.add_trace(go.Scatter(
                x=quarters,
                y=market_leaders,
                mode='lines+markers',
                name='Лидеры рынка',
                line=dict(color='#10b981', width=2, dash='dash')
            ))
            
            # Применяем тему к графику
            self._apply_theme_to_figure(fig, 'Динамика показателей по отрасли')

            fig.update_layout(
                xaxis_title='Период',
                yaxis_title='Индекс эффективности',
                height=400,
                hovermode='x unified'
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
        except Exception as e:
            logger.error(f"Error creating industry analysis: {e}")
