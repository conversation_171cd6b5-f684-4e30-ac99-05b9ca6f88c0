"""
Компонент метрик дашборда
Отвечает за отображение ключевых показателей
"""
import streamlit as st
import logging
from typing import Dict, Any, Optional

from app.styles.integration import create_metric_card, create_stats_grid

logger = logging.getLogger(__name__)


class MetricsComponent:
    """Компонент отображения метрик"""
    
    def __init__(self):
        """Инициализирует компонент метрик"""
        self.default_metrics = {
            'total_contracts': 0,
            'active_contracts': 0,
            'total_amount': 0,
            'average_amount': 0,
            'overdue_contracts': 0,
            'completed_contracts': 0,
            'overdue_rate': 0
        }
    
    def render(self, summary_data: Dict[str, Any]) -> None:
        """Отрисовывает основные метрики"""
        try:
            if not summary_data:
                summary_data = self.default_metrics
            
            st.markdown("## 📊 Ключевые показатели")
            
            # Основные метрики в первой строке
            self._render_primary_metrics(summary_data)
            
            st.markdown("---")
            
            # Дополнительные метрики во второй строке
            self._render_secondary_metrics(summary_data)
            
            logger.debug("Метрики отрисованы успешно")
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки метрик: {e}")
            self._render_fallback_metrics()
    
    def _render_primary_metrics(self, data: Dict[str, Any]) -> None:
        """Отрисовывает основные метрики"""
        try:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_contracts = int(data.get('total_contracts', 0))
                create_metric_card(
                    value=f"{total_contracts:,}",
                    label="Всего договоров",
                    delta=None
                )
            
            with col2:
                active_contracts = int(data.get('active_contracts', 0))
                total_contracts = int(data.get('total_contracts', 1))
                active_percentage = (active_contracts / total_contracts * 100) if total_contracts > 0 else 0
                
                create_metric_card(
                    value=f"{active_contracts:,}",
                    label="Активные договоры",
                    delta=f"{active_percentage:.1f}% от общего числа"
                )
            
            with col3:
                total_amount = data.get('total_amount', 0)
                avg_amount = data.get('average_amount', 0)
                
                create_metric_card(
                    value=f"{total_amount:,.0f} ₽",
                    label="Общая сумма",
                    delta=f"Средняя: {avg_amount:,.0f} ₽"
                )
            
            with col4:
                overdue_count = int(data.get('overdue_contracts', 0))
                overdue_rate = data.get('overdue_rate', 0)
                
                create_metric_card(
                    value=f"{overdue_count:,}",
                    label="Просроченные",
                    delta=f"{overdue_rate:.1f}% от активных"
                )
                
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки основных метрик: {e}")
    
    def _render_secondary_metrics(self, data: Dict[str, Any]) -> None:
        """Отрисовывает дополнительные метрики"""
        try:
            st.subheader("📈 Детальная аналитика")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                completed_contracts = int(data.get('completed_contracts', 0))
                total_contracts = int(data.get('total_contracts', 1))
                completion_rate = (completed_contracts / total_contracts * 100) if total_contracts > 0 else 0
                
                create_metric_card(
                    value=f"{completed_contracts:,}",
                    label="Завершенные",
                    delta=f"{completion_rate:.1f}% завершено"
                )
            
            with col2:
                # Приостановленные договоры (вычисляем как разность)
                active = int(data.get('active_contracts', 0))
                completed = int(data.get('completed_contracts', 0))
                overdue = int(data.get('overdue_contracts', 0))
                total = int(data.get('total_contracts', 0))
                
                suspended_count = max(0, total - active - completed - overdue)
                suspended_rate = (suspended_count / total * 100) if total > 0 else 0
                
                create_metric_card(
                    value=f"{suspended_count:,}",
                    label="Приостановленные",
                    delta=f"{suspended_rate:.1f}% приостановлено"
                )
            
            with col3:
                # Средний срок выполнения (заглушка)
                avg_duration = 45  # В реальности должно вычисляться из данных
                
                create_metric_card(
                    value=f"{avg_duration:.0f}",
                    label="Средний срок",
                    delta="дней выполнения"
                )
            
            with col4:
                # Эффективность (процент договоров без просрочек)
                overdue_rate = data.get('overdue_rate', 0)
                efficiency = 100 - overdue_rate if overdue_rate > 0 else 100
                
                create_metric_card(
                    value=f"{efficiency:.1f}%",
                    label="Эффективность",
                    delta="без просрочек"
                )
                
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки дополнительных метрик: {e}")
    
    def _render_fallback_metrics(self) -> None:
        """Отрисовывает fallback метрики при ошибке"""
        st.error("❌ Не удалось загрузить метрики")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Всего договоров", "—", "Нет данных")
        
        with col2:
            st.metric("Активные договоры", "—", "Нет данных")
        
        with col3:
            st.metric("Общая сумма", "—", "Нет данных")
        
        with col4:
            st.metric("Просроченные", "—", "Нет данных")
    
    def render_compact(self, data: Dict[str, Any]) -> None:
        """Отрисовывает компактные метрики"""
        try:
            # Создаем статистические карточки
            stats = [
                {
                    'value': f"{int(data.get('total_contracts', 0)):,}",
                    'label': 'Всего договоров',
                    'change': None
                },
                {
                    'value': f"{int(data.get('active_contracts', 0)):,}",
                    'label': 'Активные',
                    'change': None
                },
                {
                    'value': f"{data.get('total_amount', 0):,.0f} ₽",
                    'label': 'Общая сумма',
                    'change': None
                },
                {
                    'value': f"{data.get('overdue_rate', 0):.1f}%",
                    'label': 'Просрочка',
                    'change': None
                }
            ]
            
            stats_html = create_stats_grid(stats, columns=4)
            st.markdown(stats_html, unsafe_allow_html=True)
            
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки компактных метрик: {e}")
    
    def render_filtered_summary(self, original_data: Dict[str, Any], filtered_data: Dict[str, Any]) -> None:
        """Отрисовывает сравнение исходных и отфильтрованных данных"""
        try:
            st.markdown("### 🔍 Результаты фильтрации")
            
            col1, col2, col3, col4, col5 = st.columns(5)
            
            with col1:
                filtered_count = int(filtered_data.get('total_contracts', 0))
                original_count = int(original_data.get('total_contracts', 1))
                percentage = (filtered_count / original_count * 100) if original_count > 0 else 0
                
                st.markdown(f"""
                <div class="metric-card">
                    <div class="metric-label">Найдено</div>
                    <div class="metric-value">{filtered_count:,}</div>
                    <div class="metric-delta">{percentage:.1f}% от общего</div>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                filtered_sum = filtered_data.get('total_amount', 0)
                st.markdown(f"""
                <div class="metric-card">
                    <div class="metric-label">Общая сумма</div>
                    <div class="metric-value">{filtered_sum:,.0f}</div>
                    <div class="metric-delta">💰 рублей</div>
                </div>
                """, unsafe_allow_html=True)
            
            with col3:
                avg_filtered = filtered_data.get('average_amount', 0)
                st.markdown(f"""
                <div class="metric-card">
                    <div class="metric-label">Средняя сумма</div>
                    <div class="metric-value">{avg_filtered:,.0f}</div>
                    <div class="metric-delta">📊 рублей</div>
                </div>
                """, unsafe_allow_html=True)
            
            with col4:
                active_filtered = int(filtered_data.get('active_contracts', 0))
                st.markdown(f"""
                <div class="metric-card">
                    <div class="metric-label">Активные</div>
                    <div class="metric-value status-active">{active_filtered:,}</div>
                    <div class="metric-delta">✅ договоров</div>
                </div>
                """, unsafe_allow_html=True)
            
            with col5:
                overdue_filtered = int(filtered_data.get('overdue_contracts', 0))
                overdue_rate = filtered_data.get('overdue_rate', 0)
                st.markdown(f"""
                <div class="metric-card">
                    <div class="metric-label">Просроченные</div>
                    <div class="metric-value">{overdue_filtered:,}</div>
                    <div class="metric-delta">{overdue_rate:.1f}% просрочка</div>
                </div>
                """, unsafe_allow_html=True)
                
        except Exception as e:
            logger.error(f"❌ Ошибка отрисовки сравнения данных: {e}")
    
    def get_metrics_summary(self, data: Dict[str, Any]) -> str:
        """Возвращает текстовое резюме метрик"""
        try:
            total = int(data.get('total_contracts', 0))
            active = int(data.get('active_contracts', 0))
            overdue_rate = data.get('overdue_rate', 0)
            total_amount = data.get('total_amount', 0)
            
            return f"""
            📊 **Сводка:** {total:,} договоров на сумму {total_amount:,.0f} ₽
            ✅ **Активных:** {active:,} ({(active/total*100):.1f}%)
            ⚠️ **Просрочка:** {overdue_rate:.1f}%
            """
            
        except Exception as e:
            logger.error(f"❌ Ошибка создания резюме метрик: {e}")
            return "Не удалось создать резюме метрик"
