"""
Компоненты фильтров для дашборда
"""
import streamlit as st
from typing import Dict, Any


def render_status_filter() -> str:
    """Отрисовывает фильтр по статусу договора"""
    status_options = ['Все', 'active', 'completed', 'suspended', 'cancelled']
    status_labels = ['Все', 'Активные', 'Завершенные', 'Приостановленные', 'Отмененные']
    
    return st.selectbox(
        "📊 Статус договора:",
        options=status_options,
        format_func=lambda x: dict(zip(status_options, status_labels))[x],
        key="main_status_filter"
    )


def render_type_filter() -> str:
    """Отрисовывает фильтр по типу договора"""
    type_options = ['Все', 'construction', 'supply', 'services', 'maintenance']
    type_labels = ['Все', 'Строительство', 'Поставка', 'Услуги', 'Обслуживание']
    
    return st.selectbox(
        "🏗️ Тип договора:",
        options=type_options,
        format_func=lambda x: dict(zip(type_options, type_labels))[x],
        key="main_type_filter"
    )


def render_amount_filter() -> str:
    """Отрисовывает фильтр по сумме договора"""
    return st.selectbox(
        "💰 Сумма договора:",
        options=['Все', 'До 10 млн', '10-50 млн', 'Свыше 50 млн'],
        key="amount_filter"
    )


def render_deadline_filter() -> str:
    """Отрисовывает фильтр по срокам"""
    return st.selectbox(
        "⏰ По срокам:",
        options=['Все', 'Просроченные', 'Заканчиваются в течение месяца', 'Долгосрочные'],
        key="deadline_filter"
    )


def render_search_filter() -> str:
    """Отрисовывает поле поиска"""
    return st.text_input(
        "🔍 Поиск:",
        placeholder="Введите название или номер договора...",
        key="search_filter"
    )


def render_contractor_filter() -> str:
    """Отрисовывает фильтр по подрядчику"""
    return st.text_input(
        "🏢 Подрядчик:",
        placeholder="Название подрядчика...",
        key="contractor_filter"
    )


def render_sort_filter() -> str:
    """Отрисовывает фильтр сортировки"""
    sort_options = [
        'По дате создания', 
        'По сумме (убыв.)', 
        'По сумме (возр.)', 
        'По названию', 
        'По сроку окончания'
    ]
    return st.selectbox(
        "📈 Сортировка:",
        options=sort_options,
        key="sort_filter"
    )


def render_page_size_filter() -> int:
    """Отрисовывает фильтр количества записей на странице"""
    return st.selectbox(
        "📄 Записей на странице:",
        options=[10, 20, 50, 100],
        index=1,
        key="page_size_filter"
    )


def render_filter_buttons() -> Dict[str, bool]:
    """Отрисовывает кнопки действий фильтров"""
    col_btn1, col_btn2, col_btn3 = st.columns(3)

    with col_btn1:
        reset_filters = st.button("🔄 Сбросить фильтры")
    with col_btn2:
        export_data = st.button("📥 Экспорт данных")
    with col_btn3:
        st.info("🔄 Дашборд обновляется автоматически")

    return {
        'apply_filters': False,  # Больше не нужна кнопка применения
        'reset_filters': reset_filters,
        'export_data': export_data,
        'refresh_data': False  # Больше не нужна кнопка обновления
    }


def handle_filter_actions(buttons: Dict[str, bool]) -> None:
    """Обрабатывает действия кнопок фильтров"""
    if buttons['reset_filters']:
        st.cache_data.clear()
        st.rerun()


def render_advanced_filters() -> Dict[str, Any]:
    """Отрисовывает расширенные фильтры в верхней части страницы"""
    st.markdown("### 🔧 Фильтры и настройки")

    # Создаем контейнер для фильтров
    with st.container():
        # Первая строка фильтров
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            status_filter = render_status_filter()

        with col2:
            type_filter = render_type_filter()

        with col3:
            amount_filter = render_amount_filter()

        with col4:
            deadline_filter = render_deadline_filter()

        # Вторая строка фильтров
        col5, col6, col7, col8 = st.columns(4)

        with col5:
            search_query = render_search_filter()

        with col6:
            contractor_filter = render_contractor_filter()

        with col7:
            sort_filter = render_sort_filter()

        with col8:
            page_size = render_page_size_filter()

        # Кнопки действий
        st.markdown('<div class="filter-buttons">', unsafe_allow_html=True)
        buttons = render_filter_buttons()
        st.markdown('</div>', unsafe_allow_html=True)

        # Обработка кнопок
        handle_filter_actions(buttons)

    return {
        'status_filter': status_filter,
        'type_filter': type_filter,
        'amount_filter': amount_filter,
        'deadline_filter': deadline_filter,
        'search_query': search_query,
        'contractor_filter': contractor_filter,
        'sort_filter': sort_filter,
        'page_size': page_size,
        'apply_filters': buttons['apply_filters'],
        'export_data': buttons['export_data']
    }
