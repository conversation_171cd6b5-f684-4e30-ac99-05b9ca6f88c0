"""
Компонент боковой панели дашборда
"""
import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Tuple

from data.loader import data_loader


def render_sidebar() -> Tuple[date, date]:
    """Отрисовывает расширенную боковую панель"""
    st.sidebar.markdown("## 🎛️ Панель управления")

    # Информационная панель
    st.sidebar.markdown("### 📊 Системная информация")

    # Загружаем основную статистику для боковой панели
    summary = data_loader.get_dashboard_summary()
    if summary:
        st.sidebar.markdown(f"""
        **Статистика системы:**
        - 📋 Всего договоров: **{int(summary.get('total_contracts', 0)):,}**
        - ✅ Активных: **{int(summary.get('active_contracts', 0)):,}**
        - ⚠️ Просроченных: **{int(summary.get('overdue_contracts', 0)):,}**
        - 💰 Общая сумма: **{summary.get('total_amount', 0):,.0f} ₽**
        """)

    st.sidebar.markdown("---")

    # Выбор периода
    st.sidebar.markdown("### 📅 Настройки периода")
    date_range_options = data_loader.get_date_range_options()

    selected_range = st.sidebar.selectbox(
        "Выберите период:",
        options=[option[0] for option in date_range_options],
        index=2  # По умолчанию "Последний год"
    )

    # Получаем количество месяцев для выбранного периода
    months_back = next(
        option[1] for option in date_range_options
        if option[0] == selected_range
    )

    # Вычисляем даты
    end_date = date.today()
    start_date = end_date - timedelta(days=months_back * 30)

    # Возможность выбрать кастомные даты
    st.sidebar.markdown("#### 🗓️ Кастомный период")
    custom_start = st.sidebar.date_input(
        "Дата начала:",
        value=start_date,
        max_value=end_date
    )
    custom_end = st.sidebar.date_input(
        "Дата окончания:",
        value=end_date,
        min_value=custom_start,
        max_value=date.today()
    )

    st.sidebar.markdown("---")

    # Быстрые действия
    st.sidebar.markdown("### ⚡ Быстрые действия")

    col1, col2 = st.sidebar.columns(2)
    with col1:
        if st.button("🔄 Обновить", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

    with col2:
        if st.button("📊 Сброс", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

    # Экспорт всех данных
    if st.sidebar.button("📥 Экспорт всех данных", use_container_width=True):
        contracts_df = data_loader.load_contracts_overview()
        if contracts_df is not None and not contracts_df.empty:
            csv = contracts_df.to_csv(index=False, encoding='utf-8-sig')
            st.sidebar.download_button(
                label="💾 Скачать CSV",
                data=csv,
                file_name=f"all_contracts_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                use_container_width=True
            )

    st.sidebar.markdown("---")

    # Справочная информация
    st.sidebar.markdown("### ℹ️ Справка")
    with st.sidebar.expander("📖 Как пользоваться"):
        st.markdown("""
        **Основные функции:**
        1. 📊 Просмотр общей статистики
        2. 🔍 Фильтрация договоров
        3. 📈 Анализ данных
        4. 📥 Экспорт результатов

        **Статусы договоров:**
        - ✅ **Активные** - в процессе выполнения
        - ✓ **Завершенные** - успешно завершены
        - ⏸️ **Приостановленные** - временно остановлены
        - ❌ **Отмененные** - отменены
        - ⚠️ **Просроченные** - превысили срок
        """)

    # Информация о последнем обновлении
    st.sidebar.markdown("---")
    st.sidebar.markdown(f"""
    **Последнее обновление:**
    {datetime.now().strftime('%d.%m.%Y %H:%M')}
    """)

    return custom_start, custom_end
