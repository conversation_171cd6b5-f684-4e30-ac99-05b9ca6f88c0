"""
Главное приложение дашборда освоения договоров
Рефакторированная версия с модульной архитектурой
"""
import streamlit as st
import pandas as pd
import logging
from typing import Optional

# Импорты конфигурации
try:
    from config.app_config import app_config
    APP_CONFIG = {
        'title': app_config.ui.title,
        'icon': '📊',
        'layout': 'wide',
        'initial_sidebar_state': 'expanded'
    }
    DASHBOARD_CONFIG = {
        'refresh_interval': 300,
        'max_records_per_page': 50,
        'default_theme': 'light'
    }
except ImportError:
    # Fallback конфигурация
    APP_CONFIG = {
        'title': '📊 Дашборд освоения договоров',
        'icon': '📊',
        'layout': 'wide',
        'initial_sidebar_state': 'expanded'
    }
    DASHBOARD_CONFIG = {
        'refresh_interval': 300,
        'max_records_per_page': 50,
        'default_theme': 'light'
    }

# Импорты модульной системы стилей
from app.styles.integration import init_styles

# Импорты основных модулей приложения
from app.core.application import DashboardApplication
from app.components.theme_switcher import theme_switcher
from app.components.active_filters_panel import ActiveFiltersPanel
from data.database import db_connection

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def configure_streamlit_page() -> None:
    """Конфигурирует страницу Streamlit"""
    st.set_page_config(
        page_title=APP_CONFIG['title'],
        page_icon=APP_CONFIG['icon'],
        layout=APP_CONFIG['layout'],
        initial_sidebar_state=APP_CONFIG['initial_sidebar_state']
    )
    
    # Инициализируем модульную систему стилей
    init_styles()
    
    logger.info("✅ Страница Streamlit сконфигурирована")

def check_database_connection() -> bool:
    """Проверяет подключение к базе данных"""
    try:
        if not db_connection.test_connection():
            st.warning("⚠️ База данных недоступна - работаем в демо режиме")
            st.info("Для полной функциональности настройте подключение к базе данных в файле .env")

            # Устанавливаем флаг демо режима
            st.session_state.demo_mode = True
            logger.info("🔄 Запуск в демо режиме без подключения к БД")
            return True

        logger.info("✅ Подключение к базе данных успешно")
        st.session_state.demo_mode = False
        return True

    except Exception as e:
        logger.error(f"❌ Ошибка подключения к БД: {e}")
        st.warning("⚠️ База данных недоступна - работаем в демо режиме")
        st.info("Для полной функциональности настройте подключение к базе данных")

        # Устанавливаем флаг демо режима
        st.session_state.demo_mode = True
        logger.info("🔄 Запуск в демо режиме из-за ошибки подключения к БД")
        return True


def initialize_session_state() -> None:
    """Инициализирует состояние сессии"""
    if 'app_initialized' not in st.session_state:
        st.session_state.app_initialized = False

    if 'theme' not in st.session_state:
        st.session_state.theme = 'light'

    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'dashboard'

    logger.debug("Session state инициализирован")


def render_error_page(error_message: str, details: Optional[str] = None) -> None:
    """Отрисовывает страницу ошибки используя нативные Streamlit компоненты"""
    st.error(f"**Произошла ошибка**: {error_message}")

    if details:
        with st.expander("Подробности ошибки"):
            st.code(details)

    st.info("""
    **Что делать?**

    1. Обновите страницу (F5)
    2. Проверьте подключение к интернету
    3. Обратитесь к администратору системы
    """)


def render_loading_page() -> None:
    """Отрисовывает страницу загрузки используя нативные Streamlit компоненты"""
    with st.spinner("Загрузка дашборда..."):
        st.markdown("### 📊 Подготавливаем данные для отображения")
        st.progress(0.5)  # Show progress indicator


def inject_global_fixed_panel_styles():
    """Инжектирует глобальные CSS стили для фиксированной панели"""
    st.markdown("""
    <style>
    /* Глобальные стили для фиксированной панели активных фильтров */
    .active-filters-panel {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 999999 !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-bottom: 2px solid #dee2e6 !important;
        padding: 12px 16px !important;
        margin: 0 !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        backdrop-filter: blur(15px) !important;
        -webkit-backdrop-filter: blur(15px) !important;
        display: block !important;
        visibility: visible !important;
    }

    /* Отступ для основного контента Streamlit */
    .stApp > div[data-testid="stAppViewContainer"] > .main > .block-container {
        padding-top: 80px !important;
    }

    .stApp > div[data-testid="stAppViewContainer"] > .main {
        padding-top: 80px !important;
    }

    /* Дополнительные селекторы для отступа */
    div[data-testid="stAppViewContainer"] .main .block-container {
        padding-top: 80px !important;
    }

    /* Убираем верхний отступ у заголовка */
    .stApp h1:first-child {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    /* Обеспечиваем отступ для всего контента */
    .main-content-with-fixed-panel {
        padding-top: 80px !important;
        margin-top: 0 !important;
    }
    </style>

    <script>
    // Принудительно применяем отступ для основного контента
    (function() {
        function applyMainContentPadding() {
            // Находим основной контейнер Streamlit
            const mainContainer = document.querySelector('.stApp > div[data-testid="stAppViewContainer"] > .main > .block-container');
            const main = document.querySelector('.stApp > div[data-testid="stAppViewContainer"] > .main');

            if (mainContainer) {
                mainContainer.style.setProperty('padding-top', '80px', 'important');
                console.log('Applied padding to main container');
            }

            if (main) {
                main.style.setProperty('padding-top', '80px', 'important');
                console.log('Applied padding to main');
            }

            // Также применяем к любым элементам с классом main-content-with-fixed-panel
            const contentElements = document.querySelectorAll('.main-content-with-fixed-panel');
            contentElements.forEach(element => {
                element.style.setProperty('padding-top', '80px', 'important');
                element.style.setProperty('margin-top', '0', 'important');
            });
        }

        // Применяем отступы сразу и через задержки
        applyMainContentPadding();
        setTimeout(applyMainContentPadding, 100);
        setTimeout(applyMainContentPadding, 500);
        setTimeout(applyMainContentPadding, 1000);

        // Также применяем при изменениях DOM
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    setTimeout(applyMainContentPadding, 10);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    })();
    </script>
    """, unsafe_allow_html=True)


def render_top_active_filters_panel(app: DashboardApplication) -> bool:
    """Отрисовывает панель активных фильтров в самом верху страницы"""
    try:
        # Инжектируем глобальные стили
        inject_global_fixed_panel_styles()

        # Получаем текущие фильтры и данные
        current_filters = app.get_current_filters()
        contracts_data = app.cached_data.get('contracts', pd.DataFrame())

        logger.info(f"🔍 ПАНЕЛЬ АКТИВНЫХ ФИЛЬТРОВ: Текущие фильтры: {current_filters}")

        # Получаем доступные статусы и типы
        available_statuses = sorted(list(contracts_data['status'].unique())) if not contracts_data.empty and 'status' in contracts_data.columns else ['active', 'completed', 'suspended', 'cancelled']
        available_types = sorted(list(contracts_data['contract_type'].unique())) if not contracts_data.empty and 'contract_type' in contracts_data.columns else ['construction', 'services', 'supply']

        logger.info(f"🔍 ПАНЕЛЬ АКТИВНЫХ ФИЛЬТРОВ: Доступные статусы: {available_statuses}")
        logger.info(f"🔍 ПАНЕЛЬ АКТИВНЫХ ФИЛЬТРОВ: Доступные типы: {available_types}")

        # Создаем панель активных фильтров
        active_filters_panel = ActiveFiltersPanel()

        # Отрисовываем панель и получаем результат нажатия кнопки сброса
        reset_clicked = active_filters_panel.render(current_filters, available_statuses, available_types)

        logger.info(f"🔍 ПАНЕЛЬ АКТИВНЫХ ФИЛЬТРОВ: Панель отрисована, reset_clicked: {reset_clicked}")

        # Применяем отступ для основного контента
        active_filters_panel.apply_main_content_padding()

        return reset_clicked

    except Exception as e:
        logger.error(f"❌ Ошибка отрисовки панели активных фильтров: {e}")
        return False


def main() -> None:
    """Главная функция приложения с централизованными фильтрами"""
    try:
        # 1. Конфигурируем Streamlit
        configure_streamlit_page()

        # 2. Инициализируем состояние сессии
        initialize_session_state()

        # 3. Применяем текущую тему
        current_theme = st.session_state.get('theme', 'light')
        theme_switcher.apply_theme_styles(current_theme)

        # 3.1. Принудительно обновляем тему
        theme_switcher.force_theme_refresh()

        # 4. Отображаем переключатель темы в боковой панели (минимально)
        theme_switcher.render_sidebar_toggle()

        # 5. Проверяем подключение к БД (всегда возвращает True, может установить demo_mode)
        check_database_connection()

        # 6. Показываем загрузку при первом запуске
        if not st.session_state.app_initialized:
            render_loading_page()

            # Инициализируем приложение
            try:
                app = DashboardApplication()
                st.session_state.dashboard_app = app
                st.session_state.app_initialized = True

                # Перезагружаем страницу для отображения дашборда
                st.rerun()

            except Exception as e:
                logger.error(f"❌ Ошибка инициализации приложения: {e}")
                render_error_page(
                    "Не удалось инициализировать приложение",
                    str(e)
                )
                return

        # 7. Запускаем основное приложение с новой структурой
        if 'dashboard_app' in st.session_state:
            app = st.session_state.dashboard_app

            # 7.1. СНАЧАЛА отрисовываем панель активных фильтров в самом верху
            reset_clicked = render_top_active_filters_panel(app)

            # 7.2. Если была нажата кнопка сброса, сбрасываем фильтры
            if reset_clicked:
                logger.info(f"🔄 СБРОС ФИЛЬТРОВ: Начинаем сброс фильтров... (reset_clicked: {reset_clicked})")

                # Сбрасываем все фильтры в session_state
                filter_keys = [key for key in st.session_state.keys() if key.startswith('top_')]
                logger.info(f"🔄 СБРОС ФИЛЬТРОВ: Найдены ключи для сброса: {filter_keys}")

                for key in filter_keys:
                    logger.info(f"🔄 СБРОС ФИЛЬТРОВ: Удаляем ключ: {key}")
                    del st.session_state[key]

                # Также сбрасываем чекбокс фильтра по дате
                if 'enable_date_filter' in st.session_state:
                    logger.info("🔄 СБРОС ФИЛЬТРОВ: Удаляем enable_date_filter")
                    del st.session_state['enable_date_filter']

                # Сбрасываем дополнительные ключи фильтров
                additional_keys = ['amount_range_filter', 'date_range_filter', 'search_query', 'js_reset_filters_flag']
                for key in additional_keys:
                    if key in st.session_state:
                        logger.info(f"🔄 СБРОС ФИЛЬТРОВ: Удаляем дополнительный ключ: {key}")
                        del st.session_state[key]

                # Очищаем query parameters (если есть)
                if hasattr(st, 'query_params') and st.query_params:
                    logger.info("🔄 СБРОС ФИЛЬТРОВ: Очищаем query parameters")
                    st.query_params.clear()

                # Сбрасываем JavaScript флаг
                st.markdown("""
                <script>
                window.resetFiltersClicked = false;
                console.log('🔄 MAIN: JavaScript reset flag cleared');
                </script>
                """, unsafe_allow_html=True)

                logger.info("✅ СБРОС ФИЛЬТРОВ: Все фильтры сброшены, перезагружаем страницу")
                st.rerun()  # Перезагружаем страницу для применения сброса

            # 7.3. Запускаем основное приложение
            app.run_with_top_filters()  # Используем новый метод с фильтрами сверху
        else:
            logger.error("❌ Приложение не инициализировано")
            render_error_page("Приложение не инициализировано")

    except Exception as e:
        logger.error(f"❌ Критическая ошибка в main(): {e}")
        render_error_page(
            "Критическая ошибка приложения",
            str(e)
        )


if __name__ == "__main__":
    main()
