"""
Тестирование исправленной функции сброса фильтров
"""
import streamlit as st
import pandas as pd
import logging
from app.components.active_filters_panel import ActiveFiltersPanel

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

st.set_page_config(
    page_title="Тест сброса фильтров",
    page_icon="🔄",
    layout="wide"
)

st.title("🔄 Тестирование сброса фильтров")

# Создаем тестовые фильтры в session_state
st.markdown("## 🎯 Установка тестовых фильтров")

col1, col2, col3 = st.columns(3)

with col1:
    # Фильтры с префиксом dashboard_
    st.session_state['dashboard_status_filter'] = st.selectbox(
        "Dashboard Status", 
        ['Все', 'active', 'completed'], 
        key='test_dashboard_status'
    )
    
with col2:
    # Фильтры с префиксом top_
    st.session_state['top_type_filter'] = st.multiselect(
        "Top Type Filter", 
        ['construction', 'services', 'supply'],
        default=['construction'],
        key='test_top_type'
    )

with col3:
    # Другие фильтры
    st.session_state['main_amount_filter'] = st.selectbox(
        "Amount Filter",
        ['Все суммы', 'До 10 млн', '10-50 млн'],
        key='test_amount'
    )

# Показываем текущие фильтры в session_state
st.markdown("## 📊 Текущие фильтры в Session State")
filter_keys = {k: v for k, v in st.session_state.items() if 'filter' in k.lower()}
st.json(filter_keys)

# Создаем тестовые активные фильтры для панели
st.markdown("## 🔍 Панель активных фильтров")

# Формируем фильтры в нужном формате
active_filters = {
    'status': ['active'] if st.session_state.get('dashboard_status_filter') == 'active' else [],
    'contract_type': st.session_state.get('top_type_filter', []),
    'amount_filter': st.session_state.get('main_amount_filter', 'Все суммы'),
    'start_date': None,
    'end_date': None
}

available_statuses = ['active', 'completed', 'suspended']
available_types = ['construction', 'services', 'supply']

# Создаем и отрисовываем панель активных фильтров
active_filters_panel = ActiveFiltersPanel()
reset_clicked = active_filters_panel.render(active_filters, available_statuses, available_types)

# Обрабатываем сброс фильтров
if reset_clicked:
    st.success("🎉 Кнопка сброса была нажата!")
    
    # Имитируем функцию сброса из дашборда
    try:
        # Очищаем все ключи фильтров из session_state
        filter_prefixes = ['dashboard_', 'top_', 'main_', 'filter_', 'enable_date_filter']
        filter_keys_to_remove = []
        
        for key in st.session_state.keys():
            # Проверяем префиксы
            if any(key.startswith(prefix) for prefix in filter_prefixes):
                filter_keys_to_remove.append(key)
            # Также проверяем ключи, содержащие 'filter' в названии
            elif 'filter' in key.lower():
                filter_keys_to_remove.append(key)
        
        # Удаляем найденные ключи фильтров
        for key in filter_keys_to_remove:
            if key in st.session_state:
                del st.session_state[key]
                logger.info(f"🗑️ Удален ключ фильтра: {key}")

        st.success(f"✅ Фильтры сброшены! Удалено ключей: {len(filter_keys_to_remove)}")
        if filter_keys_to_remove:
            st.info(f"🔍 Удаленные ключи: {filter_keys_to_remove}")
        
        st.rerun()
        
    except Exception as e:
        st.error(f"❌ Ошибка сброса фильтров: {e}")

# Кнопка для ручного сброса (для сравнения)
st.markdown("## 🔧 Ручной сброс для сравнения")
if st.button("🗑️ Ручной сброс всех фильтров"):
    # Очищаем все ключи с 'filter' в названии
    keys_to_remove = [k for k in st.session_state.keys() if 'filter' in k.lower()]
    for key in keys_to_remove:
        if key in st.session_state:
            del st.session_state[key]
    st.success(f"Ручной сброс выполнен! Удалено: {len(keys_to_remove)} ключей")
    st.rerun()

# Отладочная информация
st.markdown("## 🔧 Отладочная информация")
st.write("**Все ключи в Session State:**")
all_keys = list(st.session_state.keys())
st.write(f"Всего ключей: {len(all_keys)}")
st.write(all_keys)

st.markdown("---")
st.markdown("### 📝 Инструкции по тестированию:")
st.markdown("""
1. **Установите фильтры** используя селекторы выше
2. **Проверьте**, что панель активных фильтров появилась
3. **Нажмите кнопку "Сбросить все фильтры"** в панели
4. **Проверьте**, что фильтры сброшены и панель исчезла
5. **Посмотрите в консоль браузера** (F12) для отладочных сообщений
""")
